#!/bin/bash

# 测试环境启动脚本
echo "🧪 启动测试环境..."

# 设置环境变量
export SPRING_PROFILES_ACTIVE=test

# 设置JVM参数
export JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 启动应用
echo "📊 环境: 测试环境 (test)"
echo "🔗 数据库: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com:3306"
echo "👤 用户: healthcar_test"
echo "🌐 端口: 9999"
echo "📝 日志: /var/log/db-copy-data/test-application.log"

mvn spring-boot:run -Dspring-boot.run.profiles=test
