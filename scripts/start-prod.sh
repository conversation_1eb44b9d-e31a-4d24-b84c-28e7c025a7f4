#!/bin/bash

# 生产环境启动脚本
echo "🚀 启动生产环境..."

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod

# 设置JVM参数
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/log/db-copy-data/"

# 启动应用
echo "📊 环境: 生产环境 (prod)"
echo "🔗 数据库: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com:3306"
echo "👤 用户: healthcar_test"
echo "🌐 端口: 8080"
echo "📝 日志: /var/log/db-copy-data/application.log"

mvn spring-boot:run -Dspring-boot.run.profiles=prod
