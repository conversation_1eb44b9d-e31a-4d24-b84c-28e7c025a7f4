#!/bin/bash

# 应用AI提示词字段迁移脚本
echo "🔧 应用AI提示词字段迁移..."

# 设置数据库变量（使用开发环境配置）
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="db_config_management"
DB_USER="root"
DB_PASS="liuyang"

# SQL文件路径
SQL_FILE="src/main/resources/sql/add_ai_prompt_description.sql"

echo "📊 数据库信息:"
echo "  主机: $DB_HOST"
echo "  数据库: $DB_NAME"
echo "  用户: $DB_USER"

# 检查SQL文件是否存在
if [ ! -f "$SQL_FILE" ]; then
    echo "❌ SQL文件不存在: $SQL_FILE"
    exit 1
fi

# 检查MySQL连接
echo "🔍 检查数据库连接..."
docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "❌ 无法连接到数据库"
    exit 1
fi

echo "✅ 数据库连接成功"

# 检查表是否存在
echo "🔍 检查table_mapping_records表..."
TABLE_EXISTS=$(docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW TABLES LIKE 'table_mapping_records';" | wc -l)

if [ $TABLE_EXISTS -lt 2 ]; then
    echo "❌ table_mapping_records表不存在"
    exit 1
fi

echo "✅ table_mapping_records表存在"

# 检查字段是否已存在
echo "🔍 检查ai_prompt_description字段..."
FIELD_EXISTS=$(docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW COLUMNS FROM table_mapping_records LIKE 'ai_prompt_description';" | wc -l)

if [ $FIELD_EXISTS -gt 1 ]; then
    echo "⚠️ ai_prompt_description字段已存在，跳过创建"
else
    echo "🚀 执行字段添加..."
    docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$SQL_FILE"

    if [ $? -eq 0 ]; then
        echo "✅ 字段添加成功"
    else
        echo "❌ 字段添加失败"
        exit 1
    fi
fi

# 验证字段添加结果
echo "🔍 验证字段添加结果..."
docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
    SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'table_mapping_records'
        AND COLUMN_NAME = 'ai_prompt_description'
        AND TABLE_SCHEMA = '$DB_NAME';
"

# 显示表结构
echo ""
echo "📋 当前表结构:"
docker exec -i $(docker ps | grep mysql | awk '{print $1}') mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "DESCRIBE table_mapping_records;"

echo ""
echo "🎉 AI提示词字段迁移完成！"
echo ""
echo "📝 下一步操作："
echo "  1. 重新编译应用: mvn clean package -DskipTests"
echo "  2. 重启应用服务"
echo "  3. 测试数据迁移功能，验证AI提示词是否正确保存"
echo ""
echo "💡 功能说明："
echo "  - 新增的ai_prompt_description字段将保存AI优化表名时使用的提示词"
echo "  - 该提示词将作为MySQL表的COMMENT使用"
echo "  - 可以在迁移记录中查看完整的AI优化过程"
