# DB_CopyData - 智能数据库迁移系统

[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.5.3-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

## 📋 项目概述

DB_CopyData 是一个基于 Spring Boot 的智能数据库迁移系统，专门用于跨数据库平台的数据迁移和表结构优化。系统集成了阿里云千问AI模型，提供智能字段命名、表结构优化和数据类型转换等功能，支持 PostgreSQL 到 MySQL 的无缝迁移。

### 🎯 核心特性

- **🤖 AI智能优化**: 集成阿里云千问AI，智能生成字段名称和注释
- **🔄 跨数据库迁移**: 支持 PostgreSQL → MySQL 的完整迁移方案
- **🏗️ 表结构重构**: 支持双主键设计和字段映射优化
- **📊 可视化界面**: 提供友好的Web界面进行操作管理
- **⚡ 批量处理**: 支持大数据量的批量迁移处理
- **📈 迁移历史**: 完整的迁移记录和回滚支持
- **🔧 配置管理**: 灵活的数据库连接配置管理

## 🛠️ 技术栈

### 后端技术
- **Java 21** - 最新LTS版本，提供优秀的性能和特性
- **Spring Boot 3.5.3** - 企业级应用框架
- **Spring AI Alibaba** - 阿里云AI集成框架
- **MyBatis 3.0.4** - 持久层框架
- **HikariCP** - 高性能数据库连接池

### 数据库支持
- **PostgreSQL** - 源数据库支持
- **MySQL** - 目标数据库支持
- **跨数据库类型映射** - 智能数据类型转换

### 前端技术
- **Vue.js 3** - 现代化前端框架
- **Element Plus** - 企业级UI组件库
- **JavaScript ES6+** - 现代JavaScript特性

### AI集成
- **阿里云千问 (DashScope)** - 智能字段命名和优化
- **Spring AI** - AI服务集成框架
- **智能降级机制** - AI服务不可用时的备用方案

## 🏗️ 系统架构

```
db_copydata/
├── src/main/java/com/liuyang/
│   ├── config/                 # 配置类
│   │   ├── AiConfigurationChecker.java
│   │   ├── DatabaseConfigForMybatis.java
│   │   ├── SimplifiedAiConfiguration.java
│   │   └── StartupRunner.java
│   ├── controller/             # 控制器层
│   │   ├── AiFieldNamingController.java
│   │   ├── CopyController.java
│   │   ├── DatabaseController.java
│   │   ├── FieldMappingController.java
│   │   └── TableController.java
│   ├── service/               # 服务层
│   │   ├── impl/              # 服务实现
│   │   ├── AiFieldNamingService.java
│   │   ├── CrossDatabaseTableCopyService.java
│   │   ├── DatabaseService.java
│   │   └── TableService.java
│   ├── dto/                   # 数据传输对象
│   │   ├── CopyTableRequest.java
│   │   ├── DatabaseInfo.java
│   │   ├── FieldMapping.java
│   │   └── TableStructure.java
│   ├── entity/                # 实体类
│   │   ├── DatabaseConfigEntity.java
│   │   ├── MigrationRecord.java
│   │   └── FieldMappingRecord.java
│   ├── mapper/                # MyBatis映射器
│   │   ├── DatabaseConfigMapper.java
│   │   └── MigrationRecordMapper.java
│   └── util/                  # 工具类
│       ├── SnakeCaseConverter.java
│       └── SnowflakeIdGenerator.java
├── src/main/resources/
│   ├── static/                # 静态资源
│   │   ├── index.html         # 主页面
│   │   ├── database-config.html
│   │   ├── migration.html
│   │   └── app.js
│   ├── application.yml        # 主配置文件
│   ├── application-dev.yml    # 开发环境配置
│   └── application-prod.yml   # 生产环境配置
└── logs/                      # 日志文件
    └── db-copy-data-dev.log
```

## 🚀 核心功能详解

### 1. 智能数据库迁移
- **跨平台迁移**: PostgreSQL → MySQL 完整迁移方案
- **数据类型映射**: 自动处理不同数据库间的类型差异
- **约束转换**: 主键、外键、索引等约束的智能转换
- **批量数据复制**: 支持大表的分批次数据迁移

### 2. AI智能字段优化
- **智能命名**: 使用AI分析中文字段名，生成标准英文字段名
- **Snake Case规范**: 自动转换为snake_case命名规范
- **字段注释生成**: AI生成详细的字段注释说明
- **业务语义理解**: 基于业务上下文的智能分析

### 3. 表结构重构
- **双主键设计**: 支持业务ID + 自增ID的双主键架构
- **字段映射配置**: 灵活的源字段到目标字段映射
- **索引优化**: 智能索引创建和优化建议
- **数据完整性**: 确保迁移过程中数据的完整性

### 4. 配置管理系统
- **多环境支持**: dev/test/prod环境配置管理
- **连接池管理**: HikariCP连接池优化配置
- **安全存储**: 数据库密码加密存储
- **健康检查**: 数据库连接状态监控

### 5. 可视化操作界面
- **数据库管理**: 可视化数据库连接配置
- **表结构预览**: 直观的表结构对比展示
- **迁移进度**: 实时迁移进度和状态监控
- **历史记录**: 完整的操作历史和日志查看

## 📦 环境要求

### 系统要求
- **Java**: JDK 21 或更高版本
- **Maven**: 3.6+
- **内存**: 最小 2GB RAM，推荐 4GB+
- **磁盘**: 至少 1GB 可用空间

### 数据库要求
- **PostgreSQL**: 9.6+ (源数据库)
- **MySQL**: 5.7+ 或 8.0+ (目标数据库)
- **网络**: 确保应用服务器能访问数据库服务器

### AI服务要求
- **阿里云账号**: 需要开通DashScope服务
- **API密钥**: 获取千问模型的API Key
- **网络**: 确保能访问阿里云AI服务

## 🔧 安装和配置

### 1. 克隆项目
```bash
git clone http://gltb.dev.necloud.com.cn/health-car-document/db_copydata.git
cd db_copydata
```

### 2. 配置数据库
编辑 `src/main/resources/application-dev.yml`:

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
    driver-class-name: org.postgresql.Driver

app:
  database:
    mysql:
      host: localhost
      port: 3306
      username: root
      password: your_mysql_password
```

### 3. 配置AI服务
设置阿里云千问API密钥:

```yaml
spring:
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY:your_api_key_here}
```

或通过环境变量设置:
```bash
export AI_DASHSCOPE_API_KEY=your_api_key_here
```

### 4. 编译和运行
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/DB_CopyData-0.0.1-SNAPSHOT.jar
```

### 5. 访问应用
应用启动后，访问: http://localhost:8081

## 📖 使用指南

### 1. 数据库配置管理

#### 添加数据库配置
1. 访问 "数据库配置" 页面
2. 点击 "添加配置" 按钮
3. 填写数据库连接信息:
   - 配置名称: 给配置起个名字
   - 数据库类型: PostgreSQL/MySQL
   - 主机地址: 数据库服务器地址
   - 端口: 数据库端口
   - 用户名/密码: 数据库认证信息
4. 点击 "测试连接" 验证配置
5. 保存配置

#### 配置示例
```json
{
  "configName": "生产PostgreSQL",
  "databaseType": "POSTGRESQL",
  "host": "*************",
  "port": 5432,
  "username": "postgres",
  "password": "your_password",
  "defaultDatabase": "production_db",
  "environment": "prod"
}
```

### 2. 表结构迁移

#### 基础迁移流程
1. 选择源数据库和目标数据库
2. 选择要迁移的表
3. 配置字段映射关系
4. 设置迁移选项:
   - 是否复制数据
   - 是否使用双主键设计
   - 是否创建索引
5. 执行迁移

#### AI智能字段映射
系统会自动调用AI服务分析字段名称:
```
源字段: 用户姓名 → 目标字段: user_name
源字段: 创建时间 → 目标字段: created_time
源字段: 是否激活 → 目标字段: is_active
```

### 3. 高级功能使用

#### 双主键设计
启用双主键设计后，系统会:
- 添加自增主键字段 `id`
- 保留原业务主键作为唯一约束
- 优化查询性能和数据一致性

#### 批量数据处理
对于大表迁移，系统支持:
- 分批次数据复制 (默认1000条/批)
- 进度实时监控
- 失败自动重试
- 事务回滚保护

### 4. 监控和日志

#### 查看迁移历史
- 访问 "迁移历史" 页面
- 查看所有迁移记录
- 包含迁移状态、耗时、数据量等信息

#### 日志文件位置
```
logs/
├── db-copy-data-dev.log      # 当前日志
└── db-copy-data-dev.log.*.gz # 历史日志(压缩)
```

## 🔌 API接口文档

### 数据库管理接口

#### 获取数据库列表
```http
GET /api/databases
```

响应示例:
```json
{
  "success": true,
  "data": [
    {
      "name": "test_db",
      "databaseType": "POSTGRESQL",
      "accessible": true
    }
  ]
}
```

#### 测试数据库连接
```http
POST /api/database/test-connection
Content-Type: application/json

{
  "databaseType": "POSTGRESQL",
  "host": "localhost",
  "port": 5432,
  "username": "postgres",
  "password": "password"
}
```

### 表操作接口

#### 获取表列表
```http
GET /api/tables?database={database_name}
```

#### 获取表结构
```http
GET /api/tables/{table_name}/structure?database={database_name}
```

#### 复制表
```http
POST /api/copy/table
Content-Type: application/json

{
  "sourceDatabase": "source_db",
  "targetDatabase": "target_db",
  "tableName": "users",
  "targetTableName": "users_new",
  "copyData": true,
  "useDualPrimaryKey": true
}
```

### AI服务接口

#### 生成字段映射建议
```http
POST /api/ai/field-mapping/suggestions
Content-Type: application/json

{
  "sourceTableName": "用户表",
  "targetTableName": "users",
  "businessContext": "用户管理系统",
  "sourceFields": ["用户ID", "用户姓名", "创建时间"]
}
```

#### AI字段命名优化
```http
POST /api/ai/field-naming/optimize
Content-Type: application/json

{
  "originalFieldName": "用户姓名",
  "fieldType": "VARCHAR",
  "businessContext": "用户管理"
}
```

## 🛠️ 开发指南

### 开发环境搭建

#### 1. 开发工具推荐
- **IDE**: IntelliJ IDEA 2023.3+
- **JDK**: OpenJDK 21 或 Oracle JDK 21
- **Maven**: 3.9+
- **Git**: 2.30+

#### 2. 导入项目
```bash
# 克隆项目
git clone http://gltb.dev.necloud.com.cn/health-car-document/db_copydata.git

# 导入到IDE
# File -> Open -> 选择项目目录
```

#### 3. 配置开发环境
```bash
# 设置环境变量
export JAVA_HOME=/path/to/jdk21
export MAVEN_HOME=/path/to/maven
export AI_DASHSCOPE_API_KEY=your_api_key

# 安装依赖
mvn clean install
```

### 代码结构说明

#### 分层架构
```
Controller Layer (控制层)
    ↓
Service Layer (服务层)
    ↓
Mapper Layer (数据访问层)
    ↓
Database (数据库层)
```

#### 核心组件

**1. 数据库服务 (DatabaseService)**
- 管理数据库连接
- 提供数据库操作基础功能
- 支持多数据源动态切换

**2. AI服务 (AiFieldNamingService)**
- 集成阿里云千问AI
- 提供智能字段命名
- 支持降级机制

**3. 迁移服务 (CrossDatabaseTableCopyService)**
- 跨数据库表复制
- 数据类型映射
- 批量数据处理

**4. 配置管理 (DatabaseConfigManagementService)**
- 数据库配置CRUD
- 配置验证和测试
- 环境隔离

### 添加新功能

#### 1. 添加新的数据库类型支持
```java
// 1. 扩展 DatabaseType 枚举
public enum DatabaseType {
    POSTGRESQL("PostgreSQL", "jdbc:postgresql", 5432),
    MYSQL("MySQL", "jdbc:mysql", 3306),
    ORACLE("Oracle", "jdbc:oracle:thin", 1521); // 新增
}

// 2. 实现类型映射器
@Component
public class OracleTypeMapper implements DatabaseTypeMapper {
    @Override
    public String mapType(String sourceType, DatabaseType targetType) {
        // 实现Oracle类型映射逻辑
    }
}
```

#### 2. 扩展AI功能
```java
@Service
public class EnhancedAiService extends AiFieldNamingService {

    public TableOptimizationSuggestion optimizeTableStructure(
            TableStructure table, String businessContext) {
        // 实现表结构优化建议
    }
}
```

### 测试指南

#### 单元测试
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=DatabaseServiceTest

# 生成测试报告
mvn surefire-report:report
```

#### 集成测试
```bash
# 启动测试环境
mvn spring-boot:run -Dspring.profiles.active=test

# 运行集成测试
mvn verify -P integration-test
```

## 🚀 部署指南

### 生产环境部署

#### 1. 构建应用
```bash
# 构建生产版本
mvn clean package -Pprod

# 生成的JAR文件
target/DB_CopyData-0.0.1-SNAPSHOT.jar
```

#### 2. 配置生产环境
创建 `application-prod.yml`:
```yaml
server:
  port: 8080

spring:
  profiles:
    active: prod
  datasource:
    url: *****************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

app:
  ai:
    field-naming:
      enabled: true

logging:
  level:
    com.liuyang: INFO
    root: WARN
  file:
    name: /var/log/db-copy-data/application.log
```

#### 3. Docker部署
```dockerfile
FROM openjdk:21-jre-slim

WORKDIR /app

COPY target/DB_CopyData-0.0.1-SNAPSHOT.jar app.jar

EXPOSE 8080

ENV SPRING_PROFILES_ACTIVE=prod

CMD ["java", "-jar", "app.jar"]
```

```bash
# 构建镜像
docker build -t db-copy-data:latest .

# 运行容器
docker run -d \
  --name db-copy-data \
  -p 8080:8080 \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=your_password \
  -e AI_DASHSCOPE_API_KEY=your_api_key \
  db-copy-data:latest
```

#### 4. 系统服务配置
创建 systemd 服务文件 `/etc/systemd/system/db-copy-data.service`:
```ini
[Unit]
Description=DB Copy Data Service
After=network.target

[Service]
Type=simple
User=app
WorkingDirectory=/opt/db-copy-data
ExecStart=/usr/bin/java -jar DB_CopyData-0.0.1-SNAPSHOT.jar
Restart=always
RestartSec=10

Environment=SPRING_PROFILES_ACTIVE=prod
Environment=DB_USERNAME=postgres
Environment=DB_PASSWORD=your_password
Environment=AI_DASHSCOPE_API_KEY=your_api_key

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable db-copy-data
sudo systemctl start db-copy-data
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
**问题**: `Connection refused` 或 `Authentication failed`

**解决方案**:
- 检查数据库服务是否启动
- 验证连接参数 (host, port, username, password)
- 确认防火墙设置
- 检查数据库用户权限

```bash
# 测试PostgreSQL连接
psql -h localhost -p 5432 -U postgres -d test_db

# 测试MySQL连接
mysql -h localhost -P 3306 -u root -p
```

#### 2. AI服务调用失败
**问题**: `AI service unavailable` 或 `API key invalid`

**解决方案**:
- 验证API密钥是否正确
- 检查网络连接到阿里云
- 确认DashScope服务配额
- 查看AI服务降级日志

#### 3. 内存不足错误
**问题**: `OutOfMemoryError` 在大表迁移时

**解决方案**:
- 增加JVM堆内存: `-Xmx4g`
- 调整批处理大小: `app.copy.batch-size=500`
- 优化数据库连接池配置

#### 4. 字符编码问题
**问题**: 中文字符显示乱码

**解决方案**:
```yaml
spring:
  datasource:
    url: ************************************************************************
```

### 日志分析

#### 启用调试日志
```yaml
logging:
  level:
    com.liuyang: DEBUG
    org.springframework.jdbc: DEBUG
```

#### 关键日志位置
- 应用日志: `logs/db-copy-data-dev.log`
- 迁移日志: 搜索关键字 `Migration`
- AI服务日志: 搜索关键字 `AI`
- 错误日志: 搜索关键字 `ERROR`

## 🤝 贡献指南

### 参与贡献

我们欢迎所有形式的贡献，包括但不限于:
- 🐛 Bug报告和修复
- ✨ 新功能开发
- 📚 文档改进
- 🧪 测试用例添加
- 🎨 UI/UX优化

### 开发流程

1. **Fork项目** 到你的GitHub账号
2. **创建特性分支**: `git checkout -b feature/amazing-feature`
3. **提交更改**: `git commit -m 'Add amazing feature'`
4. **推送分支**: `git push origin feature/amazing-feature`
5. **创建Pull Request**

### 代码规范

#### Java代码规范
- 遵循Google Java Style Guide
- 使用4空格缩进
- 类名使用PascalCase
- 方法名使用camelCase
- 常量使用UPPER_SNAKE_CASE

#### 提交信息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明:
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例:
```
feat(ai): 添加表结构优化AI建议功能

- 集成新的AI模型接口
- 支持表结构分析和优化建议
- 添加相关单元测试

Closes #123
```

## 📄 许可证

本项目采用 Apache License 2.0 许可证。详细信息请查看 [LICENSE](LICENSE) 文件。

## 👥 作者和贡献者

### 主要开发者
- **刘洋** - 项目创建者和主要维护者

### 贡献者
感谢所有为项目做出贡献的开发者！

## 📞 支持和联系

### 获取帮助
- 📧 邮件支持: [项目邮箱]
- 💬 问题讨论: [GitHub Issues](http://gltb.dev.necloud.com.cn/health-car-document/db_copydata/-/issues)
- 📖 文档中心: [项目Wiki]

### 项目状态
- ✅ 活跃开发中
- 🔄 持续集成
- 📈 功能不断完善

---

**⭐ 如果这个项目对你有帮助，请给我们一个Star！**

**🔗 项目地址**: http://gltb.dev.necloud.com.cn/health-car-document/db_copydata
