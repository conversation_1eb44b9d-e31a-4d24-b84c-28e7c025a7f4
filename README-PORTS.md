# 🌐 端口配置总结

## 📋 环境端口配置

| 环境 | 端口 | 数据库 | 访问地址 | 配置文件 |
|------|------|--------|----------|----------|
| **开发** | 9999 | 本地MySQL | http://localhost:9999 | `application.yml` |
| **测试** | 9999 | 阿里云RDS | http://localhost:9999 | `application-test.yml` |
| **生产** | 8080 | 阿里云RDS | http://localhost:8080 | `application-prod.yml` |

## ⚠️ 重要说明

### 端口冲突处理
- **开发环境** 和 **测试环境** 都使用 **9999** 端口
- 这意味着同一时间只能运行其中一个环境
- 如需同时运行，请手动修改其中一个环境的端口

### 环境切换
```bash
# 停止当前运行的应用
Ctrl + C

# 切换到测试环境
./scripts/start-test.sh

# 或切换到开发环境
./scripts/start-dev.sh

# 生产环境使用不同端口，可以同时运行
./scripts/start-prod.sh
```

## 🔧 如需修改端口

### 修改测试环境端口为8888
如果您希望测试环境使用不同端口，可以修改：

1. **修改配置文件**：
```yaml
# src/main/resources/application-test.yml
server:
  port: 8888  # 改为8888
```

2. **修改启动脚本**：
```bash
# scripts/start-test.sh
echo "🌐 端口: 8888"  # 更新显示信息
```

3. **访问地址**：
- 开发环境: http://localhost:9999
- 测试环境: http://localhost:8888
- 生产环境: http://localhost:8080

## 🚀 推荐配置

为了避免端口冲突，建议：

### 方案一：错开端口
- 开发环境: 9999
- 测试环境: 8888  
- 生产环境: 8080

### 方案二：统一端口
- 所有环境: 9999 (通过环境切换使用)

### 方案三：Docker部署
- 使用Docker容器，每个环境独立端口映射

## 📱 当前访问方式

### 开发环境
```bash
./scripts/start-dev.sh
# 访问: http://localhost:9999
```

### 测试环境  
```bash
./scripts/start-test.sh
# 访问: http://localhost:9999 (与开发环境相同端口)
```

### 生产环境
```bash
./scripts/start-prod.sh  
# 访问: http://localhost:8080 (独立端口)
```
