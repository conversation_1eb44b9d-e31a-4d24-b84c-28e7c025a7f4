package com.liuyang.util;

import com.liuyang.dto.ColumnInfo;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 审计字段工具类测试
 */
public class AuditFieldUtilsTest {

    @Test
    public void testIsAuditField() {
        // 测试标准审计字段
        assertTrue(AuditFieldUtils.isAuditField("create_user"));
        assertTrue(AuditFieldUtils.isAuditField("create_time"));
        assertTrue(AuditFieldUtils.isAuditField("update_user"));
        assertTrue(AuditFieldUtils.isAuditField("update_time"));
        
        // 测试变体
        assertTrue(AuditFieldUtils.isAuditField("created_by"));
        assertTrue(AuditFieldUtils.isAuditField("updated_at"));
        
        // 测试非审计字段
        assertFalse(AuditFieldUtils.isAuditField("user_name"));
        assertFalse(AuditFieldUtils.isAuditField("id"));
        assertFalse(AuditFieldUtils.isAuditField(null));
    }

    @Test
    public void testGetMissingAuditFields() {
        // 创建一个没有审计字段的表结构
        List<ColumnInfo> columns = new ArrayList<>();
        
        ColumnInfo idColumn = new ColumnInfo();
        idColumn.setColumnName("id");
        idColumn.setDataType("bigint");
        columns.add(idColumn);
        
        ColumnInfo nameColumn = new ColumnInfo();
        nameColumn.setColumnName("name");
        nameColumn.setDataType("varchar(100)");
        columns.add(nameColumn);
        
        // 检查缺失的审计字段
        List<AuditFieldUtils.AuditField> missingFields = AuditFieldUtils.getMissingAuditFields(columns);
        
        // 应该缺失所有4个标准审计字段
        assertEquals(4, missingFields.size());
        
        List<String> missingFieldNames = missingFields.stream()
                .map(AuditFieldUtils.AuditField::getFieldName)
                .toList();
        
        assertTrue(missingFieldNames.contains("create_user"));
        assertTrue(missingFieldNames.contains("create_time"));
        assertTrue(missingFieldNames.contains("update_user"));
        assertTrue(missingFieldNames.contains("update_time"));
    }

    @Test
    public void testGetMissingAuditFieldsWithExisting() {
        // 创建一个已有部分审计字段的表结构
        List<ColumnInfo> columns = new ArrayList<>();
        
        ColumnInfo idColumn = new ColumnInfo();
        idColumn.setColumnName("id");
        idColumn.setDataType("bigint");
        columns.add(idColumn);
        
        ColumnInfo createUserColumn = new ColumnInfo();
        createUserColumn.setColumnName("create_user");
        createUserColumn.setDataType("varchar(200)");
        columns.add(createUserColumn);
        
        ColumnInfo createTimeColumn = new ColumnInfo();
        createTimeColumn.setColumnName("create_time");
        createTimeColumn.setDataType("datetime");
        columns.add(createTimeColumn);
        
        // 检查缺失的审计字段
        List<AuditFieldUtils.AuditField> missingFields = AuditFieldUtils.getMissingAuditFields(columns);
        
        // 应该只缺失2个审计字段
        assertEquals(2, missingFields.size());
        
        List<String> missingFieldNames = missingFields.stream()
                .map(AuditFieldUtils.AuditField::getFieldName)
                .toList();
        
        assertTrue(missingFieldNames.contains("update_user"));
        assertTrue(missingFieldNames.contains("update_time"));
        assertFalse(missingFieldNames.contains("create_user"));
        assertFalse(missingFieldNames.contains("create_time"));
    }

    @Test
    public void testAddMissingAuditFields() {
        // 创建一个没有审计字段的表结构
        List<ColumnInfo> columns = new ArrayList<>();
        
        ColumnInfo idColumn = new ColumnInfo();
        idColumn.setColumnName("id");
        idColumn.setDataType("bigint");
        columns.add(idColumn);
        
        ColumnInfo nameColumn = new ColumnInfo();
        nameColumn.setColumnName("name");
        nameColumn.setDataType("varchar(100)");
        columns.add(nameColumn);
        
        // 添加缺失的审计字段
        List<ColumnInfo> result = AuditFieldUtils.addMissingAuditFields(columns);
        
        // 应该有原来的2个字段 + 4个审计字段 = 6个字段
        assertEquals(6, result.size());
        
        // 检查审计字段是否正确添加
        List<String> columnNames = result.stream()
                .map(ColumnInfo::getColumnName)
                .toList();
        
        assertTrue(columnNames.contains("create_user"));
        assertTrue(columnNames.contains("create_time"));
        assertTrue(columnNames.contains("update_user"));
        assertTrue(columnNames.contains("update_time"));
        
        // 检查自动添加的字段是否正确标记
        for (ColumnInfo column : result) {
            if (AuditFieldUtils.getStandardAuditFieldNames().contains(column.getColumnName())) {
                assertTrue(column.isAutoAdded(), "审计字段应该被标记为自动添加: " + column.getColumnName());
            } else {
                assertFalse(column.isAutoAdded(), "非审计字段不应该被标记为自动添加: " + column.getColumnName());
            }
        }
    }

    @Test
    public void testFilterOutAutoAddedFields() {
        List<ColumnInfo> columns = new ArrayList<>();
        
        // 普通字段
        ColumnInfo normalColumn = new ColumnInfo();
        normalColumn.setColumnName("name");
        normalColumn.setAutoAdded(false);
        columns.add(normalColumn);
        
        // 自动添加的审计字段
        ColumnInfo auditColumn = new ColumnInfo();
        auditColumn.setColumnName("create_user");
        auditColumn.setAutoAdded(true);
        columns.add(auditColumn);
        
        // 过滤掉自动添加的字段
        List<ColumnInfo> filtered = AuditFieldUtils.filterOutAutoAddedFields(columns);
        
        assertEquals(1, filtered.size());
        assertEquals("name", filtered.get(0).getColumnName());
        assertFalse(filtered.get(0).isAutoAdded());
    }
}
