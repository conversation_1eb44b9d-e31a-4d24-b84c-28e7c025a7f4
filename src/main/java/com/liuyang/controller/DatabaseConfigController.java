package com.liuyang.controller;

import com.liuyang.dto.DatabaseConnectionConfig;
import com.liuyang.dto.DatabaseInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.service.DynamicDatabaseService;
import com.liuyang.service.DatabaseConfigManagementService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库配置控制器
 * 处理数据库连接配置和测试
 */
@RestController
@RequestMapping("/api/db-config")
public class DatabaseConfigController {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfigController.class);
    
    @Autowired
    private DynamicDatabaseService dynamicDatabaseService;

    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;
    
    /**
     * 获取默认数据库配置
     */
    @GetMapping("/defaults")
    public ResponseEntity<Map<String, Object>> getDefaultConfigs() {
        logger.info("获取默认数据库配置");

        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, DatabaseConnectionConfig> configs = new HashMap<>();

            // 尝试从数据库配置管理服务获取默认配置
            try {
                // 获取源数据库配置（通常是PostgreSQL）
                DatabaseConfigEntity sourceConfig = databaseConfigManagementService.getSourceDatabaseConfig();
                DatabaseConnectionConfig postgresConfig = convertToConnectionConfig(sourceConfig);
                postgresConfig.setDescription("PostgreSQL源数据库");
                configs.put("postgresql", postgresConfig);

                // 获取目标数据库配置（通常是MySQL）
                DatabaseConfigEntity targetConfig = databaseConfigManagementService.getTargetDatabaseConfig();
                DatabaseConnectionConfig mysqlConfig = convertToConnectionConfig(targetConfig);
                mysqlConfig.setDescription("MySQL目标数据库");
                configs.put("mysql", mysqlConfig);

            } catch (Exception e) {
                logger.warn("无法从数据库获取配置，使用默认配置: {}", e.getMessage());

                // 降级方案：提供基本的默认配置模板
                DatabaseConnectionConfig postgresConfig = new DatabaseConnectionConfig(
                    DatabaseType.POSTGRESQL, "localhost", 5432, "postgres", ""
                );
                postgresConfig.setDescription("PostgreSQL源数据库（请配置实际连接信息）");

                DatabaseConnectionConfig mysqlConfig = new DatabaseConnectionConfig(
                    DatabaseType.MYSQL, "localhost", 3306, "root", ""
                );
                mysqlConfig.setDescription("MySQL目标数据库（请配置实际连接信息）");

                configs.put("postgresql", postgresConfig);
                configs.put("mysql", mysqlConfig);
            }

            response.put("success", true);
            response.put("data", configs);
            response.put("message", "获取默认配置成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取默认配置失败", e);
            response.put("success", false);
            response.put("message", "获取默认配置失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 将数据库配置实体转换为连接配置DTO
     */
    private DatabaseConnectionConfig convertToConnectionConfig(DatabaseConfigEntity entity) {
        DatabaseType dbType = "POSTGRESQL".equalsIgnoreCase(entity.getDatabaseType())
            ? DatabaseType.POSTGRESQL : DatabaseType.MYSQL;

        DatabaseConnectionConfig config = new DatabaseConnectionConfig(
            dbType, entity.getHost(), entity.getPort(),
            entity.getUsername(), entity.getPassword()
        );
        config.setDescription(entity.getDescription());
        return config;
    }
    
    /**
     * 测试数据库连接
     */
    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@Valid @RequestBody DatabaseConnectionConfig config) {
        logger.info("测试数据库连接: {}", config);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isConnected = dynamicDatabaseService.testConnection(config);
            
            response.put("success", isConnected);
            response.put("connected", isConnected);
            response.put("message", isConnected ? "连接测试成功" : "连接测试失败");
            response.put("config", Map.of(
                "databaseType", config.getDatabaseType().getDisplayName(),
                "host", config.getHost(),
                "port", config.getPort(),
                "username", config.getUsername()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("数据库连接测试失败", e);
            response.put("success", false);
            response.put("connected", false);
            response.put("message", "连接测试异常: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取数据库列表
     */
    @PostMapping("/databases")
    public ResponseEntity<Map<String, Object>> getDatabases(@Valid @RequestBody DatabaseConnectionConfig config) {
        logger.info("获取数据库列表: {}", config.getDatabaseType());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<DatabaseInfo> databases = dynamicDatabaseService.getDatabaseList(config);
            
            response.put("success", true);
            response.put("data", databases);
            response.put("count", databases.size());
            response.put("message", "获取数据库列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取数据库列表失败", e);
            response.put("success", false);
            response.put("message", "获取数据库列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取表列表
     */
    @PostMapping("/tables")
    public ResponseEntity<Map<String, Object>> getTables(
            @Valid @RequestBody DatabaseConnectionConfig config,
            @RequestParam String databaseName) {
        logger.info("获取表列表: {} -> {}", config.getDatabaseType(), databaseName);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> tables = dynamicDatabaseService.getTableList(config, databaseName);
            
            response.put("success", true);
            response.put("data", tables);
            response.put("count", tables.size());
            response.put("message", "获取表列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取表列表失败", e);
            response.put("success", false);
            response.put("message", "获取表列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 验证配置并获取完整信息
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateConfig(@Valid @RequestBody DatabaseConnectionConfig config) {
        logger.info("验证数据库配置: {}", config);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 测试连接
            boolean isConnected = dynamicDatabaseService.testConnection(config);
            
            if (!isConnected) {
                response.put("success", false);
                response.put("message", "数据库连接失败，请检查配置信息");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 获取数据库列表
            List<DatabaseInfo> databases = dynamicDatabaseService.getDatabaseList(config);
            
            response.put("success", true);
            response.put("connected", true);
            response.put("databases", databases);
            response.put("databaseCount", databases.size());
            response.put("message", "配置验证成功");
            response.put("config", Map.of(
                "databaseType", config.getDatabaseType().getDisplayName(),
                "host", config.getHost(),
                "port", config.getPort(),
                "username", config.getUsername(),
                "description", config.getDescription() != null ? config.getDescription() : ""
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("验证数据库配置失败", e);
            response.put("success", false);
            response.put("message", "配置验证失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
} 