package com.liuyang.controller;

import com.liuyang.dto.TableInfo;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.TableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表控制器
 */
@RestController
@RequestMapping("/api/tables")
public class TableController {
    
    private static final Logger logger = LoggerFactory.getLogger(TableController.class);
    
    @Autowired
    private TableService tableService;
    
    /**
     * 获取指定数据库的表列表
     */
    @GetMapping("/database/{databaseName}")
    public ResponseEntity<Map<String, Object>> getTablesByDatabase(@PathVariable String databaseName) {
        logger.info("获取数据库 {} 的表列表", databaseName);
        
        Map<String, Object> response = new HashMap<>();
        try {
            List<TableInfo> tables = tableService.getTablesByDatabase(databaseName);
            response.put("success", true);
            response.put("data", tables);
            response.put("message", "获取表列表成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取表列表失败", e);
            response.put("success", false);
            response.put("message", "获取表列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取表结构信息
     */
    @GetMapping("/{databaseName}/{tableName}/structure")
    public ResponseEntity<Map<String, Object>> getTableStructure(
            @PathVariable String databaseName,
            @PathVariable String tableName) {
        logger.info("获取表结构: {}.{}", databaseName, tableName);
        
        Map<String, Object> response = new HashMap<>();
        try {
            TableStructure structure = tableService.getTableStructure(databaseName, tableName);
            response.put("success", true);
            response.put("data", structure);
            response.put("message", "获取表结构成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取表结构失败", e);
            response.put("success", false);
            response.put("message", "获取表结构失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 检查表是否存在
     */
    @GetMapping("/{databaseName}/{tableName}/exists")
    public ResponseEntity<Map<String, Object>> checkTableExists(
            @PathVariable String databaseName,
            @PathVariable String tableName) {
        logger.info("检查表是否存在: {}.{}", databaseName, tableName);
        
        Map<String, Object> response = new HashMap<>();
        try {
            boolean exists = tableService.tableExists(databaseName, tableName);
            response.put("success", true);
            response.put("data", Map.of("exists", exists));
            response.put("message", exists ? "表存在" : "表不存在");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("检查表是否存在失败", e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
