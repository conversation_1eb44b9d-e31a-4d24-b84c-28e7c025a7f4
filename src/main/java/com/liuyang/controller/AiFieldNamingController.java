package com.liuyang.controller;

import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI字段命名控制器
 * 提供AI字段命名相关的REST API
 */
@RestController
@RequestMapping("/api/ai/field-naming")

public class AiFieldNamingController {

    private static final Logger logger = LoggerFactory.getLogger(AiFieldNamingController.class);

    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;

    /**
     * 生成字段映射建议
     */
    @PostMapping("/suggestions")
    public ResponseEntity<Map<String, Object>> generateFieldMappingSuggestions(
            @RequestBody FieldMappingRequest request) {
        
        logger.info("收到字段映射建议请求: 源表={}, 目标表={}", 
                   request.getSourceTable().getTableName(), request.getTargetTableName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用");
                return ResponseEntity.ok(response);
            }
            
            List<FieldMapping> suggestions = aiFieldNamingService.generateFieldMappingSuggestions(
                request.getSourceTable(),
                request.getTargetTableName(),
                request.getBusinessContext()
            );
            
            response.put("success", true);
            response.put("suggestions", suggestions);
            response.put("count", suggestions.size());
            
            logger.info("成功生成 {} 个字段映射建议", suggestions.size());
            
        } catch (Exception e) {
            logger.error("生成字段映射建议失败", e);
            response.put("success", false);
            response.put("message", "生成建议失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 优化单个字段名
     */
    @PostMapping("/optimize-field")
    public ResponseEntity<Map<String, Object>> optimizeFieldName(
            @RequestBody FieldOptimizationRequest request) {
        
        logger.info("收到字段名优化请求: {}", request.getOriginalFieldName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用");
                return ResponseEntity.ok(response);
            }
            
            String optimizedName = aiFieldNamingService.optimizeFieldName(
                request.getOriginalFieldName(),
                request.getFieldType(),
                request.getComment(),
                request.getBusinessContext()
            );
            
            response.put("success", true);
            response.put("originalName", request.getOriginalFieldName());
            response.put("optimizedName", optimizedName);
            
            logger.info("字段名优化完成: {} -> {}", request.getOriginalFieldName(), optimizedName);
            
        } catch (Exception e) {
            logger.error("字段名优化失败", e);
            response.put("success", false);
            response.put("message", "优化失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 生成字段注释
     */
    @PostMapping("/generate-comment")
    public ResponseEntity<Map<String, Object>> generateFieldComment(
            @RequestBody FieldCommentRequest request) {
        
        logger.info("收到字段注释生成请求: {}", request.getFieldName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用");
                return ResponseEntity.ok(response);
            }
            
            String generatedComment = aiFieldNamingService.generateFieldComment(
                request.getFieldName(),
                request.getFieldType(),
                request.getOriginalComment(),
                request.getBusinessContext()
            );
            
            response.put("success", true);
            response.put("fieldName", request.getFieldName());
            response.put("comment", generatedComment);
            response.put("originalComment", request.getOriginalComment());
            
            logger.info("字段注释生成完成: {} -> {}", request.getFieldName(), generatedComment);
            
        } catch (Exception e) {
            logger.error("字段注释生成失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 验证字段名
     */
    @PostMapping("/validate-field")
    public ResponseEntity<Map<String, Object>> validateFieldName(
            @RequestBody FieldValidationRequest request) {
        
        logger.info("收到字段名验证请求: {}", request.getFieldName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用");
                return ResponseEntity.ok(response);
            }
            
            AiFieldNamingService.FieldNameValidationResult result =
                aiFieldNamingService.validateFieldName(request.getFieldName(), request.getFieldType());
            
            response.put("success", true);
            response.put("valid", result.isValid());
            response.put("reason", result.getReason());
            response.put("suggestion", result.getSuggestion());
            response.put("improvedName", result.getImprovedName());
            
            logger.info("字段名验证完成: {} -> valid={}", request.getFieldName(), result.isValid());
            
        } catch (Exception e) {
            logger.error("字段名验证失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 生成表结构建议
     */
    @PostMapping("/table-structure")
    public ResponseEntity<Map<String, Object>> generateTableStructure(
            @RequestBody TableStructureRequest request) {
        
        logger.info("收到表结构建议请求: {}", request.getTableName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用");
                return ResponseEntity.ok(response);
            }
            
            AiFieldNamingService.TableStructureSuggestion suggestion =
                aiFieldNamingService.generateTableStructureSuggestion(
                    request.getTableName(),
                    request.getBusinessDescription(),
                    request.getFieldCount()
                );
            
            response.put("success", true);
            response.put("suggestion", suggestion);
            
            logger.info("表结构建议生成完成: {} 个字段", suggestion.getFields().size());
            
        } catch (Exception e) {
            logger.error("表结构建议生成失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取AI服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getAiServiceStatus() {
        Map<String, Object> response = new HashMap<>();
        
        boolean aiEnabled = aiFieldNamingService != null;
        String serviceType = aiEnabled ? "AI服务" : "降级服务";
        
        response.put("aiEnabled", aiEnabled);
        response.put("serviceType", serviceType);
        response.put("status", "运行中");
        
        logger.info("AI服务状态查询: enabled={}, type={}", aiEnabled, serviceType);
        
        return ResponseEntity.ok(response);
    }

    // 请求DTO类
    public static class FieldMappingRequest {
        private TableStructure sourceTable;
        private String targetTableName;
        private String businessContext;

        // Getters and Setters
        public TableStructure getSourceTable() { return sourceTable; }
        public void setSourceTable(TableStructure sourceTable) { this.sourceTable = sourceTable; }

        public String getTargetTableName() { return targetTableName; }
        public void setTargetTableName(String targetTableName) { this.targetTableName = targetTableName; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }

    public static class FieldOptimizationRequest {
        private String originalFieldName;
        private String fieldType;
        private String comment;
        private String businessContext;

        // Getters and Setters
        public String getOriginalFieldName() { return originalFieldName; }
        public void setOriginalFieldName(String originalFieldName) { this.originalFieldName = originalFieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }

    public static class FieldCommentRequest {
        private String fieldName;
        private String fieldType;
        private String originalComment;
        private String businessContext;

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getOriginalComment() { return originalComment; }
        public void setOriginalComment(String originalComment) { this.originalComment = originalComment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }

    public static class FieldValidationRequest {
        private String fieldName;
        private String fieldType;

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }
    }

    public static class FieldCommentGenerationRequest {
        private String fieldName;
        private String fieldType;
        private String originalComment;
        private String businessContext;

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getOriginalComment() { return originalComment; }
        public void setOriginalComment(String originalComment) { this.originalComment = originalComment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }

    public static class TableStructureRequest {
        private String tableName;
        private String businessDescription;
        private Integer fieldCount;

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public String getBusinessDescription() { return businessDescription; }
        public void setBusinessDescription(String businessDescription) { this.businessDescription = businessDescription; }

        public Integer getFieldCount() { return fieldCount; }
        public void setFieldCount(Integer fieldCount) { this.fieldCount = fieldCount; }
    }
}
