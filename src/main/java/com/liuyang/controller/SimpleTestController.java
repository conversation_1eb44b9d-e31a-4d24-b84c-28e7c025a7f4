package com.liuyang.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 简单测试控制器
 * 用于验证Spring AI Alibaba配置
 */
@RestController
@RequestMapping("/api/simple-test")

public class SimpleTestController {

    private static final Logger logger = LoggerFactory.getLogger(SimpleTestController.class);

    @Autowired(required = false)
    @Qualifier("fieldNamingChatClient")
    private ChatClient fieldNamingChatClient;

    @Value("${spring.ai.dashscope.api-key:}")
    private String dashscopeApiKey;

    @Value("${app.ai.field-naming.enabled:false}")
    private boolean aiEnabled;

    /**
     * 简单的健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", System.currentTimeMillis());
        response.put("aiEnabled", aiEnabled);
        response.put("chatClientAvailable", fieldNamingChatClient != null);
        
        boolean hasValidApiKey = dashscopeApiKey != null && 
                                !dashscopeApiKey.trim().isEmpty() && 
                                !dashscopeApiKey.contains("your-dashscope-api-key");
        response.put("hasValidApiKey", hasValidApiKey);
        
        logger.info("健康检查: AI启用={}, ChatClient可用={}, API密钥有效={}", 
                   aiEnabled, fieldNamingChatClient != null, hasValidApiKey);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试简单的AI调用（如果可用）
     */
    @PostMapping("/test-ai")
    public ResponseEntity<Map<String, Object>> testAi(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (fieldNamingChatClient == null) {
                response.put("success", false);
                response.put("message", "ChatClient未配置");
                return ResponseEntity.ok(response);
            }
            
            String testPrompt = request.getOrDefault("prompt", "请说'你好'");
            
            // 简单的AI调用测试
            String result = fieldNamingChatClient.prompt(testPrompt).call().content();
            
            response.put("success", true);
            response.put("prompt", testPrompt);
            response.put("result", result);
            response.put("message", "AI调用成功");
            
            logger.info("AI测试调用成功: {} -> {}", testPrompt, result);
            
        } catch (Exception e) {
            logger.error("AI测试调用失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "AI调用失败");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("aiEnabled", aiEnabled);
        response.put("chatClientType", fieldNamingChatClient != null ? 
                    fieldNamingChatClient.getClass().getSimpleName() : "null");
        response.put("hasApiKey", dashscopeApiKey != null && !dashscopeApiKey.trim().isEmpty());
        response.put("apiKeyPrefix", dashscopeApiKey != null && dashscopeApiKey.length() > 10 ? 
                    dashscopeApiKey.substring(0, 10) + "..." : "未配置");
        
        return ResponseEntity.ok(response);
    }
}
