package com.liuyang.controller;

import com.liuyang.entity.MigrationRecord;
import com.liuyang.entity.TableMappingRecord;
import com.liuyang.entity.FieldMappingRecord;
import com.liuyang.controller.LogWebSocketController;
import com.liuyang.mapper.MigrationRecordMapper.MigrationStatistics;
import com.liuyang.mapper.TableMappingRecordMapper;
import com.liuyang.mapper.TableMappingRecordMapper.TableMappingStatistics;
import com.liuyang.mapper.FieldMappingRecordMapper;
import com.liuyang.mapper.FieldMappingRecordMapper.FieldMappingStatistics;
import com.liuyang.service.MigrationMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 迁移历史控制器
 */
@RestController
@RequestMapping("/api/migration-history")
public class MigrationHistoryController {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationHistoryController.class);
    
    @Autowired
    private MigrationMappingService migrationMappingService;
    
    @Autowired
    private TableMappingRecordMapper tableMappingRecordMapper;
    
    @Autowired
    private FieldMappingRecordMapper fieldMappingRecordMapper;
    
    /**
     * 查询迁移记录列表（分页）
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getMigrationRecords(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        
        try {
            List<MigrationRecord> records = migrationMappingService.getMigrationRecords(pageNum, pageSize);
            int total = migrationMappingService.getMigrationRecordCount();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", records);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询迁移记录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 根据批次ID查询迁移记录详情
     */
    @GetMapping("/detail/{migrationBatchId}")
    public ResponseEntity<Map<String, Object>> getMigrationDetail(@PathVariable String migrationBatchId) {
        try {
            Long batchId = Long.parseLong(migrationBatchId);
            MigrationRecord record = migrationMappingService.getMigrationRecordWithDetails(batchId);
            
            Map<String, Object> response = new HashMap<>();
            if (record != null) {
                response.put("success", true);
                response.put("data", record);
            } else {
                response.put("success", false);
                response.put("message", "未找到对应的迁移记录");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询迁移记录详情失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 查询迁移统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getMigrationStatistics() {
        try {
            MigrationStatistics statistics = migrationMappingService.getMigrationStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询迁移统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 查询表迁移历史
     */
    @GetMapping("/table-history")
    public ResponseEntity<Map<String, Object>> getTableMigrationHistory(
            @RequestParam String sourceTableName,
            @RequestParam(required = false) String targetTableName) {
        
        try {
            List<TableMappingRecord> records = migrationMappingService.getTableMigrationHistory(sourceTableName, targetTableName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", records);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询表迁移历史失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 根据源表名查询迁移记录
     */
    @GetMapping("/source-table/{sourceTableName}")
    public ResponseEntity<List<MigrationRecord>> getMigrationRecordsBySourceTable(
            @PathVariable String sourceTableName) {
        List<MigrationRecord> records = migrationMappingService.getMigrationRecordsBySourceTable(sourceTableName);
        return ResponseEntity.ok(records);
    }
    
    /**
     * 根据目标表名查询迁移记录
     */
    @GetMapping("/target-table/{targetTableName}")
    public ResponseEntity<List<MigrationRecord>> getMigrationRecordsByTargetTable(
            @PathVariable String targetTableName) {
        List<MigrationRecord> records = migrationMappingService.getMigrationRecordsByTargetTable(targetTableName);
        return ResponseEntity.ok(records);
    }
    
    /**
     * 根据表映射关系查询迁移记录
     */
    @GetMapping("/table-mapping")
    public ResponseEntity<List<MigrationRecord>> getMigrationRecordsByTableMapping(
            @RequestParam String sourceTableName,
            @RequestParam String targetTableName) {
        List<MigrationRecord> records = migrationMappingService.getMigrationRecordsByTableMapping(sourceTableName, targetTableName);
        return ResponseEntity.ok(records);
    }
    
    /**
     * 检查表迁移是否已存在
     */
    @GetMapping("/check-exists")
    public ResponseEntity<Boolean> checkTableMigrationExists(
            @RequestParam String sourceTableName,
            @RequestParam String targetTableName) {
        boolean exists = migrationMappingService.isTableMigrationExists(sourceTableName, targetTableName);
        return ResponseEntity.ok(exists);
    }
    
    /**
     * 测试新的历史记录查询逻辑
     */
    @GetMapping("/test-table-history/{sourceTableName}")
    public ResponseEntity<Map<String, Object>> testTableMigrationHistory(@PathVariable String sourceTableName) {
        try {
            logger.info("测试表 {} 的历史记录查询逻辑", sourceTableName);
            
            // 使用新的查询逻辑
            List<TableMappingRecord> records = migrationMappingService.getTableMigrationHistory(sourceTableName, null);
            
            // 使用旧的查询逻辑进行对比
            List<MigrationRecord> migrationRecords = migrationMappingService.getMigrationRecordsBySourceTable(sourceTableName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sourceTableName", sourceTableName);
            response.put("newLogicResults", records.size());
            response.put("migrationRecordsCount", migrationRecords.size());
            response.put("tableMappingRecords", records);
            response.put("migrationRecords", migrationRecords);
            
            // 详细分析
            List<Map<String, Object>> analysis = new ArrayList<>();
            for (TableMappingRecord record : records) {
                Map<String, Object> recordAnalysis = new HashMap<>();
                recordAnalysis.put("tableMappingId", record.getId());
                recordAnalysis.put("sourceTableName", record.getSourceTableName());
                recordAnalysis.put("targetTableName", record.getTargetTableName());
                recordAnalysis.put("migrationTime", record.getMigrationStartTime());
                recordAnalysis.put("fieldCount", record.getFieldMappingRecords() != null ? record.getFieldMappingRecords().size() : 0);
                
                if (record.getFieldMappingRecords() != null) {
                    List<String> fieldStructure = record.getFieldMappingRecords().stream()
                        .map(f -> f.getSourceFieldName() + ":" + f.getSourceFieldType())
                        .toList();
                    recordAnalysis.put("fieldStructure", fieldStructure);
                }
                
                analysis.add(recordAnalysis);
            }
            response.put("detailedAnalysis", analysis);
            
            logger.info("表 {} 的历史记录查询完成: 新逻辑找到 {} 条记录，迁移记录 {} 条", 
                       sourceTableName, records.size(), migrationRecords.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("测试历史记录查询失败: " + sourceTableName, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }



    /**
     * 删除源表的所有历史迁移记录
     */
    @DeleteMapping("/source-table/{sourceTableName}")
    public ResponseEntity<Map<String, Object>> deleteAllHistoryBySourceTable(@PathVariable String sourceTableName) {
        try {
            int deletedCount = migrationMappingService.deleteAllHistoryBySourceTable(sourceTableName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", String.format("成功删除源表 %s 的 %d 条历史迁移记录", sourceTableName, deletedCount));
            response.put("deletedCount", deletedCount);
            
            logger.info("手动删除源表 {} 的历史记录: {} 条", sourceTableName, deletedCount);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("删除源表历史记录失败: " + sourceTableName, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除源表历史记录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 测试字段映射逻辑（验证双主键设计修复）
     */
    @GetMapping("/test-field-mapping/{sourceTableName}")
    public ResponseEntity<Map<String, Object>> testFieldMapping(@PathVariable String sourceTableName) {
        try {
            List<MigrationRecord> migrationRecords = migrationMappingService.getMigrationRecordsBySourceTable(sourceTableName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sourceTableName", sourceTableName);
            response.put("totalMigrationRecords", migrationRecords.size());
            
            List<Map<String, Object>> recordDetails = new ArrayList<>();
            
            for (MigrationRecord record : migrationRecords) {
                Map<String, Object> recordInfo = new HashMap<>();
                recordInfo.put("migrationBatchId", record.getMigrationBatchId());
                recordInfo.put("sourceTableName", record.getSourceTableName());
                recordInfo.put("targetTableName", record.getTargetTableName());
                recordInfo.put("migrationTime", record.getMigrationStartTime());
                
                // 查询表映射记录
                List<TableMappingRecord> tableMappings = tableMappingRecordMapper.selectByMigrationRecordId(record.getId());
                
                List<Map<String, Object>> tableMappingDetails = new ArrayList<>();
                for (TableMappingRecord tableMapping : tableMappings) {
                    Map<String, Object> tableMappingInfo = new HashMap<>();
                    tableMappingInfo.put("tableMappingId", tableMapping.getId());
                    tableMappingInfo.put("sourceTableName", tableMapping.getSourceTableName());
                    tableMappingInfo.put("targetTableName", tableMapping.getTargetTableName());
                    
                    // 查询字段映射记录
                    List<FieldMappingRecord> fieldMappings = fieldMappingRecordMapper.selectByTableMappingRecordId(tableMapping.getId());
                    
                    List<Map<String, Object>> fieldMappingDetails = new ArrayList<>();
                    for (FieldMappingRecord fieldMapping : fieldMappings) {
                        Map<String, Object> fieldInfo = new HashMap<>();
                        fieldInfo.put("position", fieldMapping.getFieldOrderPosition());
                        fieldInfo.put("sourceFieldName", fieldMapping.getSourceFieldName());
                        fieldInfo.put("sourceFieldType", fieldMapping.getSourceFieldType());
                        fieldInfo.put("sourceIsPrimary", fieldMapping.getSourceFieldIsPrimary());
                        fieldInfo.put("targetFieldName", fieldMapping.getTargetFieldName());
                        fieldInfo.put("targetFieldType", fieldMapping.getTargetFieldType());
                        fieldInfo.put("targetIsPrimary", fieldMapping.getTargetFieldIsPrimary());
                        fieldInfo.put("mappingRule", fieldMapping.getMappingRule());
                        fieldInfo.put("conversionNotes", fieldMapping.getConversionNotes());
                        
                        fieldMappingDetails.add(fieldInfo);
                    }
                    
                    // 按位置排序
                    fieldMappingDetails.sort((a, b) -> 
                        Integer.compare((Integer) a.get("position"), (Integer) b.get("position")));
                    
                    tableMappingInfo.put("fieldMappings", fieldMappingDetails);
                    tableMappingInfo.put("fieldCount", fieldMappingDetails.size());
                    
                    tableMappingDetails.add(tableMappingInfo);
                }
                
                recordInfo.put("tableMappings", tableMappingDetails);
                recordDetails.add(recordInfo);
            }
            
            response.put("migrationRecords", recordDetails);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("测试字段映射失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试字段映射失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询目标表详细信息（新增）
     * 根据迁移批次ID查询目标MySQL表的完整信息
     */
    @GetMapping("/target-table-detail/{migrationBatchId}")
    public ResponseEntity<Map<String, Object>> getTargetTableDetail(@PathVariable String migrationBatchId) {
        try {
            Long batchId = Long.parseLong(migrationBatchId);
            MigrationRecord migrationRecord = migrationMappingService.getMigrationRecordWithDetails(batchId);
            
            if (migrationRecord == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "未找到对应的迁移记录");
                return ResponseEntity.ok(response);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            
            Map<String, Object> targetTableInfo = new HashMap<>();
            
            // 基本迁移信息
            targetTableInfo.put("migrationBatchId", migrationRecord.getMigrationBatchId());
            targetTableInfo.put("sourceDatabaseName", migrationRecord.getSourceDatabaseName());
            targetTableInfo.put("targetDatabaseName", migrationRecord.getTargetDatabaseName());
            targetTableInfo.put("migrationStatus", migrationRecord.getMigrationStatus());
            targetTableInfo.put("migrationStartTime", migrationRecord.getMigrationStartTime());
            targetTableInfo.put("migrationEndTime", migrationRecord.getMigrationEndTime());
            
            // 目标表详细信息
            if (migrationRecord.getTableMappingRecords() != null && !migrationRecord.getTableMappingRecords().isEmpty()) {
                TableMappingRecord tableMapping = migrationRecord.getTableMappingRecords().get(0); // 假设一次迁移一个表
                
                Map<String, Object> targetTableDetails = new HashMap<>();
                targetTableDetails.put("tableName", tableMapping.getTargetTableName());
                targetTableDetails.put("tableComment", tableMapping.getTargetTableComment());
                targetTableDetails.put("ddlStatement", tableMapping.getTargetDdlStatement());
                targetTableDetails.put("recordCount", tableMapping.getTargetRecordCount());
                
                // 目标字段详细信息
                if (tableMapping.getFieldMappingRecords() != null) {
                    List<Map<String, Object>> targetFields = tableMapping.getFieldMappingRecords().stream()
                        .filter(field -> field.getTargetFieldName() != null) // 只显示成功映射的字段
                        .map(field -> {
                            Map<String, Object> fieldInfo = new HashMap<>();
                            // 目标字段信息
                            fieldInfo.put("fieldName", field.getTargetFieldName());
                            fieldInfo.put("fieldType", field.getTargetFieldType());
                            fieldInfo.put("fieldComment", field.getTargetFieldComment());
                            fieldInfo.put("nullable", field.getTargetFieldNullable());
                            fieldInfo.put("defaultValue", field.getTargetFieldDefaultValue());
                            fieldInfo.put("isPrimary", field.getTargetFieldIsPrimary());
                            fieldInfo.put("isUnique", field.getTargetFieldIsUnique());
                            fieldInfo.put("position", field.getFieldOrderPosition());
                            fieldInfo.put("mappingRule", field.getMappingRule());
                            fieldInfo.put("conversionNotes", field.getConversionNotes());
                            
                            // 源字段信息（用于对比）
                            fieldInfo.put("sourceFieldName", field.getSourceFieldName());
                            fieldInfo.put("sourceFieldType", field.getSourceFieldType());
                            fieldInfo.put("sourceFieldComment", field.getSourceFieldComment());
                            
                            // 添加一些额外的布尔值处理，确保前端能正确识别
                            fieldInfo.put("isPrimary", Boolean.TRUE.equals(field.getTargetFieldIsPrimary()));
                            fieldInfo.put("isUnique", Boolean.TRUE.equals(field.getTargetFieldIsUnique()));
                            fieldInfo.put("nullable", !Boolean.FALSE.equals(field.getTargetFieldNullable()));
                            
                            return fieldInfo;
                        })
                        .sorted((a, b) -> {
                            Integer posA = (Integer) a.get("position");
                            Integer posB = (Integer) b.get("position");
                            if (posA == null) posA = 0;
                            if (posB == null) posB = 0;
                            return Integer.compare(posA, posB);
                        })
                        .toList();
                    
                    targetTableDetails.put("fields", targetFields);
                    targetTableDetails.put("fieldCount", targetFields.size());
                }
                
                targetTableInfo.put("targetTable", targetTableDetails);
            }
            
            response.put("data", targetTableInfo);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询目标表详细信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 查询所有迁移记录的目标表信息列表（新增）
     */
    @GetMapping("/target-tables")
    public ResponseEntity<Map<String, Object>> getAllTargetTables(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        try {
            List<MigrationRecord> migrationRecords = migrationMappingService.getMigrationRecords(pageNum, pageSize);
            int total = migrationMappingService.getMigrationRecordCount();
            
            List<Map<String, Object>> targetTablesList = migrationRecords.stream()
                .map(record -> {
                    Map<String, Object> tableInfo = new HashMap<>();
                    tableInfo.put("migrationBatchId", record.getMigrationBatchId());
                    tableInfo.put("sourceDatabaseName", record.getSourceDatabaseName());
                    tableInfo.put("targetDatabaseName", record.getTargetDatabaseName());
                    tableInfo.put("migrationStatus", record.getMigrationStatus());
                    tableInfo.put("migrationTime", record.getMigrationStartTime());
                    
                    // 如果有表映射记录，添加目标表信息
                    if (record.getTableMappingRecords() != null && !record.getTableMappingRecords().isEmpty()) {
                        TableMappingRecord tableMapping = record.getTableMappingRecords().get(0);
                        tableInfo.put("sourceTableName", tableMapping.getSourceTableName());
                        tableInfo.put("targetTableName", tableMapping.getTargetTableName());
                        tableInfo.put("targetTableComment", tableMapping.getTargetTableComment());
                        tableInfo.put("targetRecordCount", tableMapping.getTargetRecordCount());
                        tableInfo.put("fieldCount", tableMapping.getFieldCount());
                    }
                    
                    return tableInfo;
                })
                .toList();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", targetTablesList);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询目标表列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 测试字段映射修复效果
     */
    @GetMapping("/test-field-mapping-fix/{sourceTableName}")
    public ResponseEntity<Map<String, Object>> testFieldMappingFix(@PathVariable String sourceTableName) {
        try {
            logger.info("🔧 测试字段映射修复效果: {}", sourceTableName);
            
            Map<String, Object> result = new HashMap<>();
            
            // 1. 获取源表名对应的迁移记录
            List<MigrationRecord> migrationRecords = migrationMappingService.getMigrationRecordsBySourceTable(sourceTableName);
            if (migrationRecords.isEmpty()) {
                result.put("error", "未找到源表名为 " + sourceTableName + " 的迁移记录");
                return ResponseEntity.ok(result);
            }
            
            MigrationRecord latestRecord = migrationRecords.get(0);
            logger.info("📋 找到迁移记录: ID={}, 批次ID={}", latestRecord.getId(), latestRecord.getMigrationBatchId());
            
            // 2. 获取表映射记录
            List<TableMappingRecord> tableMappingRecords = tableMappingRecordMapper.selectByMigrationRecordId(latestRecord.getId());
            if (tableMappingRecords.isEmpty()) {
                result.put("error", "未找到对应的表映射记录");
                return ResponseEntity.ok(result);
            }
            
            TableMappingRecord tableMapping = tableMappingRecords.get(0);
            logger.info("📊 找到表映射记录: ID={}, 源表={}, 目标表={}", 
                       tableMapping.getId(), tableMapping.getSourceTableName(), tableMapping.getTargetTableName());
            
            // 3. 获取字段映射记录
            List<FieldMappingRecord> fieldMappingRecords = fieldMappingRecordMapper.selectByTableMappingRecordId(tableMapping.getId());
            
            // 4. 分析字段映射结果
            Map<String, Object> analysisResult = analyzeFieldMappings(fieldMappingRecords);
            
            result.put("success", true);
            result.put("migrationRecord", latestRecord);
            result.put("tableMapping", tableMapping);
            result.put("fieldMappings", fieldMappingRecords);
            result.put("analysis", analysisResult);
            
            logger.info("✅ 字段映射测试完成，共 {} 个字段映射记录", fieldMappingRecords.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("测试字段映射失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "测试失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }
    
    /**
     * 分析字段映射结果
     */
    private Map<String, Object> analyzeFieldMappings(List<FieldMappingRecord> fieldMappings) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 统计信息
        int totalMappings = fieldMappings.size();
        long systemFields = fieldMappings.stream()
            .filter(f -> "__SYSTEM_GENERATED__".equals(f.getSourceFieldName()))
            .count();
        long normalMappings = fieldMappings.stream()
            .filter(f -> !"__SYSTEM_GENERATED__".equals(f.getSourceFieldName()))
            .count();
        
        // 检查重复的目标字段名
        Map<String, Long> targetFieldCounts = fieldMappings.stream()
            .collect(Collectors.groupingBy(
                FieldMappingRecord::getTargetFieldName,
                Collectors.counting()
            ));
        
        List<String> duplicateTargetFields = targetFieldCounts.entrySet().stream()
            .filter(entry -> entry.getValue() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        
        // 检查目标字段名为空或null
        long nullTargetFields = fieldMappings.stream()
            .filter(f -> f.getTargetFieldName() == null || f.getTargetFieldName().trim().isEmpty())
            .count();
        
        analysis.put("totalMappings", totalMappings);
        analysis.put("systemFields", systemFields);
        analysis.put("normalMappings", normalMappings);
        analysis.put("duplicateTargetFields", duplicateTargetFields);
        analysis.put("nullTargetFields", nullTargetFields);
        analysis.put("targetFieldCounts", targetFieldCounts);
        
        // 验证结果
        boolean hasIssues = !duplicateTargetFields.isEmpty() || nullTargetFields > 0;
        analysis.put("hasIssues", hasIssues);
        
        if (hasIssues) {
            List<String> issues = new ArrayList<>();
            if (!duplicateTargetFields.isEmpty()) {
                issues.add("检测到重复的目标字段: " + duplicateTargetFields);
            }
            if (nullTargetFields > 0) {
                issues.add("检测到 " + nullTargetFields + " 个空的目标字段");
            }
            analysis.put("issues", issues);
        }
        
        return analysis;
    }

    /**
     * 调试API：检查表映射记录的迁移记录ID关联
     */
    @GetMapping("/debug-table-mapping/{migrationRecordId}")
    public ResponseEntity<Map<String, Object>> debugTableMapping(@PathVariable String migrationRecordId) {
        try {
            Long recordId = Long.parseLong(migrationRecordId);
            
            Map<String, Object> debugInfo = new HashMap<>();
            
            // 查询迁移记录
            MigrationRecord migrationRecord = migrationMappingService.getMigrationRecordWithDetails(Long.valueOf("463884108586684416"));
            debugInfo.put("migrationRecord", migrationRecord);
            
            if (migrationRecord != null) {
                debugInfo.put("migrationRecordId", migrationRecord.getId());
                debugInfo.put("migrationBatchId", migrationRecord.getMigrationBatchId());
                
                // 查询所有表映射记录（不按迁移记录ID过滤）
                List<TableMappingRecord> allTableMappings = tableMappingRecordMapper.selectByMigrationBatchId(migrationRecord.getMigrationBatchId());
                debugInfo.put("allTableMappingsCount", allTableMappings.size());
                debugInfo.put("allTableMappings", allTableMappings);
                
                // 查询通过迁移记录ID的表映射记录
                List<TableMappingRecord> tableMappingsByRecordId = tableMappingRecordMapper.selectByMigrationRecordId(recordId);
                debugInfo.put("tableMappingsByRecordIdCount", tableMappingsByRecordId.size());
                debugInfo.put("tableMappingsByRecordId", tableMappingsByRecordId);
                
                // 查询通过正确的迁移记录ID的表映射记录
                List<TableMappingRecord> tableMappingsByCorrectId = tableMappingRecordMapper.selectByMigrationRecordId(migrationRecord.getId());
                debugInfo.put("tableMappingsByCorrectIdCount", tableMappingsByCorrectId.size());
                debugInfo.put("tableMappingsByCorrectId", tableMappingsByCorrectId);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", debugInfo);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("调试API失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除迁移记录及其所有关联数据
     */
    @DeleteMapping("/records/{id}")
    public ResponseEntity<Map<String, Object>> deleteMigrationRecord(@PathVariable Long id) {
        try {
            String deleteMsg = String.format("🗑️ 用户请求删除迁移记录: ID=%d", id);
            logger.info(deleteMsg);
            LogWebSocketController.addLog("INFO", "MigrationHistoryController", deleteMsg);

            String result = migrationMappingService.deleteMigrationRecordWithCascade(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", result);

            String successMsg = String.format("✅ 迁移记录删除成功: ID=%d", id);
            LogWebSocketController.addLog("INFO", "MigrationHistoryController", successMsg);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            String errorMsg = String.format("❌ 删除迁移记录失败: ID=%d, 错误: %s", id, e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "MigrationHistoryController", errorMsg);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除迁移记录失败: " + e.getMessage());

            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 批量删除迁移记录
     */
    @DeleteMapping("/records/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteMigrationRecords(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");

            if (ids == null || ids.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "请选择要删除的记录");
                return ResponseEntity.badRequest().body(response);
            }

            String batchDeleteMsg = String.format("🗑️ 用户请求批量删除迁移记录: %d 条", ids.size());
            logger.info(batchDeleteMsg);
            LogWebSocketController.addLog("INFO", "MigrationHistoryController", batchDeleteMsg);

            int successCount = 0;
            int failCount = 0;
            StringBuilder resultMsg = new StringBuilder();

            for (Long id : ids) {
                try {
                    migrationMappingService.deleteMigrationRecordWithCascade(id);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    logger.error("删除迁移记录失败: ID={}", id, e);
                }
            }

            resultMsg.append(String.format("批量删除完成: 成功 %d 条, 失败 %d 条", successCount, failCount));

            Map<String, Object> response = new HashMap<>();
            response.put("success", failCount == 0);
            response.put("message", resultMsg.toString());
            response.put("successCount", successCount);
            response.put("failCount", failCount);

            String batchResultMsg = String.format("✅ 批量删除完成: 成功 %d 条, 失败 %d 条", successCount, failCount);
            LogWebSocketController.addLog("INFO", "MigrationHistoryController", batchResultMsg);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            String errorMsg = String.format("❌ 批量删除迁移记录失败: %s", e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "MigrationHistoryController", errorMsg);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量删除失败: " + e.getMessage());

            return ResponseEntity.status(500).body(response);
        }
    }
}