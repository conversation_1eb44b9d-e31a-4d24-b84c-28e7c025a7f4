package com.liuyang.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库升级控制器（临时使用）
 * 用于执行雪花算法迁移脚本
 */
@RestController
@RequestMapping("/api/database-upgrade")
public class DatabaseUpgradeController {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseUpgradeController.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 执行雪花算法迁移脚本
     */
    @PostMapping("/snowflake-migration")
    public Map<String, Object> executeSnowflakeMigration() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始执行雪花算法迁移脚本...");
            
            // Step 1: 删除现有索引
            logger.info("Step 1: 删除现有索引");
            executeSQL("DROP INDEX IF EXISTS idx_migration_batch_id ON migration_records");
            executeSQL("DROP INDEX IF EXISTS idx_migration_batch_id ON table_mapping_records");
            executeSQL("DROP INDEX IF EXISTS idx_migration_batch_id ON field_mapping_records");
            executeSQL("DROP INDEX IF EXISTS idx_composite_table ON field_mapping_records");
            
            // Step 2: 修改字段类型为BIGINT
            logger.info("Step 2: 修改字段类型为BIGINT");
            executeSQL("ALTER TABLE migration_records MODIFY COLUMN migration_batch_id BIGINT NOT NULL UNIQUE COMMENT '迁移批次ID（雪花算法）'");
            executeSQL("ALTER TABLE table_mapping_records MODIFY COLUMN migration_batch_id BIGINT NOT NULL COMMENT '迁移批次ID（雪花算法）'");
            executeSQL("ALTER TABLE field_mapping_records MODIFY COLUMN migration_batch_id BIGINT NOT NULL COMMENT '迁移批次ID（雪花算法）'");
            
            // Step 3: 重建索引
            logger.info("Step 3: 重建索引");
            executeSQL("CREATE INDEX idx_migration_batch_id ON migration_records (migration_batch_id)");
            executeSQL("CREATE INDEX idx_migration_batch_id ON table_mapping_records (migration_batch_id)");
            executeSQL("CREATE INDEX idx_migration_batch_id ON field_mapping_records (migration_batch_id)");
            executeSQL("CREATE INDEX idx_composite_table ON field_mapping_records (migration_batch_id, source_table_name, target_table_name)");
            
            // Step 4: 验证表结构
            logger.info("Step 4: 验证表结构");
            List<Map<String, Object>> columnInfo = jdbcTemplate.queryForList(
                "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_COMMENT " +
                "FROM INFORMATION_SCHEMA.COLUMNS " +
                "WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'migration_batch_id' " +
                "ORDER BY TABLE_NAME"
            );
            
            List<Map<String, Object>> indexInfo = jdbcTemplate.queryForList(
                "SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME, NON_UNIQUE " +
                "FROM INFORMATION_SCHEMA.STATISTICS " +
                "WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'migration_batch_id' " +
                "ORDER BY TABLE_NAME, INDEX_NAME"
            );
            
            result.put("success", true);
            result.put("message", "雪花算法迁移执行成功！");
            result.put("columnInfo", columnInfo);
            result.put("indexInfo", indexInfo);
            result.put("status", "UUID to Snowflake migration completed successfully!");
            
            logger.info("雪花算法迁移脚本执行完成！");
            
        } catch (Exception e) {
            logger.error("雪花算法迁移脚本执行失败", e);
            result.put("success", false);
            result.put("message", "迁移失败: " + e.getMessage());
            result.put("error", e.toString());
        }
        
        return result;
    }
    
    /**
     * 检查当前字段类型状态
     */
    @GetMapping("/check-schema")
    public Map<String, Object> checkCurrentSchema() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> columnInfo = jdbcTemplate.queryForList(
                "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_COMMENT " +
                "FROM INFORMATION_SCHEMA.COLUMNS " +
                "WHERE TABLE_SCHEMA = DATABASE() AND COLUMN_NAME = 'migration_batch_id' " +
                "ORDER BY TABLE_NAME"
            );
            
            result.put("success", true);
            result.put("columnInfo", columnInfo);
            
        } catch (Exception e) {
            logger.error("检查表结构失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    private void executeSQL(String sql) {
        try {
            logger.debug("执行SQL: {}", sql);
            jdbcTemplate.execute(sql);
        } catch (Exception e) {
            logger.warn("SQL执行警告 [{}]: {}", sql, e.getMessage());
            // 某些SQL可能因为索引不存在等原因失败，这是正常的
        }
    }
} 