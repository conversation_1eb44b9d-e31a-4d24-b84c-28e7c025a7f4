package com.liuyang.controller;

import com.liuyang.annotation.OperationLog;
import com.liuyang.dto.CopyTableRequest;
import com.liuyang.service.CrossDatabaseTableCopyService;
import com.liuyang.service.TableCopyService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 复制控制器
 */
@RestController
@RequestMapping("/api/copy")
public class CopyController {
    
    private static final Logger logger = LoggerFactory.getLogger(CopyController.class);
    
    @Autowired(required = false)
    private TableCopyService tableCopyService;
    
    @Autowired(required = false)
    private CrossDatabaseTableCopyService crossDatabaseTableCopyService;
    
    /**
     * 复制表（自动检测是否跨数据库）
     */
    @PostMapping("/table")
    @OperationLog(operationType = "DATABASE_MIGRATION",
                 operationDesc = "数据库表迁移: #{#request.sourceDatabase}.#{#request.tableName} -> #{#request.targetDatabase}.#{#request.targetTableName}",
                 targetType = "TABLE", targetId = "#{#request.sourceDatabase}.#{#request.tableName}")
    public ResponseEntity<Map<String, Object>> copyTable(@Valid @RequestBody CopyTableRequest request) {
        logger.info("收到复制表请求: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        try {
            // 根据是否跨数据库选择不同的服务
            if (request.isCrossDatabaseMigration()) {
                if (crossDatabaseTableCopyService == null) {
                    throw new RuntimeException("跨数据库复制服务当前不可用");
                }
                crossDatabaseTableCopyService.crossDatabaseCopyTable(request);
                response.put("message", "跨数据库表复制成功");
            } else {
                if (tableCopyService == null) {
                    throw new RuntimeException("表复制服务当前不可用");
                }
            tableCopyService.copyTable(request);
                response.put("message", "表复制成功");
            }
            
            response.put("success", true);
            response.put("data", Map.of(
                "sourceDatabase", request.getSourceDatabase(),
                "targetDatabase", request.getTargetDatabase(),
                "sourceTable", request.getTableName(),
                "targetTable", request.getTargetTableName(),
                "copyData", request.isCopyData(),
                "crossDatabase", request.isCrossDatabaseMigration(),
                "useDualPrimaryKey", request.isUseDualPrimaryKey()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("复制表失败", e);
            response.put("success", false);
            response.put("message", "复制表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 跨数据库复制表（专用接口）
     */
    @PostMapping("/table/cross-database")
    public ResponseEntity<Map<String, Object>> crossDatabaseCopyTable(@Valid @RequestBody CopyTableRequest request) {
        logger.info("收到跨数据库复制表请求: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        try {
            if (crossDatabaseTableCopyService == null) {
                throw new RuntimeException("跨数据库复制服务当前不可用");
            }
            crossDatabaseTableCopyService.crossDatabaseCopyTable(request);
            response.put("success", true);
            response.put("message", "跨数据库表复制成功");
            response.put("data", Map.of(
                "sourceDatabase", request.getSourceDatabase(),
                "targetDatabase", request.getTargetDatabase(),
                "sourceTable", request.getTableName(),
                "targetTable", request.getTargetTableName(),
                "copyData", request.isCopyData(),
                "sourceDbType", request.getSourceDbType().getDisplayName(),
                "targetDbType", request.getTargetDbType().getDisplayName(),
                "useDualPrimaryKey", request.isUseDualPrimaryKey(),
                "businessKeyFieldName", request.getBusinessKeyFieldName(),
                "newPrimaryKeyFieldName", request.getNewPrimaryKeyFieldName()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("跨数据库复制表失败", e);
            response.put("success", false);
            response.put("message", "跨数据库复制表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 预览复制操作（不实际执行）
     */
    @PostMapping("/table/preview")
    public ResponseEntity<Map<String, Object>> previewCopyTable(@Valid @RequestBody CopyTableRequest request) {
        logger.info("预览复制表操作: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        try {
            // 这里可以添加预览逻辑，比如检查源表和目标数据库的兼容性
            Map<String, Object> previewData = new HashMap<>();
            previewData.put("sourceDatabase", request.getSourceDatabase());
            previewData.put("targetDatabase", request.getTargetDatabase());
            previewData.put("sourceTable", request.getTableName());
            previewData.put("targetTable", request.getTargetTableName());
            previewData.put("copyData", request.isCopyData());
            previewData.put("dropIfExists", request.isDropIfExists());
            
            response.put("success", true);
            response.put("data", previewData);
            response.put("message", "预览成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("预览复制操作失败", e);
            response.put("success", false);
            response.put("message", "预览失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
