package com.liuyang.controller;

import com.liuyang.annotation.OperationLog;
import com.liuyang.service.OperationLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/operation-logs")
public class OperationLogController {

    private static final Logger logger = LoggerFactory.getLogger(OperationLogController.class);

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @GetMapping("/list")
    @OperationLog(operationType = "LOG_QUERY", operationDesc = "查询操作日志列表", targetType = "LOG")
    public ResponseEntity<Map<String, Object>> getLogsList(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize) {
        
        try {
            logger.info("查询操作日志列表: userId={}, username={}, operationType={}, startTime={}, endTime={}, page={}, pageSize={}",
                       userId, username, operationType, startTime, endTime, page, pageSize);

            // 转换时间格式
            LocalDateTime parsedStartTime = parseDateTime(startTime);
            LocalDateTime parsedEndTime = parseDateTime(endTime);

            Map<String, Object> result = operationLogService.getLogsByPage(
                userId, username, operationType, parsedStartTime, parsedEndTime, page, pageSize);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("查询操作日志列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 根据ID查询操作日志详情
     */
    @GetMapping("/{id}")
    @OperationLog(operationType = "LOG_DETAIL", operationDesc = "查询操作日志详情", 
                 targetType = "LOG", targetId = "#{#id}")
    public ResponseEntity<Map<String, Object>> getLogDetail(@PathVariable Long id) {
        try {
            logger.info("查询操作日志详情: id={}", id);

            com.liuyang.entity.OperationLog log = operationLogService.getLogById(id);
            
            Map<String, Object> response = new HashMap<>();
            if (log != null) {
                response.put("success", true);
                response.put("data", log);
            } else {
                response.put("success", false);
                response.put("message", "日志不存在");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询操作日志详情失败: id={}", id, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取简单统计信息
     */
    @GetMapping("/simple-stats")
    public ResponseEntity<Map<String, Object>> getSimpleStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取今日开始时间
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime now = LocalDateTime.now();

            // 获取最近7天开始时间
            LocalDateTime weekStart = now.minusDays(7);

            // 查询统计数据 - 使用现有的分页查询方法来获取计数
            Map<String, Object> totalResult = operationLogService.getLogsByPage(null, null, null, weekStart, now, 1, 1);
            long totalLogs = (Long) totalResult.get("total");

            Map<String, Object> todayResult = operationLogService.getLogsByPage(null, null, null, todayStart, now, 1, 1);
            long todayLogs = (Long) todayResult.get("total");

            // 查询活跃用户数量
            List<Map<String, Object>> userStats = operationLogService.getUserOperationStats(weekStart, now);
            int activeUsers = userStats != null ? userStats.size() : 0;

            stats.put("totalLogs", totalLogs);
            stats.put("todayLogs", todayLogs);
            stats.put("errorLogs", 0); // 暂时设为0，因为需要特殊查询
            stats.put("activeUsers", activeUsers);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询简单统计失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取操作日志统计信息
     */
    @GetMapping("/statistics")
    @OperationLog(operationType = "LOG_STATISTICS", operationDesc = "查询操作日志统计", targetType = "LOG")
    public ResponseEntity<Map<String, Object>> getLogStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        try {
            // 默认查询最近7天的统计
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(7);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }

            logger.info("查询操作日志统计: startTime={}, endTime={}", startTime, endTime);

            Map<String, Object> statistics = new HashMap<>();
            
            // 操作类型统计
            List<Map<String, Object>> operationTypeStats = 
                operationLogService.getOperationTypeStats(startTime, endTime);
            statistics.put("operationTypeStats", operationTypeStats);
            
            // 用户操作统计
            List<Map<String, Object>> userOperationStats = 
                operationLogService.getUserOperationStats(startTime, endTime);
            statistics.put("userOperationStats", userOperationStats);
            
            // 每日操作统计
            List<Map<String, Object>> dailyOperationStats = 
                operationLogService.getDailyOperationStats(startTime, endTime);
            statistics.put("dailyOperationStats", dailyOperationStats);
            
            // 错误统计
            Map<String, Object> errorStats = 
                operationLogService.getErrorStats(startTime, endTime);
            statistics.put("errorStats", errorStats);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);
            response.put("timeRange", Map.of("startTime", startTime, "endTime", endTime));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询操作日志统计失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询统计失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取最近的操作日志
     */
    @GetMapping("/recent")
    @OperationLog(operationType = "LOG_RECENT", operationDesc = "查询最近操作日志", targetType = "LOG")
    public ResponseEntity<Map<String, Object>> getRecentLogs(
            @RequestParam(defaultValue = "50") int limit) {
        
        try {
            logger.info("查询最近操作日志: limit={}", limit);

            List<com.liuyang.entity.OperationLog> logs = operationLogService.getRecentLogs(limit);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", logs);
            response.put("total", logs.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询最近操作日志失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 清理过期日志
     */
    @DeleteMapping("/cleanup")
    @OperationLog(operationType = "LOG_CLEANUP", operationDesc = "清理过期操作日志", targetType = "LOG")
    public ResponseEntity<Map<String, Object>> cleanupExpiredLogs(
            @RequestParam(defaultValue = "30") int daysToKeep) {
        
        try {
            logger.info("清理过期操作日志: daysToKeep={}", daysToKeep);

            LocalDateTime beforeTime = LocalDateTime.now().minusDays(daysToKeep);
            int deletedCount = operationLogService.cleanExpiredLogs(beforeTime);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", String.format("成功清理 %d 条过期日志", deletedCount));
            response.put("deletedCount", deletedCount);
            response.put("beforeTime", beforeTime);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清理过期操作日志失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 解析时间字符串
     * 支持多种时间格式：
     * - yyyy-MM-ddTHH:mm (HTML datetime-local格式)
     * - yyyy-MM-dd HH:mm:ss
     * - yyyy-MM-dd HH:mm
     * - yyyy-MM-dd (自动补充时间部分)
     */
    private LocalDateTime parseDateTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }

        timeStr = timeStr.trim();

        try {
            // 尝试解析 HTML datetime-local 格式: yyyy-MM-ddTHH:mm
            if (timeStr.contains("T")) {
                if (timeStr.length() == 16) {
                    return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm"));
                } else if (timeStr.length() == 19) {
                    return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                }
            }

            // 尝试解析完整时间格式: yyyy-MM-dd HH:mm:ss
            if (timeStr.length() == 19) {
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            // 尝试解析时间格式: yyyy-MM-dd HH:mm
            if (timeStr.length() == 16) {
                return LocalDateTime.parse(timeStr + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            // 尝试解析日期格式: yyyy-MM-dd (补充时间为00:00:00)
            if (timeStr.length() == 10) {
                return LocalDateTime.parse(timeStr + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }

            // 如果都不匹配，尝试ISO格式
            return LocalDateTime.parse(timeStr);

        } catch (DateTimeParseException e) {
            logger.warn("无法解析时间字符串: {}, 错误: {}", timeStr, e.getMessage());
            return null;
        }
    }
}
