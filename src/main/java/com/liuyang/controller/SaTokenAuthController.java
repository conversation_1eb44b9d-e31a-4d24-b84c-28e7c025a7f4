package com.liuyang.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.liuyang.annotation.OperationLog;
import com.liuyang.entity.User;
import com.liuyang.service.InvitationCodeService;
import com.liuyang.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Sa-Token版本的认证控制器
 */
@RestController
@RequestMapping("/api/satoken")
public class SaTokenAuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(SaTokenAuthController.class);
    
    @Autowired
    private UserService userService;

    @Autowired
    private InvitationCodeService invitationCodeService;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    @OperationLog(operationType = "USER_LOGIN", operationDesc = "用户登录",
                 targetType = "USER", targetId = "#{#loginRequest['username']}")
    public ResponseEntity<Map<String, Object>> login(
            @RequestBody Map<String, String> loginRequest,
            HttpServletRequest request) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = loginRequest.get("username");
            String password = loginRequest.get("password");
            
            // 参数验证
            if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
                result.put("success", false);
                result.put("message", "用户名和密码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询用户
            User user = userService.getUserByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户名或密码错误");
                return ResponseEntity.ok(result);
            }
            
            // 检查用户状态
            if (!user.isEnabled()) {
                result.put("success", false);
                result.put("message", "用户已被禁用");
                return ResponseEntity.ok(result);
            }
            
            // 验证密码
            if (!passwordEncoder.matches(password, user.getPassword())) {
                result.put("success", false);
                result.put("message", "用户名或密码错误");
                return ResponseEntity.ok(result);
            }
            
            // Sa-Token登录
            StpUtil.login(user.getId());
            
            // 在Session中存储用户信息
            StpUtil.getSession().set("user", user);
            
            // 更新用户最后登录信息
            String clientIp = getClientIp(request);
            userService.updateLastLogin(user.getId(), clientIp);
            
            // 获取Token
            String token = StpUtil.getTokenValue();
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("token", token);
            result.put("user", user);
            
            logger.info("✅ 用户登录成功: {} ({}) - IP: {}", 
                       user.getUsername(), user.getDisplayName(), clientIp);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 登录处理异常", e);
            result.put("success", false);
            result.put("message", "登录失败，请稍后重试");
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    @OperationLog(operationType = "USER_REGISTER", operationDesc = "用户注册",
                 targetType = "USER", targetId = "#{#registerRequest['username']}")
    public ResponseEntity<Map<String, Object>> register(
            @RequestBody Map<String, String> registerRequest) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = registerRequest.get("username");
            String password = registerRequest.get("password");
            String realName = registerRequest.get("realName");
            String invitationCode = registerRequest.get("invitationCode");

            // 参数验证
            if (!StringUtils.hasText(username) || !StringUtils.hasText(password) ||
                !StringUtils.hasText(realName) || !StringUtils.hasText(invitationCode)) {
                result.put("success", false);
                result.put("message", "用户名、密码、真实姓名和邀请码不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 检查用户名是否已存在
            if (userService.isUsernameExists(username)) {
                result.put("success", false);
                result.put("message", "用户名已存在，请选择其他用户名");
                return ResponseEntity.ok(result);
            }

            // 验证邀请码
            if (!invitationCodeService.validateInvitationCode(invitationCode)) {
                result.put("success", false);
                result.put("message", "邀请码无效或已过期，请联系管理员获取有效邀请码");
                return ResponseEntity.ok(result);
            }

            // 创建用户对象
            User newUser = User.builder()
                    .username(username)
                    .password(password) // 在service中会被加密
                    .realName(realName)
                    .role(User.Role.USER.getCode())
                    .status(User.Status.ENABLED.getCode())
                    .build();
            
            // 创建用户
            User createdUser = userService.createUser(newUser, "system");

            // 使用邀请码（增加使用次数）
            boolean codeUsed = invitationCodeService.useInvitationCode(invitationCode, username);
            if (!codeUsed) {
                logger.warn("⚠️ 邀请码使用失败，但用户已创建: {}", username);
            }

            result.put("success", true);
            result.put("message", "注册成功，请使用新账号登录");
            result.put("user", createdUser);

            logger.info("✅ 用户注册成功: {} ({}) - 邀请码: {}",
                       createdUser.getUsername(), createdUser.getDisplayName(),
                       codeUsed ? "已使用" : "使用失败");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 用户注册异常", e);
            result.put("success", false);
            result.put("message", "注册失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @OperationLog(operationType = "USER_LOGOUT", operationDesc = "用户登出", targetType = "USER")
    public ResponseEntity<Map<String, Object>> logout() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Sa-Token登出
            StpUtil.logout();
            
            result.put("success", true);
            result.put("message", "登出成功");
            
            logger.info("✅ 用户登出成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 登出处理异常", e);
            result.put("success", false);
            result.put("message", "登出失败");
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/current-user")
    public ResponseEntity<Map<String, Object>> getCurrentUser(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 手动检查Token
            String token = request.getHeader("satoken");
            if (!StringUtils.hasText(token)) {
                result.put("success", false);
                result.put("message", "未提供Token");
                return ResponseEntity.ok(result);
            }

            // 验证Token并获取登录ID
            Long loginId;
            try {
                Object loginIdObj = StpUtil.getLoginIdByToken(token);
                loginId = Long.valueOf(loginIdObj.toString());
            } catch (Exception e) {
                result.put("success", false);
                result.put("message", "Token无效");
                return ResponseEntity.ok(result);
            }

            // 从数据库获取用户信息
            User user = userService.getUserById(loginId);

            if (user == null) {
                result.put("success", false);
                result.put("message", "用户信息不存在");
                return ResponseEntity.ok(result);
            }

            result.put("success", true);
            result.put("user", user);
            result.put("loginId", loginId);
            result.put("token", token);

            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 获取当前用户信息异常", e);
            result.put("success", false);
            result.put("message", "获取用户信息失败");
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 获取Token信息
     */
    @GetMapping("/token-info")
    public ResponseEntity<Map<String, Object>> getTokenInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!StpUtil.isLogin()) {
                result.put("success", false);
                result.put("message", "未登录");
                return ResponseEntity.ok(result);
            }
            
            result.put("success", true);
            result.put("tokenInfo", StpUtil.getTokenInfo());
            result.put("loginId", StpUtil.getLoginId());
            result.put("tokenValue", StpUtil.getTokenValue());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 获取Token信息异常", e);
            result.put("success", false);
            result.put("message", "获取Token信息失败");
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
