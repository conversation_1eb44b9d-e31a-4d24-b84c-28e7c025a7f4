package com.liuyang.controller;

import com.liuyang.dto.DatabaseType;
import com.liuyang.service.DatabaseConnectionTestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库连接测试控制器
 */
@RestController
@RequestMapping("/api/database")

public class DatabaseTestController {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseTestController.class);

    @Autowired(required = false)
    private DataSource dataSource;

    @Autowired(required = false)
    private DatabaseConnectionTestService databaseConnectionTestService;

    /**
     * 测试数据库连接
     */
    @GetMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (dataSource == null) {
                response.put("success", false);
                response.put("message", "数据源未配置");
                return ResponseEntity.ok(response);
            }

            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                response.put("success", true);
                response.put("connected", true);
                response.put("databaseProductName", metaData.getDatabaseProductName());
                response.put("databaseProductVersion", metaData.getDatabaseProductVersion());
                response.put("driverName", metaData.getDriverName());
                response.put("driverVersion", metaData.getDriverVersion());
                response.put("url", metaData.getURL());
                response.put("userName", metaData.getUserName());
                
                logger.info("数据库连接测试成功: {}", metaData.getURL());
                
            }
        } catch (Exception e) {
            logger.error("数据库连接测试失败", e);
            response.put("success", false);
            response.put("connected", false);
            response.put("error", e.getMessage());
            response.put("message", "数据库连接失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取数据库表列表
     */
    @GetMapping("/tables")
    public ResponseEntity<Map<String, Object>> getTables() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (dataSource == null) {
                response.put("success", false);
                response.put("message", "数据源未配置");
                return ResponseEntity.ok(response);
            }

            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                // 获取表列表
                try (ResultSet tables = metaData.getTables(null, "public", "%", new String[]{"TABLE"})) {
                    Map<String, Object> tableList = new HashMap<>();
                    int count = 0;
                    
                    while (tables.next()) {
                        String tableName = tables.getString("TABLE_NAME");
                        String tableType = tables.getString("TABLE_TYPE");
                        String remarks = tables.getString("REMARKS");
                        
                        Map<String, Object> tableInfo = new HashMap<>();
                        tableInfo.put("type", tableType);
                        tableInfo.put("remarks", remarks);
                        
                        tableList.put(tableName, tableInfo);
                        count++;
                    }
                    
                    response.put("success", true);
                    response.put("tableCount", count);
                    response.put("tables", tableList);
                    
                    logger.info("获取数据库表列表成功，共 {} 个表", count);
                }
                
            }
        } catch (Exception e) {
            logger.error("获取数据库表列表失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "获取表列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 执行简单的SQL查询测试
     */
    @GetMapping("/test-query")
    public ResponseEntity<Map<String, Object>> testQuery() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (dataSource == null) {
                response.put("success", false);
                response.put("message", "数据源未配置");
                return ResponseEntity.ok(response);
            }

            try (Connection connection = dataSource.getConnection()) {
                // 执行简单的查询
                try (var statement = connection.createStatement();
                     var resultSet = statement.executeQuery("SELECT version(), current_database(), current_user")) {
                    
                    if (resultSet.next()) {
                        response.put("success", true);
                        response.put("postgresVersion", resultSet.getString(1));
                        response.put("currentDatabase", resultSet.getString(2));
                        response.put("currentUser", resultSet.getString(3));
                        
                        logger.info("数据库查询测试成功");
                    }
                }
                
            }
        } catch (Exception e) {
            logger.error("数据库查询测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "查询测试失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试跨数据库连接 - 所有数据库
     */
    @PostMapping("/test-cross-db")
    public ResponseEntity<Map<String, Object>> testCrossDbConnections() {
        logger.info("🔍 测试跨数据库连接请求");

        if (databaseConnectionTestService == null) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "数据库连接测试服务未可用"
            ));
        }

        try {
            Map<String, Object> result = databaseConnectionTestService.testAllConnections();

            if ((Boolean) result.get("allSuccess")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(500).body(result);
            }

        } catch (Exception e) {
            logger.error("测试跨数据库连接失败", e);
            return ResponseEntity.status(500).body(Map.of(
                "allSuccess", false,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 测试源数据库连接 (PostgreSQL)
     */
    @PostMapping("/test-source")
    public ResponseEntity<Map<String, Object>> testSourceConnection() {
        logger.info("🔍 测试源数据库连接请求");

        if (databaseConnectionTestService == null) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "数据库连接测试服务未可用"
            ));
        }

        try {
            Map<String, Object> result = databaseConnectionTestService.testConnection(
                DatabaseType.POSTGRESQL, "qxrtoc_fee");

            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(500).body(result);
            }

        } catch (Exception e) {
            logger.error("测试源数据库连接失败", e);
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 测试目标数据库连接 (MySQL)
     */
    @PostMapping("/test-target")
    public ResponseEntity<Map<String, Object>> testTargetConnection() {
        logger.info("🔍 测试目标数据库连接请求");

        if (databaseConnectionTestService == null) {
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "数据库连接测试服务未可用"
            ));
        }

        try {
            Map<String, Object> result = databaseConnectionTestService.testConnection(
                DatabaseType.MYSQL, "health_car");

            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(500).body(result);
            }

        } catch (Exception e) {
            logger.error("测试目标数据库连接失败", e);
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "测试失败: " + e.getMessage()
            ));
        }
    }
}
