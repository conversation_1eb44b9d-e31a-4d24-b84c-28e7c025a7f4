package com.liuyang.controller;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.EnhancedFieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.EnhancedFieldMappingService;
import com.liuyang.service.TableNameOptimizationService;
import com.liuyang.service.impl.EnhancedFieldMappingServiceImpl;
import com.liuyang.service.impl.TableNameOptimizationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强的字段映射控制器
 * 支持AI智能字段命名优化
 */
@RestController
@RequestMapping("/api/enhanced-field-mapping")

public class EnhancedFieldMappingController {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedFieldMappingController.class);

    @Autowired
    private EnhancedFieldMappingService enhancedFieldMappingService;

    @Autowired
    private TableNameOptimizationService tableNameOptimizationService;

    /**
     * 分析表字段映射（增强版）
     */
    @PostMapping("/analyze")
    public ResponseEntity<Map<String, Object>> analyzeFieldMappings(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 解析请求参数 - 支持直接表结构格式
            String businessContext = (String) request.get("businessContext");
            
            // 检查是否是直接表结构格式
            TableStructure table;
            if (request.containsKey("tableName") && request.containsKey("columns")) {
                // 直接表结构格式
                table = buildTableStructureDirectly(request);
            } else {
                // 嵌套格式
                @SuppressWarnings("unchecked")
                Map<String, Object> tableData = (Map<String, Object>) request.get("table");
                
                if (tableData == null) {
                    response.put("success", false);
                    response.put("message", "表结构数据不能为空");
                    return ResponseEntity.badRequest().body(response);
                }
                
                table = buildTableStructure(tableData);
            }
            
            if (table == null || table.getTableName() == null) {
                response.put("success", false);
                response.put("message", "表结构数据不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 优化表名
            TableNameOptimizationServiceImpl.TableNameOptimizationResult tableNameResult =
                tableNameOptimizationService.optimizeTableName(table.getTableName(), businessContext);
            
            // 分析字段映射
            List<EnhancedFieldMapping> mappings = enhancedFieldMappingService.analyzeFieldMappings(table, businessContext);
            
            // 获取统计信息
            EnhancedFieldMappingServiceImpl.FieldMappingStatistics stats =
                enhancedFieldMappingService.getStatistics(mappings);
            
            response.put("success", true);
            response.put("originalTableName", table.getTableName());
            response.put("optimizedTableName", tableNameResult.getOptimizedName());
            response.put("tableOptimization", tableNameResult);
            response.put("fieldMappings", mappings);
            response.put("statistics", stats);
            response.put("message", "字段映射分析完成");
            
            logger.info("增强字段映射分析完成: 表={}, 字段数={}, 优化率={:.2f}%", 
                       table.getTableName(), mappings.size(), stats.getOptimizationRate() * 100);
            
        } catch (Exception e) {
            logger.error("增强字段映射分析失败", e);
            response.put("success", false);
            response.put("message", "分析失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试单个字段优化
     */
    @PostMapping("/test-field")
    public ResponseEntity<Map<String, Object>> testFieldOptimization(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String fieldName = request.get("fieldName");
            String fieldType = request.get("fieldType");
            String comment = request.get("comment");
            String businessContext = request.get("businessContext");
            
            if (fieldName == null || fieldName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "字段名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 创建测试字段
            ColumnInfo testColumn = new ColumnInfo();
            testColumn.setColumnName(fieldName);
            testColumn.setDataType(fieldType != null ? fieldType : "VARCHAR");
            testColumn.setComment(comment);
            
            // 创建测试表结构
            TableStructure testTable = new TableStructure();
            testTable.setTableName("test_table");
            testTable.getColumns().add(testColumn);
            
            // 分析字段
            List<EnhancedFieldMapping> mappings = enhancedFieldMappingService.analyzeFieldMappings(testTable, businessContext);
            
            if (!mappings.isEmpty()) {
                EnhancedFieldMapping mapping = mappings.get(0);
                
                response.put("success", true);
                response.put("originalField", fieldName);
                response.put("optimizedField", mapping.getTargetField());
                response.put("fieldType", mapping.getFieldTypeDescription());
                response.put("isBaseField", false); // 不再支持基础字段模板
                response.put("isEnglishName", mapping.isEnglishName());
                response.put("isSnakeCase", mapping.isSnakeCase());
                response.put("confidence", mapping.getConfidence());
                response.put("optimizationNote", mapping.getOptimizationNote());
                response.put("suggestion", mapping.getOptimizationSuggestion());
                
                logger.info("字段优化测试: {} -> {} ({})", 
                           fieldName, mapping.getTargetField(), mapping.getFieldTypeDescription());
            } else {
                response.put("success", false);
                response.put("message", "字段分析失败");
            }
            
        } catch (Exception e) {
            logger.error("字段优化测试失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试表名优化
     */
    @PostMapping("/test-table-name")
    public ResponseEntity<Map<String, Object>> testTableNameOptimization(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String tableName = request.get("tableName");
            String businessContext = request.get("businessContext");
            
            if (tableName == null || tableName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "表名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            TableNameOptimizationServiceImpl.TableNameOptimizationResult result =
                tableNameOptimizationService.optimizeTableName(tableName, businessContext);
            
            response.put("success", true);
            response.put("originalTableName", result.getOriginalName());
            response.put("optimizedTableName", result.getOptimizedName());
            response.put("optimizationType", result.getOptimizationType());
            response.put("isOptimized", result.isOptimized());
            response.put("confidence", result.getConfidence());
            response.put("note", result.getNote());
            
            logger.info("表名优化测试: {} -> {} ({})", 
                       tableName, result.getOptimizedName(), result.getOptimizationType());
            
        } catch (Exception e) {
            logger.error("表名优化测试失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }



    /**
     * 构建表结构对象
     */
    private TableStructure buildTableStructure(Map<String, Object> tableData) {
        TableStructure table = new TableStructure();
        table.setTableName((String) tableData.get("tableName"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> columns = (List<Map<String, Object>>) tableData.get("columns");
        
        if (columns != null) {
            for (Map<String, Object> columnData : columns) {
                ColumnInfo column = new ColumnInfo();
                column.setColumnName((String) columnData.get("columnName"));
                column.setDataType((String) columnData.get("dataType"));
                column.setComment((String) columnData.get("comment"));
                
                Object sizeObj = columnData.get("columnSize");
                if (sizeObj instanceof Number) {
                    column.setColumnSize(((Number) sizeObj).intValue());
                }
                
                Object nullableObj = columnData.get("nullable");
                if (nullableObj instanceof Boolean) {
                    column.setNullable((Boolean) nullableObj);
                }
                
                table.getColumns().add(column);
            }
        }
        
        return table;
    }

    /**
     * 构建表结构对象（直接格式）
     */
    private TableStructure buildTableStructureDirectly(Map<String, Object> request) {
        TableStructure table = new TableStructure();
        table.setTableName((String) request.get("tableName"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> columns = (List<Map<String, Object>>) request.get("columns");
        
        if (columns != null) {
            for (Map<String, Object> columnData : columns) {
                ColumnInfo column = new ColumnInfo();
                column.setColumnName((String) columnData.get("name"));
                column.setDataType((String) columnData.get("dataType"));
                column.setComment((String) columnData.get("comment"));
                column.setDefaultValue((String) columnData.get("defaultValue"));
                
                Object nullableObj = columnData.get("isNullable");
                if (nullableObj instanceof Boolean) {
                    column.setNullable((Boolean) nullableObj);
                }
                
                Object primaryKeyObj = columnData.get("isPrimaryKey");
                if (primaryKeyObj instanceof Boolean && (Boolean) primaryKeyObj) {
                    table.getPrimaryKeys().add(column.getColumnName());
                }
                
                table.getColumns().add(column);
            }
        }
        
        // 处理主键列表
        @SuppressWarnings("unchecked")
        List<String> primaryKeys = (List<String>) request.get("primaryKeys");
        if (primaryKeys != null && !primaryKeys.isEmpty()) {
            for (String pk : primaryKeys) {
                if (!table.getPrimaryKeys().contains(pk)) {
                    table.getPrimaryKeys().add(pk);
                }
            }
        }
        
        return table;
    }
}
