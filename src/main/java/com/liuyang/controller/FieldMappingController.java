package com.liuyang.controller;

import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import com.liuyang.service.AiTableNamingService;
import com.liuyang.service.FieldMappingAnalysisService;
import com.liuyang.service.TableService;
import com.liuyang.service.impl.FieldMappingAnalysisServiceimpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字段映射控制器
 */
@RestController
@RequestMapping("/api/field-mapping")
public class FieldMappingController {
    
    private static final Logger logger = LoggerFactory.getLogger(FieldMappingController.class);
    
    @Autowired
    private FieldMappingAnalysisService analysisService;

    @Autowired
    private TableService tableService;

    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;

    @Autowired(required = false)
    private AiTableNamingService aiTableNamingService;
    
    /**
     * 分析字段映射
     */
    @PostMapping("/analyze")
    public ResponseEntity<Map<String, Object>> analyzeFieldMappings(@RequestBody AnalyzeRequest request) {
        logger.info("分析字段映射: 数据库={}, 表={}", request.getDatabaseName(), request.getTableName());
        
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取源表结构
            TableStructure sourceTable = tableService.getTableStructure(request.getDatabaseName(), request.getTableName());
            
            // 执行字段映射分析（支持AI增强）
            FieldMappingAnalysisServiceimpl.FieldMappingAnalysisResult analysisResult =
                analysisService.analyzeFieldMappings(
                    sourceTable,
                    request.getTemplateMappings(),
                    request.getTargetTableName(),
                    request.getBusinessContext());
            
            response.put("success", true);
            response.put("data", analysisResult);
            response.put("sourceTable", sourceTable);
            response.put("message", "字段映射分析完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("字段映射分析失败", e);
            response.put("success", false);
            response.put("message", "字段映射分析失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 验证字段映射配置
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateFieldMappings(@RequestBody ValidateRequest request) {
        logger.info("验证字段映射配置");
        
        Map<String, Object> response = new HashMap<>();
        try {
            ValidationResult validationResult = validateMappings(request.getFieldMappings());
            
            response.put("success", true);
            response.put("data", validationResult);
            response.put("message", "字段映射验证完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("字段映射验证失败", e);
            response.put("success", false);
            response.put("message", "字段映射验证失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * AI优化字段映射
     */
    @PostMapping("/ai-optimize")
    public ResponseEntity<Map<String, Object>> aiOptimizeFieldMappings(@RequestBody AiOptimizeRequest request) {
        logger.info("AI优化字段映射");

        Map<String, Object> response = new HashMap<>();
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI服务未启用");
                return ResponseEntity.status(400).body(response);
            }

            List<FieldMapping> optimizedMappings = aiFieldNamingService.optimizeFieldMappings(
                request.getFieldMappings(), request.getBusinessContext());

            response.put("success", true);
            response.put("data", optimizedMappings);
            response.put("message", "AI优化完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("AI优化字段映射失败", e);
            response.put("success", false);
            response.put("message", "AI优化失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * AI优化单个字段名
     */
    @PostMapping("/ai-optimize-field")
    public ResponseEntity<Map<String, Object>> aiOptimizeFieldName(@RequestBody AiOptimizeFieldRequest request) {
        logger.info("AI优化单个字段名: {}", request.getFieldName());

        Map<String, Object> response = new HashMap<>();
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI服务未启用");
                return ResponseEntity.status(400).body(response);
            }

            String optimizedName = aiFieldNamingService.optimizeFieldName(
                request.getFieldName(),
                request.getFieldType(),
                request.getComment(),
                request.getBusinessContext());

            response.put("success", true);
            response.put("data", optimizedName);
            response.put("message", "AI优化完成");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("AI优化字段名失败", e);
            response.put("success", false);
            response.put("message", "AI优化失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * AI生成表名
     */
    @PostMapping("/generate-table-name")
    public ResponseEntity<Map<String, Object>> generateTableName(@RequestBody GenerateTableNameRequest request) {
        logger.info("AI生成表名: 原表名={}", request.getOriginalTableName());

        Map<String, Object> response = new HashMap<>();
        try {
            String optimizedTableName;

            if (aiTableNamingService != null) {
                // 使用AI生成表名
                optimizedTableName = aiTableNamingService.generateOptimizedTableName(
                    request.getOriginalTableName(),
                    request.getTableComment(),
                    request.getBusinessContext());

                response.put("success", true);
                response.put("optimizedTableName", optimizedTableName);
                response.put("message", "AI表名生成成功");
                response.put("method", "AI");
            } else {
                // 降级方案：使用规则转换
                optimizedTableName = generateFallbackTableName(request.getOriginalTableName());

                response.put("success", false);
                response.put("fallbackTableName", optimizedTableName);
                response.put("message", "AI服务不可用，使用降级方案");
                response.put("method", "FALLBACK");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("AI表名生成失败", e);

            // 提供降级方案
            String fallbackTableName = generateFallbackTableName(request.getOriginalTableName());

            response.put("success", false);
            response.put("fallbackTableName", fallbackTableName);
            response.put("message", "AI表名生成失败，使用降级方案: " + e.getMessage());
            response.put("method", "FALLBACK");
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取字段映射建议
     */
    @GetMapping("/suggestions")
    public ResponseEntity<Map<String, Object>> getFieldMappingSuggestions(
            @RequestParam String sourceField,
            @RequestParam(required = false) String sourceType) {
        
        logger.info("获取字段映射建议: sourceField={}, sourceType={}", sourceField, sourceType);
        
        Map<String, Object> response = new HashMap<>();
        try {
            // 这里可以实现更复杂的建议逻辑
            Map<String, String> suggestions = generateSuggestions(sourceField, sourceType);
            
            response.put("success", true);
            response.put("data", suggestions);
            response.put("message", "获取建议成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取字段映射建议失败", e);
            response.put("success", false);
            response.put("message", "获取建议失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 验证字段映射
     */
    private ValidationResult validateMappings(List<FieldMapping> mappings) {
        ValidationResult result = new ValidationResult();
        
        for (FieldMapping mapping : mappings) {
            // 检查必填字段
            if (mapping.getSourceField() == null || mapping.getSourceField().trim().isEmpty()) {
                result.addError("源字段名不能为空");
            }
            
            if (mapping.getTargetField() == null || mapping.getTargetField().trim().isEmpty()) {
                result.addError("目标字段名不能为空");
            }
            
            // 检查字段名重复
            long targetFieldCount = mappings.stream()
                    .filter(m -> m.getTargetField() != null)
                    .filter(m -> m.getTargetField().equals(mapping.getTargetField()))
                    .count();
            
            if (targetFieldCount > 1) {
                result.addError("目标字段名重复: " + mapping.getTargetField());
            }
            
            // 检查数据类型兼容性
            if (!isTypeCompatible(mapping.getSourceType(), mapping.getTargetType())) {
                result.addWarning(String.format("字段 %s: 数据类型可能不兼容 (%s -> %s)", 
                    mapping.getSourceField(), mapping.getSourceType(), mapping.getTargetType()));
            }
        }
        
        return result;
    }
    
    /**
     * 检查数据类型兼容性
     */
    private boolean isTypeCompatible(String sourceType, String targetType) {
        if (sourceType == null || targetType == null) {
            return true;
        }
        
        String source = sourceType.toLowerCase();
        String target = targetType.toLowerCase();
        
        // 简单的类型兼容性检查
        if (source.equals(target)) {
            return true;
        }
        
        // 数值类型兼容
        if ((source.contains("int") || source.contains("serial")) && 
            (target.contains("int") || target.contains("serial"))) {
            return true;
        }
        
        // 字符串类型兼容
        if ((source.contains("varchar") || source.contains("text") || source.contains("char")) &&
            (target.contains("varchar") || target.contains("text") || target.contains("char"))) {
            return true;
        }
        
        // 时间类型兼容
        if ((source.contains("timestamp") || source.contains("datetime") || source.contains("date")) &&
            (target.contains("timestamp") || target.contains("datetime") || target.contains("date"))) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 生成字段映射建议
     */
    private Map<String, String> generateSuggestions(String sourceField, String sourceType) {
        Map<String, String> suggestions = new HashMap<>();
        
        // 基于字段名的建议
        String lowerField = sourceField.toLowerCase();
        
        if (lowerField.contains("id") && !lowerField.equals("id")) {
            suggestions.put("保持原名", sourceField);
        } else if (lowerField.equals("id")) {
            suggestions.put("业务ID", "business_id");
            suggestions.put("保持原名", "id");
        } else if (lowerField.contains("time") || lowerField.contains("date")) {
            if (lowerField.contains("create")) {
                suggestions.put("创建时间", "create_time");
            } else if (lowerField.contains("update") || lowerField.contains("modify")) {
                suggestions.put("更新时间", "update_time");
            } else {
                suggestions.put("保持原名", sourceField);
            }
        } else if (lowerField.contains("user") || lowerField.contains("by")) {
            if (lowerField.contains("create")) {
                suggestions.put("创建人", "create_user");
            } else if (lowerField.contains("update") || lowerField.contains("modify")) {
                suggestions.put("更新人", "update_user");
            } else {
                suggestions.put("保持原名", sourceField);
            }
        } else {
            suggestions.put("保持原名", sourceField);
            suggestions.put("下划线命名", convertToSnakeCase(sourceField));
        }
        
        return suggestions;
    }
    
    /**
     * 转换为下划线命名
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 分析请求
     */
    public static class AnalyzeRequest {
        private String databaseName;
        private String tableName;
        private String targetTableName;
        private String businessContext;
        private List<FieldMapping> templateMappings;

        // Getters and setters
        public String getDatabaseName() { return databaseName; }
        public void setDatabaseName(String databaseName) { this.databaseName = databaseName; }

        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public String getTargetTableName() { return targetTableName; }
        public void setTargetTableName(String targetTableName) { this.targetTableName = targetTableName; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }

        public List<FieldMapping> getTemplateMappings() { return templateMappings; }
        public void setTemplateMappings(List<FieldMapping> templateMappings) { this.templateMappings = templateMappings; }
    }

    /**
     * AI优化请求
     */
    public static class AiOptimizeRequest {
        private List<FieldMapping> fieldMappings;
        private String businessContext;

        public List<FieldMapping> getFieldMappings() { return fieldMappings; }
        public void setFieldMappings(List<FieldMapping> fieldMappings) { this.fieldMappings = fieldMappings; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }

    /**
     * AI优化单个字段请求
     */
    public static class AiOptimizeFieldRequest {
        private String fieldName;
        private String fieldType;
        private String comment;
        private String businessContext;

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }
    
    /**
     * 验证请求
     */
    public static class ValidateRequest {
        private List<FieldMapping> fieldMappings;
        
        public List<FieldMapping> getFieldMappings() { return fieldMappings; }
        public void setFieldMappings(List<FieldMapping> fieldMappings) { this.fieldMappings = fieldMappings; }
    }
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private List<String> errors = new java.util.ArrayList<>();
        private List<String> warnings = new java.util.ArrayList<>();
        
        public void addError(String error) { this.errors.add(error); }
        public void addWarning(String warning) { this.warnings.add(warning); }
        
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        
        public boolean hasErrors() { return !errors.isEmpty(); }
        public boolean hasWarnings() { return !warnings.isEmpty(); }
        public boolean isValid() { return !hasErrors(); }
    }



    /**
     * 生成简单的降级表名
     */
    private String generateSimpleFallbackTableName(String originalTableName) {
        if (originalTableName == null || originalTableName.trim().isEmpty()) {
            return "hc_table";
        }

        String fallback = originalTableName.toLowerCase()
                .replaceAll("[^a-z0-9]", "_")
                .replaceAll("_+", "_")
                .replaceAll("^_+|_+$", "");

        if (fallback.isEmpty()) {
            fallback = "table";
        }

        if (!fallback.matches("^[a-z].*")) {
            fallback = "t_" + fallback;
        }

        return "hc_" + fallback;
    }

    /**
     * 生成降级表名（规则转换）
     */
    private String generateFallbackTableName(String originalTableName) {
        if (originalTableName == null || originalTableName.trim().isEmpty()) {
            return "hc_unknown_table";
        }

        String tableName = originalTableName.trim();

        // 如果已经以hc_开头，直接返回
        if (tableName.toLowerCase().startsWith("hc_")) {
            return tableName.toLowerCase();
        }

        // 转换为snake_case
        String snakeCaseName = convertToSnakeCase(tableName);

        // 添加hc_前缀
        return "hc_" + snakeCaseName;
    }

    /**
     * 表名生成请求
     */
    public static class GenerateTableNameRequest {
        private String originalTableName;
        private String tableComment;
        private String businessContext;

        // Getters and Setters
        public String getOriginalTableName() { return originalTableName; }
        public void setOriginalTableName(String originalTableName) { this.originalTableName = originalTableName; }

        public String getTableComment() { return tableComment; }
        public void setTableComment(String tableComment) { this.tableComment = tableComment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }


}
