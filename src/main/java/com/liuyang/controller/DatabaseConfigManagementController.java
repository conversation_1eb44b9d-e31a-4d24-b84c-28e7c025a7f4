package com.liuyang.controller;

import com.liuyang.annotation.OperationLog;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.service.DatabaseConfigManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库配置管理Controller
 */
@RestController
@RequestMapping("/api/database-config")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:8080", "http://localhost:8081"})
public class DatabaseConfigManagementController {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfigManagementController.class);

    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;

    /**
     * 查询所有配置
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getAllConfigs() {
        try {
            logger.info("查询所有配置");

            List<DatabaseConfigEntity> configs = databaseConfigManagementService.getAllConfigs();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);
            response.put("total", configs.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("查询配置列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取配置详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getConfigById(@PathVariable Long id) {
        try {
            logger.info("获取配置详情: ID={}", id);

            DatabaseConfigEntity config = databaseConfigManagementService.getConfigById(id);
            
            Map<String, Object> response = new HashMap<>();
            if (config != null) {
                response.put("success", true);
                response.put("data", config);
            } else {
                response.put("success", false);
                response.put("message", "配置不存在");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取配置详情失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 创建或更新配置
     */
    @PostMapping("/save")
    @OperationLog(operationType = "CONFIG_MANAGEMENT",
                 operationDesc = "保存数据库配置: #{#config.configName}",
                 targetType = "CONFIG", targetId = "#{#config.id}")
    public ResponseEntity<Map<String, Object>> saveConfig(@RequestBody DatabaseConfigEntity config) {
        try {
            logger.info("保存配置: {}", config.getConfigName());

            DatabaseConfigEntity savedConfig = databaseConfigManagementService.saveOrUpdateConfig(config);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedConfig);
            response.put("message", "保存成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("保存配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    @OperationLog(operationType = "CONFIG_MANAGEMENT",
                 operationDesc = "删除数据库配置",
                 targetType = "CONFIG", targetId = "#{#id}")
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        try {
            logger.info("删除配置: ID={}", id);

            databaseConfigManagementService.deleteConfig(id);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "删除成功");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("删除配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 切换配置状态
     */
    @PostMapping("/{id}/toggle-status")
    public ResponseEntity<Map<String, Object>> toggleStatus(@PathVariable Long id, @RequestParam boolean isActive) {
        try {
            logger.info("切换配置状态: ID={}, 新状态={}", id, isActive);

            DatabaseConfigEntity config = databaseConfigManagementService.getConfigById(id);
            if (config == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "配置不存在");
                return ResponseEntity.status(404).body(response);
            }

            config.setIsActive(isActive);
            databaseConfigManagementService.saveOrUpdateConfig(config);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", isActive ? "配置已启用" : "配置已禁用");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("切换配置状态失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "切换状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 测试数据库连接
     */
    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@RequestBody DatabaseConfigEntity config) {
        try {
            logger.info("测试数据库连接: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort());

            boolean isConnected = databaseConfigManagementService.testConnection(config);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("connected", isConnected);
            response.put("message", isConnected ? "连接成功" : "连接失败");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("测试连接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("connected", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 批量测试数据库连接
     */
    @PostMapping("/batch-test-connection")
    public ResponseEntity<Map<String, Object>> batchTestConnection(@RequestBody List<Long> configIds) {
        try {
            logger.info("批量测试数据库连接，配置数量: {}", configIds.size());

            List<Map<String, Object>> results = databaseConfigManagementService.batchTestConnection(configIds);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", results);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("批量测试连接失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            logger.info("获取统计信息");

            Map<String, Object> statistics = databaseConfigManagementService.getStatistics();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", statistics);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取环境列表
     */
    @GetMapping("/environments")
    public ResponseEntity<Map<String, Object>> getEnvironments() {
        try {
            logger.info("获取环境列表");

            List<String> environments = databaseConfigManagementService.getAllEnvironments();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", environments);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取环境列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取指定环境的配置
     */
    @GetMapping("/environments/{environment}/configs")
    public ResponseEntity<Map<String, Object>> getConfigsByEnvironment(@PathVariable String environment) {
        try {
            logger.info("获取{}环境的配置", environment);

            List<DatabaseConfigEntity> configs = databaseConfigManagementService.getActiveConfigsByEnvironment(environment);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", configs);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取环境配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取当前环境的源数据库配置
     */
    @GetMapping("/current/source")
    public ResponseEntity<Map<String, Object>> getCurrentSourceConfig() {
        try {
            logger.info("获取当前环境的源数据库配置");

            DatabaseConfigEntity config = databaseConfigManagementService.getSourceDatabaseConfig();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取源数据库配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取当前环境的目标数据库配置
     */
    @GetMapping("/current/target")
    public ResponseEntity<Map<String, Object>> getCurrentTargetConfig() {
        try {
            logger.info("获取当前环境的目标数据库配置");

            DatabaseConfigEntity config = databaseConfigManagementService.getTargetDatabaseConfig();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("获取目标数据库配置失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
} 