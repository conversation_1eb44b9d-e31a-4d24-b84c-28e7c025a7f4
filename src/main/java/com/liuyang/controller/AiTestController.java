package com.liuyang.controller;

import com.liuyang.service.AiFieldNamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * AI测试控制器
 * 用于测试AI服务的基本功能
 */
@RestController
@RequestMapping("/api/test")

public class AiTestController {

    private static final Logger logger = LoggerFactory.getLogger(AiTestController.class);

    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;

    @Autowired(required = false)
    @Qualifier("fieldNamingChatClient")
    private ChatClient chatClient;

    @Value("${spring.ai.dashscope.api-key:}")
    private String dashscopeApiKey;

    @Value("${app.ai.field-naming.enabled:false}")
    private boolean aiEnabled;

    /**
     * 测试AI服务状态
     */
    @GetMapping("/ai-status")
    public ResponseEntity<Map<String, Object>> testAiStatus() {
        Map<String, Object> response = new HashMap<>();

        try {
            response.put("configEnabled", aiEnabled);
            response.put("chatClientAvailable", chatClient != null);
            response.put("aiServiceAvailable", aiFieldNamingService != null);
            response.put("hasApiKey", dashscopeApiKey != null && !dashscopeApiKey.trim().isEmpty()
                                     && !dashscopeApiKey.contains("your-dashscope-api-key"));

            if (chatClient != null) {
                response.put("chatClientType", "Spring AI Alibaba ChatClient");
            }

            if (aiFieldNamingService != null) {
                response.put("aiServiceType", aiFieldNamingService.getClass().getSimpleName());
            }

            // 诊断信息
            String diagnosis;
            if (!aiEnabled) {
                diagnosis = "AI功能在配置中被禁用";
            } else if (dashscopeApiKey == null || dashscopeApiKey.trim().isEmpty() || dashscopeApiKey.contains("your-dashscope-api-key")) {
                diagnosis = "阿里千问API密钥未配置或无效";
            } else if (chatClient == null) {
                diagnosis = "ChatClient未初始化，可能是网络问题或API密钥无效";
            } else if (aiFieldNamingService == null) {
                diagnosis = "AI字段命名服务未启动";
            } else {
                diagnosis = "AI服务配置正常";
            }

            response.put("diagnosis", diagnosis);
            response.put("success", true);

            logger.info("AI服务诊断: {}", diagnosis);

        } catch (Exception e) {
            logger.error("AI服务状态检查失败", e);
            response.put("success", false);
            response.put("message", "状态检查失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试字段名优化功能
     */
    @PostMapping("/optimize-field")
    public ResponseEntity<Map<String, Object>> testOptimizeField(@RequestBody TestFieldRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用，使用降级服务");
                response.put("originalName", request.getFieldName());
                response.put("optimizedName", convertToSnakeCase(request.getFieldName()));
                return ResponseEntity.ok(response);
            }
            
            String optimizedName = aiFieldNamingService.optimizeFieldName(
                request.getFieldName(),
                request.getFieldType(),
                request.getComment(),
                request.getBusinessContext()
            );
            
            response.put("success", true);
            response.put("originalName", request.getFieldName());
            response.put("optimizedName", optimizedName);
            response.put("message", "字段名优化完成");
            
            logger.info("字段名优化测试: {} -> {}", request.getFieldName(), optimizedName);
            
        } catch (Exception e) {
            logger.error("字段名优化测试失败", e);
            response.put("success", false);
            response.put("message", "优化失败: " + e.getMessage());
            response.put("originalName", request.getFieldName());
            response.put("optimizedName", convertToSnakeCase(request.getFieldName()));
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试字段名验证功能
     */
    @PostMapping("/validate-field")
    public ResponseEntity<Map<String, Object>> testValidateField(@RequestBody TestFieldRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (aiFieldNamingService == null) {
                response.put("success", false);
                response.put("message", "AI字段命名服务未启用，使用基础验证");
                response.put("valid", isValidSnakeCase(request.getFieldName()));
                response.put("suggestion", "使用降级验证");
                return ResponseEntity.ok(response);
            }
            
            AiFieldNamingService.FieldNameValidationResult result =
                aiFieldNamingService.validateFieldName(request.getFieldName(), request.getFieldType());
            
            response.put("success", true);
            response.put("valid", result.isValid());
            response.put("reason", result.getReason());
            response.put("suggestion", result.getSuggestion());
            response.put("improvedName", result.getImprovedName());
            response.put("message", "字段名验证完成");
            
            logger.info("字段名验证测试: {} -> valid={}", request.getFieldName(), result.isValid());
            
        } catch (Exception e) {
            logger.error("字段名验证测试失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
            response.put("valid", false);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("success", true);
        response.put("aiServiceAvailable", aiFieldNamingService != null);
        response.put("serviceClass", aiFieldNamingService != null ? 
            aiFieldNamingService.getClass().getSimpleName() : "None");
        response.put("message", "配置信息获取完成");
        
        return ResponseEntity.ok(response);
    }

    // 辅助方法
    private String convertToSnakeCase(String camelCase) {
        if (camelCase == null) return null;
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    private boolean isValidSnakeCase(String fieldName) {
        if (fieldName == null) return false;
        return fieldName.matches("^[a-z][a-z0-9_]*[a-z0-9]$");
    }

    // 请求DTO
    public static class TestFieldRequest {
        private String fieldName;
        private String fieldType;
        private String comment;
        private String businessContext;

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public String getBusinessContext() { return businessContext; }
        public void setBusinessContext(String businessContext) { this.businessContext = businessContext; }
    }
}
