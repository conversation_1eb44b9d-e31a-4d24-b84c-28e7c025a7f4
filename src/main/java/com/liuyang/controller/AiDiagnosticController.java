package com.liuyang.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * AI配置诊断控制器
 * 用于诊断Spring AI Alibaba配置问题
 */
@RestController
@RequestMapping("/api/ai-diagnostic")

public class AiDiagnosticController {

    private static final Logger logger = LoggerFactory.getLogger(AiDiagnosticController.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    @Qualifier("fieldNamingChatClient")
    private ChatClient chatClient;

    @Value("${spring.ai.dashscope.api-key:}")
    private String dashscopeApiKey;

    @Value("${app.ai.field-naming.enabled:false}")
    private boolean aiEnabled;

    /**
     * 诊断AI配置状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> diagnoseAiStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 基础配置检查
            response.put("aiFeatureEnabled", aiEnabled);
            response.put("hasApiKey", dashscopeApiKey != null && !dashscopeApiKey.trim().isEmpty());
            response.put("apiKeyPrefix", getApiKeyPrefix());
            response.put("chatClientAvailable", chatClient != null);
            
            // Bean检查
            Map<String, Object> beanStatus = new HashMap<>();
            beanStatus.put("chatClientBean", checkBean("fieldNamingChatClient"));
            beanStatus.put("chatClientBuilderBean", checkBean("chatClientBuilder"));
            beanStatus.put("simplifiedAiConfigurationBean", checkBean("simplifiedAiConfiguration"));
            response.put("beanStatus", beanStatus);
            
            // 依赖检查
            Map<String, Object> dependencyStatus = new HashMap<>();
            dependencyStatus.put("springAiChatClient", checkClass("org.springframework.ai.chat.client.ChatClient"));
            dependencyStatus.put("springAiChatModel", checkClass("org.springframework.ai.chat.model.ChatModel"));
            dependencyStatus.put("alibabaDashScope", checkClass("com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel"));
            response.put("dependencyStatus", dependencyStatus);
            
            // 配置建议
            response.put("suggestions", generateSuggestions());
            
            response.put("success", true);
            response.put("message", "AI配置诊断完成");
            
        } catch (Exception e) {
            logger.error("AI配置诊断失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 简单的ChatClient测试 (GET请求，浏览器友好)
     */
    @GetMapping("/test-chat-simple")
    public ResponseEntity<Map<String, Object>> testChatClientSimple() {
        Map<String, Object> response = new HashMap<>();

        try {
            if (chatClient == null) {
                response.put("success", false);
                response.put("message", "ChatClient未初始化");
                response.put("suggestion", "请检查Spring AI Alibaba配置和API密钥");
                return ResponseEntity.ok(response);
            }

            String testPrompt = "请简单说'你好，我是阿里千问'";

            String result = chatClient.prompt(testPrompt).call().content();

            response.put("success", true);
            response.put("prompt", testPrompt);
            response.put("result", result);
            response.put("message", "ChatClient测试成功");

        } catch (Exception e) {
            logger.error("ChatClient测试失败", e);
            response.put("success", false);
            response.put("message", "ChatClient测试失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 测试ChatClient基础功能 (POST请求，支持自定义提示词)
     */
    @PostMapping("/test-chat")
    public ResponseEntity<Map<String, Object>> testChatClient(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (chatClient == null) {
                response.put("success", false);
                response.put("message", "ChatClient未初始化");
                response.put("suggestion", "请检查Spring AI Alibaba配置和API密钥");
                return ResponseEntity.ok(response);
            }
            
            String testPrompt = request.getOrDefault("prompt", "请说'你好'");
            
            String result = chatClient.prompt(testPrompt).call().content();
            
            response.put("success", true);
            response.put("prompt", testPrompt);
            response.put("result", result);
            response.put("message", "ChatClient测试成功");
            
        } catch (Exception e) {
            logger.error("ChatClient测试失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "ChatClient测试失败");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取详细的配置信息
     */
    @GetMapping("/config-details")
    public ResponseEntity<Map<String, Object>> getConfigDetails() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> config = new HashMap<>();
            config.put("aiEnabled", aiEnabled);
            config.put("apiKey", getApiKeyPrefix());
            config.put("chatClientClass", chatClient != null ? chatClient.getClass().getName() : "null");
            
            // 环境变量检查
            Map<String, String> envVars = new HashMap<>();
            envVars.put("AI_DASHSCOPE_API_KEY", System.getenv("AI_DASHSCOPE_API_KEY") != null ? "已设置" : "未设置");
            config.put("environmentVariables", envVars);
            
            // Spring AI Alibaba版本信息
            config.put("springAiAlibabaVersion", getSpringAiAlibabaVersion());
            
            response.put("success", true);
            response.put("config", config);
            
        } catch (Exception e) {
            logger.error("获取配置详情失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    private String getApiKeyPrefix() {
        if (dashscopeApiKey == null || dashscopeApiKey.trim().isEmpty()) {
            return "未配置";
        }
        if (dashscopeApiKey.length() > 10) {
            return dashscopeApiKey.substring(0, 10) + "...";
        }
        return "已配置";
    }

    private boolean checkBean(String beanName) {
        try {
            return applicationContext.containsBean(beanName);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkClass(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    private String[] generateSuggestions() {
        if (chatClient == null) {
            return new String[]{
                "1. 检查API密钥是否正确配置: export AI_DASHSCOPE_API_KEY=your-key",
                "2. 确认Spring AI Alibaba依赖版本兼容性",
                "3. 检查application.yml中的配置格式",
                "4. 尝试重启应用",
                "5. 查看启动日志中的错误信息"
            };
        } else {
            return new String[]{
                "ChatClient配置正常，可以正常使用AI功能"
            };
        }
    }

    private String getSpringAiAlibabaVersion() {
        try {
            // 尝试获取版本信息
            Package pkg = Package.getPackage("com.alibaba.cloud.ai");
            if (pkg != null && pkg.getImplementationVersion() != null) {
                return pkg.getImplementationVersion();
            }
            return "无法获取版本信息";
        } catch (Exception e) {
            return "版本检查失败";
        }
    }
}
