package com.liuyang.controller;

import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableRestructureRequest;
import com.liuyang.service.TableRestructureService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表重构控制器
 */
// @RestController
@RequestMapping("/api/restructure")
public class TableRestructureController {
    
    private static final Logger logger = LoggerFactory.getLogger(TableRestructureController.class);
    
    @Resource
    private TableRestructureService tableRestructureService;
    
    /**
     * 执行表重构
     */
    @PostMapping("/table")
    public ResponseEntity<Map<String, Object>> restructureTable(@Valid @RequestBody TableRestructureRequest request) {
        logger.info("收到表重构请求: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        try {
            tableRestructureService.restructureTable(request);
            response.put("success", true);
            response.put("message", "表重构成功");
            response.put("data", Map.of(
                "sourceDatabase", request.getSourceDatabase(),
                "targetDatabase", request.getTargetDatabase(),
                "sourceTable", request.getSourceTableName(),
                "targetTable", request.getTargetTableName(),
                "fieldMappings", request.getFieldMappings().size(),
                "useDualPrimaryKey", request.isUseDualPrimaryKey()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("表重构失败", e);
            response.put("success", false);
            response.put("message", "表重构失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取健康云查询记录表的预定义映射配置
     */
    @GetMapping("/predefined/health-query-record")
    public ResponseEntity<Map<String, Object>> getHealthQueryRecordMapping() {
        logger.info("获取健康云查询记录表预定义映射");
        
        Map<String, Object> response = new HashMap<>();
        try {
            TableRestructureRequest predefinedRequest = createHealthQueryRecordMapping();
            response.put("success", true);
            response.put("data", predefinedRequest);
            response.put("message", "获取预定义映射成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取预定义映射失败", e);
            response.put("success", false);
            response.put("message", "获取预定义映射失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 创建健康云查询记录表的预定义映射
     */
    private TableRestructureRequest createHealthQueryRecordMapping() {
        TableRestructureRequest request = new TableRestructureRequest();
        request.setSourceTableName("jkycxfjltable");
        request.setTargetTableName("hc_health_query_record");
        request.setUseDualPrimaryKey(true);
        request.setBusinessIdField("business_id");
        request.setNewPrimaryKeyField("id");
        request.setCopyData(true);
        request.setDropIfExists(false);
        request.setCreateIndexes(true);
        
        // 设置索引字段
        request.setIndexFields(Arrays.asList(
            "order_no", "car_no", "vin_code", "user_id", 
            "wx_open_id", "create_time", "business_id"
        ));
        
        // 创建字段映射
        List<FieldMapping> mappings = Arrays.asList(
            // 原主键作为业务ID
            createMapping("id", "business_id", "varchar", "varchar", 64, 64, false, "业务ID（原主键）"),
            
            // 基础字段重命名
            createMapping("creator", "create_user", "varchar", "varchar", 200, 200, true, "创建人"),
            createMapping("createtime", "create_time", "timestamp", "timestamp", null, null, false, "创建时间"),
            createMapping("updater", "update_user", "varchar", "varchar", 200, 200, true, "修改人"),
            createMapping("updatetime", "update_time", "timestamp", "timestamp", null, null, true, "修改时间"),
            
            // 业务字段重命名
            createMapping("jkycuserid", "user_id", "varchar", "varchar", 200, 200, true, "健康云用户ID"),
            createMapping("xflx", "consumption_type", "varchar", "varchar", 200, 50, true, "消费类型"),
            createMapping("querytype", "query_type", "varchar", "varchar", 200, 50, true, "查询类型"),
            createMapping("vincode", "vin_code", "varchar", "varchar", 200, 200, true, "VIN码"),
            createMapping("licurl", "license_url", "varchar", "varchar", 300, 500, true, "行驶证URL"),
            createMapping("paystate", "pay_status", "varchar", "varchar", 200, 50, true, "支付状态"),
            createMapping("bgurl", "report_url", "varchar", "varchar", 300, 500, true, "报告URL"),
            createMapping("paymount", "pay_amount_str", "varchar", "varchar", 200, 200, true, "支付金额字符串"),
            createMapping("zfje", "pay_amount", "numeric", "decimal", null, null, true, "支付金额数值"),
            createMapping("wxopenid", "wx_open_id", "varchar", "varchar", 200, 200, true, "微信OpenID"),
            createMapping("orderno", "order_no", "varchar", "varchar", 200, 200, false, "订单号"),
            createMapping("querydate", "query_date", "timestamp", "timestamp", null, null, true, "查询日期"),
            createMapping("cxorderno", "query_order_no", "varchar", "varchar", 200, 200, true, "查询订单号"),
            createMapping("bgzt", "report_status", "varchar", "varchar", 200, 50, true, "报告状态"),
            createMapping("ceshi11", "test_field", "numeric", "decimal", null, null, true, "测试字段"),
            createMapping("authstate", "auth_status", "varchar", "varchar", 200, 50, true, "认证状态"),
            createMapping("authtime", "auth_time", "timestamp", "timestamp", null, null, true, "认证时间"),
            createMapping("carno", "car_no", "varchar", "varchar", 200, 200, true, "车牌号"),
            createMapping("brand", "brand", "varchar", "varchar", 200, 200, true, "品牌"),
            createMapping("par", "parameters", "varchar", "text", null, null, true, "参数"),
            createMapping("channel", "channel", "varchar", "varchar", 200, 200, true, "渠道"),
            createMapping("operator", "operator", "varchar", "varchar", 200, 200, true, "操作员"),
            createMapping("payfreecount", "free_pay_count", "varchar", "varchar", 200, 200, true, "免费支付次数"),
            createMapping("bgdata", "report_data", "varchar", "text", null, null, true, "报告数据"),
            createMapping("reason", "reason", "varchar", "varchar", 4000, 4000, true, "原因"),
            createMapping("cartypename", "car_type_name", "varchar", "varchar", 200, 200, true, "车型名称"),
            createMapping("recalltime", "recall_time", "timestamp", "timestamp", null, null, true, "召回时间")
        );
        
        request.setFieldMappings(mappings);
        
        return request;
    }
    
    /**
     * 创建字段映射的辅助方法
     */
    private FieldMapping createMapping(String sourceField, String targetField, 
                                     String sourceType, String targetType,
                                     Integer sourceSize, Integer targetSize,
                                     boolean nullable, String comment) {
        FieldMapping mapping = new FieldMapping();
        mapping.setSourceField(sourceField);
        mapping.setTargetField(targetField);
        mapping.setSourceType(sourceType);
        mapping.setTargetType(targetType);
        mapping.setSourceSize(sourceSize);
        mapping.setTargetSize(targetSize);
        mapping.setNullable(nullable);
        mapping.setComment(comment);
        return mapping;
    }
}
