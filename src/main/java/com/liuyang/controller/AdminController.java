package com.liuyang.controller;

import com.liuyang.mapper.UserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器 - 用于系统管理操作
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 修复用户密码 - 临时API，用于修复密码哈希问题
     */
    @PostMapping("/fix-passwords")
    public ResponseEntity<Map<String, Object>> fixUserPasswords() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 更新admin用户密码 (admin123)
            String adminPasswordHash = "$2a$10$7hNtr8kUc29J1JPil3xn0.3UU9lfjvnHkpx8ox2kJU/mZFIdtIkae";
            int adminUpdated = userMapper.updatePassword(1L, adminPasswordHash, "system");
            
            // 更新testuser用户密码 (user123)  
            String userPasswordHash = "$2a$10$cxgsw4rDG3Eul1yrhijJ7e12SFBYGGDPNfqiGG1hjSpcb6f2P90yy";
            int userUpdated = userMapper.updatePassword(2L, userPasswordHash, "system");
            
            result.put("success", true);
            result.put("message", "密码修复完成");
            result.put("adminUpdated", adminUpdated > 0);
            result.put("userUpdated", userUpdated > 0);
            
            logger.info("✅ 用户密码修复完成: admin={}, testuser={}", adminUpdated > 0, userUpdated > 0);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("❌ 密码修复失败", e);
            result.put("success", false);
            result.put("message", "密码修复失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
    
    /**
     * 查看用户信息
     */
    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> getUsers() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            var admin = userMapper.selectByUsername("admin");
            var testuser = userMapper.selectByUsername("testuser");
            
            result.put("success", true);
            result.put("admin", admin);
            result.put("testuser", testuser);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("查询用户失败", e);
            result.put("success", false);
            result.put("message", "查询用户失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
}
