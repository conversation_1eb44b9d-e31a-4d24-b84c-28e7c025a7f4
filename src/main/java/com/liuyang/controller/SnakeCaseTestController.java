package com.liuyang.controller;

import com.liuyang.util.SnakeCaseConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Snake Case 转换测试控制器
 * 用于测试和演示字段名snake_case转换功能
 */
@RestController
@RequestMapping("/api/snake-case")

public class SnakeCaseTestController {

    private static final Logger logger = LoggerFactory.getLogger(SnakeCaseTestController.class);

    /**
     * 转换单个字段名为snake_case
     */
    @PostMapping("/convert")
    public ResponseEntity<Map<String, Object>> convertFieldName(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String originalField = request.get("fieldName");
            if (originalField == null || originalField.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "字段名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String convertedField = SnakeCaseConverter.toSnakeCase(originalField);
            String suggestion = SnakeCaseConverter.getConversionSuggestion(originalField);
            
            response.put("success", true);
            response.put("originalField", originalField);
            response.put("convertedField", convertedField);
            response.put("suggestion", suggestion);
            response.put("isChanged", !originalField.equals(convertedField));
            
            logger.info("字段转换: {} -> {}", originalField, convertedField);
            
        } catch (Exception e) {
            logger.error("字段转换失败", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 批量转换字段名
     */
    @PostMapping("/convert-batch")
    public ResponseEntity<Map<String, Object>> convertFieldNames(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, String> fields = (Map<String, String>) request.get("fields");
            
            if (fields == null || fields.isEmpty()) {
                response.put("success", false);
                response.put("message", "字段列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            Map<String, String> convertedFields = SnakeCaseConverter.convertFieldNames(fields);
            Map<String, Object> conversions = new HashMap<>();
            
            for (Map.Entry<String, String> entry : fields.entrySet()) {
                String original = entry.getKey();
                String converted = SnakeCaseConverter.toSnakeCase(original);
                
                Map<String, Object> conversionInfo = new HashMap<>();
                conversionInfo.put("original", original);
                conversionInfo.put("converted", converted);
                conversionInfo.put("isChanged", !original.equals(converted));
                conversionInfo.put("suggestion", SnakeCaseConverter.getConversionSuggestion(original));
                
                conversions.put(original, conversionInfo);
            }
            
            response.put("success", true);
            response.put("totalFields", fields.size());
            response.put("conversions", conversions);
            
            logger.info("批量转换完成: {} 个字段", fields.size());
            
        } catch (Exception e) {
            logger.error("批量转换失败", e);
            response.put("success", false);
            response.put("message", "批量转换失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取转换示例
     */
    @GetMapping("/examples")
    public ResponseEntity<Map<String, Object>> getConversionExamples() {
        Map<String, Object> response = new HashMap<>();
        
        // 准备示例数据
        String[] exampleFields = {
            "userName", "createTime", "isActive", "totalAmount",
            "orderStatus", "businessId", "updateUser", "itemCount",
            "XMLHttpRequest", "JSONResponseData", "user_name", "create_time",
            "paymentStatus", "isDeleted", "maxLimit", "unitPrice"
        };
        
        Map<String, Object> examples = new HashMap<>();
        
        for (String field : exampleFields) {
            String converted = SnakeCaseConverter.toSnakeCase(field);
            
            Map<String, Object> example = new HashMap<>();
            example.put("original", field);
            example.put("converted", converted);
            example.put("isChanged", !field.equals(converted));
            
            examples.put(field, example);
        }
        
        response.put("success", true);
        response.put("examples", examples);
        response.put("description", "Snake Case 转换示例 - 单词之间使用下划线分隔");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 验证字段名是否符合snake_case规范
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateSnakeCase(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String fieldName = request.get("fieldName");
            if (fieldName == null || fieldName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "字段名不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            String converted = SnakeCaseConverter.toSnakeCase(fieldName);
            boolean isValid = fieldName.equals(converted);
            
            response.put("success", true);
            response.put("fieldName", fieldName);
            response.put("isValidSnakeCase", isValid);
            response.put("suggestion", SnakeCaseConverter.getConversionSuggestion(fieldName));
            
            if (!isValid) {
                response.put("recommendedName", converted);
            }
            
        } catch (Exception e) {
            logger.error("验证失败", e);
            response.put("success", false);
            response.put("message", "验证失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
}
