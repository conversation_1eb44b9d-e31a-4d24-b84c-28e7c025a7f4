package com.liuyang.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 实时日志推送WebSocket控制器
 */
@Component
public class LogWebSocketController extends TextWebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(LogWebSocketController.class);
    
    // 存储所有连接的WebSocket会话
    private static final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    
    // 日志缓存队列
    private static final java.util.concurrent.ConcurrentLinkedQueue<String> logQueue = 
        new java.util.concurrent.ConcurrentLinkedQueue<>();
    
    // 定时推送服务
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    // 日志格式化器
    private static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
    
    static {
        // 启动定时推送任务
        scheduler.scheduleAtFixedRate(() -> {
            if (!logQueue.isEmpty() && !sessions.isEmpty()) {
                StringBuilder batch = new StringBuilder();
                String log;
                int count = 0;
                
                // 批量处理日志，避免频繁推送
                while ((log = logQueue.poll()) != null && count < 10) {
                    batch.append(log).append("\n");
                    count++;
                }
                
                if (batch.length() > 0) {
                    broadcastLog(batch.toString());
                }
            }
        }, 500, 500, TimeUnit.MILLISECONDS); // 每500ms推送一次
    }
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        logger.info("📱 新的日志监控连接建立: {}", session.getId());
        
        // 发送欢迎消息
        String welcomeMsg = formatLogMessage("INFO", "LogWebSocket", "🎉 实时日志监控已连接，开始接收系统日志...");
        session.sendMessage(new TextMessage(welcomeMsg));
        
        // 发送最近的一些日志
        sendRecentLogs(session);
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        sessions.remove(session);
        logger.info("📱 日志监控连接关闭: {}", session.getId());
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("📱 日志监控连接错误: {}", session.getId(), exception);
        sessions.remove(session);
    }
    
    /**
     * 添加日志到队列
     */
    public static void addLog(String level, String logger, String message) {
        String formattedLog = formatLogMessage(level, logger, message);
        logQueue.offer(formattedLog);
        
        // 限制队列大小，避免内存溢出
        while (logQueue.size() > 1000) {
            logQueue.poll();
        }
    }
    
    /**
     * 格式化日志消息
     */
    private static String formatLogMessage(String level, String logger, String message) {
        String timestamp = LocalDateTime.now().format(timeFormatter);
        String levelIcon = getLevelIcon(level);
        String shortLogger = getShortLoggerName(logger);
        
        return String.format("[%s] %s %s - %s", timestamp, levelIcon, shortLogger, message);
    }
    
    /**
     * 获取日志级别图标
     */
    private static String getLevelIcon(String level) {
        switch (level.toUpperCase()) {
            case "ERROR": return "❌";
            case "WARN": return "⚠️";
            case "INFO": return "ℹ️";
            case "DEBUG": return "🔧";
            case "TRACE": return "🔍";
            default: return "📝";
        }
    }
    
    /**
     * 获取简短的Logger名称
     */
    private static String getShortLoggerName(String logger) {
        if (logger == null) return "System";
        
        // 提取类名
        String[] parts = logger.split("\\.");
        if (parts.length > 0) {
            String className = parts[parts.length - 1];
            // 如果是实现类，去掉Impl后缀
            if (className.endsWith("Impl")) {
                className = className.substring(0, className.length() - 4);
            }
            return className;
        }
        return logger;
    }
    
    /**
     * 广播日志消息
     */
    private static void broadcastLog(String message) {
        sessions.removeIf(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                    return false;
                } else {
                    return true;
                }
            } catch (IOException e) {
                logger.warn("发送日志消息失败: {}", e.getMessage());
                return true;
            }
        });
    }
    
    /**
     * 发送最近的日志
     */
    private void sendRecentLogs(WebSocketSession session) {
        try {
            // 发送一些示例日志
            String[] recentLogs = {
                formatLogMessage("INFO", "DbCopyDataApplication", "🚀 应用启动成功"),
                formatLogMessage("INFO", "DatabaseConfig", "✅ 数据库连接池初始化完成"),
                formatLogMessage("INFO", "AiConfiguration", "🤖 AI服务配置完成"),
                formatLogMessage("INFO", "LogWebSocket", "📊 实时日志监控服务已启动")
            };
            
            for (String log : recentLogs) {
                session.sendMessage(new TextMessage(log + "\n"));
                Thread.sleep(100); // 稍微延迟，模拟实时效果
            }
        } catch (Exception e) {
            logger.warn("发送最近日志失败: {}", e.getMessage());
        }
    }
    
    /**
     * 手动推送系统状态日志
     */
    public static void pushSystemStatus() {
        addLog("INFO", "SystemMonitor", "📊 系统运行正常 - 内存使用: " + getMemoryUsage());
        addLog("INFO", "SystemMonitor", "🔗 活跃连接数: " + sessions.size());
    }
    
    /**
     * 获取内存使用情况
     */
    private static String getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        return String.format("%.1fMB/%.1fMB", 
                           usedMemory / 1024.0 / 1024.0, 
                           totalMemory / 1024.0 / 1024.0);
    }
}
