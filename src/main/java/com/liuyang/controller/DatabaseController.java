package com.liuyang.controller;

import com.liuyang.dto.DatabaseInfo;
import com.liuyang.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库控制器
 */
@RestController
@RequestMapping("/api/databases")
public class DatabaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseController.class);
    
    @Autowired
    private DatabaseService databaseService;
    
    /**
     * 获取所有数据库列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllDatabases() {
        logger.info("获取所有数据库列表");
        
        Map<String, Object> response = new HashMap<>();
        try {
            List<DatabaseInfo> databases = databaseService.getAllDatabases();
            response.put("success", true);
            response.put("data", databases);
            response.put("message", "获取数据库列表成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取数据库列表失败", e);
            response.put("success", false);
            response.put("message", "获取数据库列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 测试数据库连接
     */
    @GetMapping("/{databaseName}/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection(@PathVariable String databaseName) {
        logger.info("测试数据库连接: {}", databaseName);
        
        Map<String, Object> response = new HashMap<>();
        try {
            boolean connected = databaseService.testConnection(databaseName);
            response.put("success", true);
            response.put("data", Map.of("connected", connected));
            response.put("message", connected ? "连接成功" : "连接失败");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("测试数据库连接失败", e);
            response.put("success", false);
            response.put("message", "测试连接失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 检查数据库是否存在
     */
    @GetMapping("/{databaseName}/exists")
    public ResponseEntity<Map<String, Object>> checkDatabaseExists(@PathVariable String databaseName) {
        logger.info("检查数据库是否存在: {}", databaseName);
        
        Map<String, Object> response = new HashMap<>();
        try {
            boolean exists = databaseService.databaseExists(databaseName);
            response.put("success", true);
            response.put("data", Map.of("exists", exists));
            response.put("message", exists ? "数据库存在" : "数据库不存在");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("检查数据库是否存在失败", e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }
}
