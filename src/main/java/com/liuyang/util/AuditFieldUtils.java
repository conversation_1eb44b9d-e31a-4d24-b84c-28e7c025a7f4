package com.liuyang.util;

import com.liuyang.dto.ColumnInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 审计字段工具类
 * 用于处理标准审计字段的检查、补充和管理
 */
public class AuditFieldUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(AuditFieldUtils.class);
    
    /**
     * 标准审计字段定义
     */
    public static class AuditField {
        private final String fieldName;
        private final String dataType;
        private final String comment;
        private final boolean nullable;
        private final String defaultValue;
        
        public AuditField(String fieldName, String dataType, String comment, boolean nullable, String defaultValue) {
            this.fieldName = fieldName;
            this.dataType = dataType;
            this.comment = comment;
            this.nullable = nullable;
            this.defaultValue = defaultValue;
        }
        
        public String getFieldName() { return fieldName; }
        public String getDataType() { return dataType; }
        public String getComment() { return comment; }
        public boolean isNullable() { return nullable; }
        public String getDefaultValue() { return defaultValue; }
    }
    
    /**
     * 标准审计字段列表
     */
    private static final List<AuditField> STANDARD_AUDIT_FIELDS = Arrays.asList(
        new AuditField("create_user", "varchar(200)", "创建人名称", true, null),
        new AuditField("create_time", "datetime", "记录创建时间", true, null),
        new AuditField("update_user", "varchar(200)", "更新人名称", true, null),
        new AuditField("update_time", "datetime", "记录更新时间", true, null)
    );
    
    /**
     * 审计字段名称集合（用于快速查找）
     */
    private static final Set<String> AUDIT_FIELD_NAMES = new HashSet<>(Arrays.asList(
        "create_user", "create_time", "update_user", "update_time",
        // 常见的变体
        "created_by", "created_at", "updated_by", "updated_at",
        "creator", "create_date", "updater", "update_date"
    ));
    
    /**
     * 检查字段是否为审计字段
     */
    public static boolean isAuditField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        return AUDIT_FIELD_NAMES.contains(fieldName.toLowerCase());
    }
    
    /**
     * 检查现有字段中缺失的标准审计字段
     */
    public static List<AuditField> getMissingAuditFields(List<ColumnInfo> existingColumns) {
        Set<String> existingFieldNames = new HashSet<>();
        for (ColumnInfo column : existingColumns) {
            existingFieldNames.add(column.getColumnName().toLowerCase());
        }
        
        List<AuditField> missingFields = new ArrayList<>();
        for (AuditField auditField : STANDARD_AUDIT_FIELDS) {
            if (!existingFieldNames.contains(auditField.getFieldName().toLowerCase())) {
                // 检查是否有类似的字段（变体）
                boolean hasVariant = false;
                for (String existingField : existingFieldNames) {
                    if (isFieldVariant(auditField.getFieldName(), existingField)) {
                        hasVariant = true;
                        break;
                    }
                }
                
                if (!hasVariant) {
                    missingFields.add(auditField);
                }
            }
        }
        
        logger.info("检查到 {} 个缺失的审计字段: {}", 
                   missingFields.size(), 
                   missingFields.stream().map(AuditField::getFieldName).toArray());
        
        return missingFields;
    }
    
    /**
     * 检查两个字段名是否为同一审计字段的变体
     */
    private static boolean isFieldVariant(String standardField, String existingField) {
        Map<String, Set<String>> variants = new HashMap<>();
        variants.put("create_user", new HashSet<>(Arrays.asList("created_by", "creator")));
        variants.put("create_time", new HashSet<>(Arrays.asList("created_at", "create_date")));
        variants.put("update_user", new HashSet<>(Arrays.asList("updated_by", "updater")));
        variants.put("update_time", new HashSet<>(Arrays.asList("updated_at", "update_date")));
        
        Set<String> fieldVariants = variants.get(standardField.toLowerCase());
        return fieldVariants != null && fieldVariants.contains(existingField.toLowerCase());
    }
    
    /**
     * 创建审计字段的ColumnInfo对象
     */
    public static ColumnInfo createAuditColumnInfo(AuditField auditField) {
        ColumnInfo columnInfo = new ColumnInfo();
        columnInfo.setColumnName(auditField.getFieldName());
        columnInfo.setDataType(auditField.getDataType());
        columnInfo.setComment(auditField.getComment());
        columnInfo.setNullable(auditField.isNullable());
        columnInfo.setDefaultValue(auditField.getDefaultValue());
        columnInfo.setPrimaryKey(false);
        columnInfo.setAutoIncrement(false);
        columnInfo.setAutoAdded(true); // 标记为自动添加的字段
        
        // 设置字段大小
        if (auditField.getDataType().startsWith("varchar")) {
            columnInfo.setColumnSize(200);
        }
        
        return columnInfo;
    }
    
    /**
     * 为表结构补充缺失的审计字段
     */
    public static List<ColumnInfo> addMissingAuditFields(List<ColumnInfo> existingColumns) {
        List<AuditField> missingFields = getMissingAuditFields(existingColumns);
        
        if (missingFields.isEmpty()) {
            logger.info("表结构已包含所有标准审计字段，无需补充");
            return existingColumns;
        }
        
        List<ColumnInfo> result = new ArrayList<>(existingColumns);
        
        // 在合适的位置插入审计字段（通常在表的末尾）
        for (AuditField missingField : missingFields) {
            ColumnInfo auditColumn = createAuditColumnInfo(missingField);
            result.add(auditColumn);
            logger.info("自动添加审计字段: {} ({})", missingField.getFieldName(), missingField.getComment());
        }
        
        return result;
    }
    
    /**
     * 获取所有标准审计字段名称
     */
    public static Set<String> getStandardAuditFieldNames() {
        Set<String> names = new HashSet<>();
        for (AuditField field : STANDARD_AUDIT_FIELDS) {
            names.add(field.getFieldName());
        }
        return names;
    }
    
    /**
     * 过滤掉自动添加的审计字段（用于验证时排除）
     */
    public static List<ColumnInfo> filterOutAutoAddedFields(List<ColumnInfo> columns) {
        List<ColumnInfo> filtered = new ArrayList<>();
        for (ColumnInfo column : columns) {
            if (!column.isAutoAdded()) {
                filtered.add(column);
            }
        }
        
        logger.debug("过滤前字段数: {}, 过滤后字段数: {}", columns.size(), filtered.size());
        return filtered;
    }
}
