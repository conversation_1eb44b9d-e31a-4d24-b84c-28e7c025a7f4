package com.liuyang.util;

import org.springframework.stereotype.Component;

/**
 * 雪花算法ID生成器
 * 简化版实现，适用于单机或小规模集群
 */
@Component
public class SnowflakeIdGenerator {
    
    // 起始时间戳 (2022-01-01 00:00:00 UTC)
    private final long epoch = 1640995200000L;
    
    // 机器ID位数
    private final long machineIdBits = 5L;
    
    // 数据中心ID位数
    private final long datacenterIdBits = 5L;
    
    // 序列号位数
    private final long sequenceBits = 12L;
    
    // 机器ID最大值
    private final long maxMachineId = ~(-1L << machineIdBits);
    
    // 数据中心ID最大值
    private final long maxDatacenterId = ~(-1L << datacenterIdBits);
    
    // 序列号最大值
    private final long sequenceMask = ~(-1L << sequenceBits);
    
    // 机器ID左移位数
    private final long machineIdShift = sequenceBits;
    
    // 数据中心ID左移位数
    private final long datacenterIdShift = sequenceBits + machineIdBits;
    
    // 时间戳左移位数
    private final long timestampLeftShift = sequenceBits + machineIdBits + datacenterIdBits;
    
    private long machineId;
    private long datacenterId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;
    
    public SnowflakeIdGenerator() {
        this(1L, 1L);
    }
    
    public SnowflakeIdGenerator(long machineId, long datacenterId) {
        if (machineId > maxMachineId || machineId < 0) {
            throw new IllegalArgumentException(String.format("机器ID不能大于%d或小于0", maxMachineId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(String.format("数据中心ID不能大于%d或小于0", maxDatacenterId));
        }
        this.machineId = machineId;
        this.datacenterId = datacenterId;
    }
    
    /**
     * 生成下一个ID
     */
    public synchronized long nextId() {
        long timestamp = timeGen();
        
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(String.format("时钟回拨，拒绝生成ID %d 毫秒", lastTimestamp - timestamp));
        }
        
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }
        
        lastTimestamp = timestamp;
        
        return ((timestamp - epoch) << timestampLeftShift) |
               (datacenterId << datacenterIdShift) |
               (machineId << machineIdShift) |
               sequence;
    }
    
    /**
     * 等待下一毫秒
     */
    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }
    
    /**
     * 获取当前时间戳
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }
}
