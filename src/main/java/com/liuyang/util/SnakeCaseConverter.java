package com.liuyang.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Snake Case 转换工具类
 * 将各种命名格式转换为下划线分隔的snake_case格式
 */
public class SnakeCaseConverter {

    // 常见字段名映射
    private static final Map<String, String> COMMON_FIELD_MAPPINGS = new HashMap<>();
    
    static {
        // 时间相关字段
        COMMON_FIELD_MAPPINGS.put("createtime", "create_time");
        COMMON_FIELD_MAPPINGS.put("createdate", "create_time");
        COMMON_FIELD_MAPPINGS.put("updatetime", "update_time");
        COMMON_FIELD_MAPPINGS.put("updatedate", "update_time");
        COMMON_FIELD_MAPPINGS.put("deletetime", "delete_time");
        COMMON_FIELD_MAPPINGS.put("deletedate", "delete_time");
        
        // 用户相关字段
        COMMON_FIELD_MAPPINGS.put("createuser", "create_user");
        COMMON_FIELD_MAPPINGS.put("updateuser", "update_user");
        COMMON_FIELD_MAPPINGS.put("deleteuser", "delete_user");
        COMMON_FIELD_MAPPINGS.put("username", "user_name");
        COMMON_FIELD_MAPPINGS.put("userid", "user_id");
        
        // 状态相关字段
        COMMON_FIELD_MAPPINGS.put("isactive", "is_active");
        COMMON_FIELD_MAPPINGS.put("isdeleted", "is_deleted");
        COMMON_FIELD_MAPPINGS.put("isenabled", "is_enabled");
        COMMON_FIELD_MAPPINGS.put("isvalid", "is_valid");
        
        // 金额相关字段
        COMMON_FIELD_MAPPINGS.put("totalamount", "total_amount");
        COMMON_FIELD_MAPPINGS.put("unitprice", "unit_price");
        COMMON_FIELD_MAPPINGS.put("discountamount", "discount_amount");
        
        // 数量相关字段
        COMMON_FIELD_MAPPINGS.put("itemcount", "item_count");
        COMMON_FIELD_MAPPINGS.put("totalnum", "total_num");
        COMMON_FIELD_MAPPINGS.put("maxlimit", "max_limit");
        
        // 业务字段
        COMMON_FIELD_MAPPINGS.put("businessid", "business_id");
        COMMON_FIELD_MAPPINGS.put("orderid", "order_id");
        COMMON_FIELD_MAPPINGS.put("orderstatus", "order_status");
        COMMON_FIELD_MAPPINGS.put("userstatus", "user_status");
        COMMON_FIELD_MAPPINGS.put("paymentstatus", "payment_status");
    }

    /**
     * 将字段名转换为snake_case格式
     * 
     * @param fieldName 原始字段名
     * @return snake_case格式的字段名
     */
    public static String toSnakeCase(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return fieldName;
        }
        
        String normalized = fieldName.trim().toLowerCase();
        
        // 检查是否有直接映射
        if (COMMON_FIELD_MAPPINGS.containsKey(normalized)) {
            return COMMON_FIELD_MAPPINGS.get(normalized);
        }
        
        // 如果已经是snake_case格式，直接返回
        if (isSnakeCase(fieldName)) {
            return fieldName.toLowerCase();
        }
        
        // 转换驼峰命名为snake_case
        return convertCamelToSnake(fieldName);
    }

    /**
     * 检查字段名是否已经是snake_case格式
     */
    private static boolean isSnakeCase(String fieldName) {
        return Pattern.matches("^[a-z][a-z0-9_]*$", fieldName);
    }

    /**
     * 将驼峰命名转换为snake_case
     */
    private static String convertCamelToSnake(String camelCase) {
        // 处理连续大写字母的情况，如 XMLHttpRequest -> xml_http_request
        String result = camelCase.replaceAll("([A-Z]+)([A-Z][a-z])", "$1_$2");
        
        // 处理普通驼峰命名，如 userName -> user_name
        result = result.replaceAll("([a-z\\d])([A-Z])", "$1_$2");
        
        return result.toLowerCase();
    }

    /**
     * 批量转换字段名
     */
    public static Map<String, String> convertFieldNames(Map<String, String> originalFields) {
        Map<String, String> convertedFields = new HashMap<>();
        
        for (Map.Entry<String, String> entry : originalFields.entrySet()) {
            String originalName = entry.getKey();
            String convertedName = toSnakeCase(originalName);
            convertedFields.put(originalName, convertedName);
        }
        
        return convertedFields;
    }

    /**
     * 获取字段转换建议
     */
    public static String getConversionSuggestion(String originalFieldName) {
        String converted = toSnakeCase(originalFieldName);
        
        if (originalFieldName.equals(converted)) {
            return "字段名已符合snake_case规范";
        } else {
            return String.format("建议将 '%s' 转换为 '%s'", originalFieldName, converted);
        }
    }
}
