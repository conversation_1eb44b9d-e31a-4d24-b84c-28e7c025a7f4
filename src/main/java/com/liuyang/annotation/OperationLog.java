package com.liuyang.annotation;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作类型
     * 例如: USER_LOGIN, DATABASE_MIGRATION, CONFIG_MANAGEMENT
     */
    String operationType();

    /**
     * 操作描述
     * 支持SpEL表达式，可以使用方法参数
     * 例如: "创建数据库配置: #{#configName}"
     */
    String operationDesc();

    /**
     * 目标类型
     * 例如: USER, TABLE, CONFIG
     */
    String targetType() default "";

    /**
     * 目标ID
     * 支持SpEL表达式，可以使用方法参数
     * 例如: "#{#configId}"
     */
    String targetId() default "";

    /**
     * 是否记录请求参数
     * 默认为true
     */
    boolean recordParams() default true;

    /**
     * 是否记录响应结果
     * 默认为false，避免记录敏感信息
     */
    boolean recordResponse() default false;

    /**
     * 是否异步记录日志
     * 默认为true，提高性能
     */
    boolean async() default true;

    /**
     * 忽略的参数名称
     * 这些参数不会被记录到日志中
     */
    String[] ignoreParams() default {"password", "token", "secret"};
}
