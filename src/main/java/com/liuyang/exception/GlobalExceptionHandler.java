package com.liuyang.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import jakarta.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(DatabaseException.class)
    public ResponseEntity<Map<String, Object>> handleDatabaseException(DatabaseException e) {
        logger.error("数据库操作异常: {}", e.getMessage(), e);
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", e.getMessage());
        response.put("code", "DATABASE_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Map<String, Object>> handleBindException(BindException e) {
        logger.error("参数校验异常: {}", e.getMessage());
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "参数校验失败: " + e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        response.put("code", "VALIDATION_ERROR");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(cn.dev33.satoken.exception.NotLoginException.class)
    public ResponseEntity<Map<String, Object>> handleNotLoginException(cn.dev33.satoken.exception.NotLoginException e) {
        logger.debug("用户未登录: {}", e.getMessage());
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "用户未登录，请先登录");
        response.put("code", "NOT_LOGIN");
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
    
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Map<String, Object>> handleNoResourceFoundException(
            NoResourceFoundException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 对于favicon.ico请求，只记录debug级别日志，不记录错误
        if (requestURI != null && requestURI.endsWith("favicon.ico")) {
            logger.debug("Favicon请求: {}", requestURI);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        // 只处理API请求的404错误，静态资源返回404但不处理
        if (requestURI != null && requestURI.startsWith("/api/")) {
            logger.warn("API资源未找到: {}", requestURI);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "API资源未找到");
            response.put("code", "RESOURCE_NOT_FOUND");
            response.put("path", requestURI);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }

        // 对于静态资源，返回404状态但不返回JSON响应
        logger.debug("静态资源未找到: {}", requestURI);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
    }

    @ExceptionHandler(org.apache.catalina.connector.ClientAbortException.class)
    public ResponseEntity<Map<String, Object>> handleClientAbortException(
            org.apache.catalina.connector.ClientAbortException e) {
        // 客户端断开连接异常（如Broken pipe），通常是用户操作导致的，不记录为错误
        if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
            logger.debug("客户端断开连接: {}", e.getMessage());
        } else {
            logger.info("客户端中止连接: {}", e.getMessage());
        }
        
        // 不返回响应，因为客户端已经断开连接
        return null;
    }
    
    @ExceptionHandler(java.io.IOException.class)
    public ResponseEntity<Map<String, Object>> handleIOException(java.io.IOException e) {
        // 处理IO异常，特别是Broken pipe
        if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
            logger.debug("IO异常 - 客户端断开连接: {}", e.getMessage());
            return null; // 不返回响应
        }
        
        // 其他IO异常正常记录
        logger.warn("IO异常: {}", e.getMessage(), e);
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "IO操作异常");
        response.put("code", "IO_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        // 过滤掉一些常见的客户端断开连接异常
        if (isBrokenPipeException(e)) {
            logger.debug("客户端连接异常（已过滤）: {}", e.getMessage());
            return null;
        }
        
        logger.error("系统异常: {}", e.getMessage(), e);
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "系统内部错误");
        response.put("code", "SYSTEM_ERROR");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 判断是否为客户端断开连接导致的异常
     */
    private boolean isBrokenPipeException(Exception e) {
        if (e == null) return false;
        
        String message = e.getMessage();
        if (message == null) return false;
        
        // 检查异常消息
        if (message.contains("Broken pipe") || 
            message.contains("Connection reset by peer") ||
            message.contains("客户端断开") ||
            message.contains("An established connection was aborted")) {
            return true;
        }
        
        // 检查异常类型
        if (e instanceof org.apache.catalina.connector.ClientAbortException ||
            e instanceof java.net.SocketException) {
            return true;
        }
        
        // 检查根异常原因
        Throwable cause = e.getCause();
        while (cause != null) {
            if (cause.getMessage() != null && cause.getMessage().contains("Broken pipe")) {
                return true;
            }
            cause = cause.getCause();
        }
        
        return false;
    }
}
