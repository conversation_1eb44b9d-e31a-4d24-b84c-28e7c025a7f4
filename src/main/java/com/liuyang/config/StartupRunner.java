package com.liuyang.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 应用启动完成后的处理器
 */
@Component
public class StartupRunner implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupRunner.class);
    
    @Value("${server.port}")
    private String port;
    
    @Value("${server.servlet.context-path:}")
    private String contextPath;
    
    private final Environment environment;
    
    public StartupRunner(Environment environment) {
        this.environment = environment;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            String ip = InetAddress.getLocalHost().getHostAddress();
            String hostName = InetAddress.getLocalHost().getHostName();
            
            // 处理上下文路径
            String context = contextPath == null || contextPath.isEmpty() ? "" : contextPath;
            if (!context.isEmpty() && !context.startsWith("/")) {
                context = "/" + context;
            }
            
            // 获取活动的配置文件
            String[] activeProfiles = environment.getActiveProfiles();
            String profileInfo = activeProfiles.length > 0 ? 
                " (Profile: " + String.join(", ", activeProfiles) + ")" : "";
            
            // 打印启动信息
            logger.info("\n" +
                "==================================================\n" +
                "🎉 应用启动成功！" + profileInfo + "\n" +
                "==================================================\n" +
                "📱 本地访问地址:\n" +
                "   http://localhost:" + port + context + "\n" +
                "==================================================\n" +
                "💡 浏览器主页设置建议:\n" +
                "   请将浏览器主页设置为: http://localhost:" + port + context + "\n" +
                "   避免设置为: http://localhost:" + port + "/login.html\n" +
                "==================================================");
            
        } catch (UnknownHostException e) {
            logger.warn("无法获取本机IP地址，使用localhost");
            logger.info("\n" +
                "==================================================\n" +
                "🎉 应用启动成功！\n" +
                "==================================================\n" +
                "📱 访问地址: http://localhost:" + port + contextPath + "\n" +
                "==================================================");
        }
    }
    
    /**
     * 判断是否为开发环境
     */
    private boolean isDevelopmentProfile() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取配置属性
     */
    private String getProperty(String key, String defaultValue) {
        return environment.getProperty(key, defaultValue);
    }
}
