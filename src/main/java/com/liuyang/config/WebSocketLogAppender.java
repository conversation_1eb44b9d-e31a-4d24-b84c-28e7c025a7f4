package com.liuyang.config;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import com.liuyang.controller.LogWebSocketController;

/**
 * 自定义日志拦截器，将日志推送到WebSocket
 */
public class WebSocketLogAppender extends AppenderBase<ILoggingEvent> {
    
    @Override
    protected void append(ILoggingEvent event) {
        try {
            // 只推送INFO及以上级别的日志
            if (event.getLevel().levelInt >= ch.qos.logback.classic.Level.INFO.levelInt) {
                String loggerName = event.getLoggerName();
                String level = event.getLevel().toString();
                String message = event.getFormattedMessage();
                
                // 过滤掉一些不重要的日志
                if (shouldIncludeLog(loggerName, message)) {
                    LogWebSocketController.addLog(level, loggerName, message);
                }
            }
        } catch (Exception e) {
            // 避免日志拦截器本身出错影响系统
            System.err.println("WebSocket日志推送失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否应该包含此日志
     */
    private boolean shouldIncludeLog(String loggerName, String message) {
        // 过滤掉一些噪音日志
        if (loggerName.contains("org.springframework.web.servlet.DispatcherServlet")) {
            return false;
        }
        if (loggerName.contains("org.apache.catalina")) {
            return false;
        }
        if (loggerName.contains("org.hibernate")) {
            return false;
        }
        if (message.contains("DEBUG") || message.contains("TRACE")) {
            return false;
        }
        
        // 包含重要的业务日志
        return loggerName.contains("com.liuyang") || 
               message.contains("迁移") || 
               message.contains("数据库") ||
               message.contains("AI") ||
               message.contains("🚀") ||
               message.contains("✅") ||
               message.contains("❌") ||
               message.contains("⚠️") ||
               message.contains("📊");
    }
}
