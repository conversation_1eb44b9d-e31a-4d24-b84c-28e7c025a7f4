package com.liuyang.config;

import com.liuyang.controller.LogWebSocketController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket配置
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private LogWebSocketController logWebSocketController;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册日志WebSocket处理器
        registry.addHandler(logWebSocketController, "/ws/logs")
                .setAllowedOrigins("*"); // 允许所有来源，生产环境应该限制
    }
}
