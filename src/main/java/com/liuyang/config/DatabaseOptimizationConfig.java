package com.liuyang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 数据库优化配置
 */
@Configuration
@ConfigurationProperties(prefix = "database.optimization")
public class DatabaseOptimizationConfig {
    
    private MySQL mysql = new MySQL();
    private PostgreSQL postgresql = new PostgreSQL();
    
    public static class MySQL {
        /**
         * InnoDB锁等待超时时间（秒）
         */
        private int innodbLockWaitTimeout = 300;
        
        /**
         * 批量插入缓冲区大小（字节）
         */
        private long bulkInsertBufferSize = 8388608L; // 8MB
        
        /**
         * 最大允许数据包大小（字节）
         */
        private long maxAllowedPacket = 67108864L; // 64MB
        
        // Getters and Setters
        public int getInnodbLockWaitTimeout() {
            return innodbLockWaitTimeout;
        }
        
        public void setInnodbLockWaitTimeout(int innodbLockWaitTimeout) {
            this.innodbLockWaitTimeout = innodbLockWaitTimeout;
        }
        
        public long getBulkInsertBufferSize() {
            return bulkInsertBufferSize;
        }
        
        public void setBulkInsertBufferSize(long bulkInsertBufferSize) {
            this.bulkInsertBufferSize = bulkInsertBufferSize;
        }
        
        public long getMaxAllowedPacket() {
            return maxAllowedPacket;
        }
        
        public void setMaxAllowedPacket(long maxAllowedPacket) {
            this.maxAllowedPacket = maxAllowedPacket;
        }
    }
    
    public static class PostgreSQL {
        /**
         * 语句超时时间（毫秒）
         */
        private long statementTimeout = 300000L; // 5分钟
        
        /**
         * 锁超时时间（毫秒）
         */
        private long lockTimeout = 300000L; // 5分钟
        
        /**
         * 工作内存大小（KB）
         */
        private int workMem = 4096; // 4MB
        
        // Getters and Setters
        public long getStatementTimeout() {
            return statementTimeout;
        }
        
        public void setStatementTimeout(long statementTimeout) {
            this.statementTimeout = statementTimeout;
        }
        
        public long getLockTimeout() {
            return lockTimeout;
        }
        
        public void setLockTimeout(long lockTimeout) {
            this.lockTimeout = lockTimeout;
        }
        
        public int getWorkMem() {
            return workMem;
        }
        
        public void setWorkMem(int workMem) {
            this.workMem = workMem;
        }
    }
    
    // Main class getters and setters
    public MySQL getMysql() {
        return mysql;
    }
    
    public void setMysql(MySQL mysql) {
        this.mysql = mysql;
    }
    
    public PostgreSQL getPostgresql() {
        return postgresql;
    }
    
    public void setPostgresql(PostgreSQL postgresql) {
        this.postgresql = postgresql;
    }
}
