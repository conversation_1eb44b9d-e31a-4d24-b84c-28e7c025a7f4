package com.liuyang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 高性能迁移配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.migration.high-performance")
public class HighPerformanceMigrationConfig {
    
    // 分片配置
    private int shardSize = 100000;                    // 分片大小
    private int maxParallelThreads = 16;               // 最大并行线程数
    private int minParallelThreads = 1;                // 最小并行线程数
    
    // 批次配置
    private int maxBatchSize = 50000;                  // 最大批次大小
    private int minBatchSize = 5000;                   // 最小批次大小
    private int optimalBatchSize = 20000;              // 最优批次大小
    
    // 连接池配置
    private int connectionPoolSize = 20;               // 连接池大小
    private int connectionTimeout = 60000;             // 连接超时(ms)
    private int socketTimeout = 300000;                // Socket超时(ms)
    private int idleTimeout = 300000;                  // 空闲超时(ms)
    private int maxLifetime = 1800000;                 // 最大生命周期(ms)
    
    // 监控配置
    private int progressReportInterval = 10;           // 进度报告间隔(秒)
    private boolean enableDetailedLogging = false;     // 启用详细日志
    private boolean enablePerformanceMetrics = true;   // 启用性能指标
    
    // 优化配置
    private boolean enableBatchRewrite = true;         // 启用批量重写
    private boolean enablePreparedStatementCache = true; // 启用预编译缓存
    private boolean enableConnectionOptimization = true; // 启用连接优化
    private boolean enableDatabaseSpecificOptimization = true; // 启用数据库特定优化
    
    // 内存配置
    private int fetchSize = 10000;                     // 获取大小
    private int resultSetCacheSize = 1000;             // 结果集缓存大小
    
    // 错误处理配置
    private int maxRetryAttempts = 3;                  // 最大重试次数
    private int retryDelayMs = 1000;                   // 重试延迟(ms)
    private boolean failFastOnError = false;           // 错误时快速失败
    
    // 性能阈值配置
    private long largeDatasetThreshold = 1000000;      // 大数据集阈值
    private long hugeDatasetThreshold = 10000000;      // 超大数据集阈值
    private double minAcceptableSpeed = 1000.0;        // 最小可接受速度(条/秒)
    
    // Getters and Setters
    public int getShardSize() {
        return shardSize;
    }
    
    public void setShardSize(int shardSize) {
        this.shardSize = shardSize;
    }
    
    public int getMaxParallelThreads() {
        return maxParallelThreads;
    }
    
    public void setMaxParallelThreads(int maxParallelThreads) {
        this.maxParallelThreads = maxParallelThreads;
    }
    
    public int getMinParallelThreads() {
        return minParallelThreads;
    }
    
    public void setMinParallelThreads(int minParallelThreads) {
        this.minParallelThreads = minParallelThreads;
    }
    
    public int getMaxBatchSize() {
        return maxBatchSize;
    }
    
    public void setMaxBatchSize(int maxBatchSize) {
        this.maxBatchSize = maxBatchSize;
    }
    
    public int getMinBatchSize() {
        return minBatchSize;
    }
    
    public void setMinBatchSize(int minBatchSize) {
        this.minBatchSize = minBatchSize;
    }
    
    public int getOptimalBatchSize() {
        return optimalBatchSize;
    }
    
    public void setOptimalBatchSize(int optimalBatchSize) {
        this.optimalBatchSize = optimalBatchSize;
    }
    
    public int getConnectionPoolSize() {
        return connectionPoolSize;
    }
    
    public void setConnectionPoolSize(int connectionPoolSize) {
        this.connectionPoolSize = connectionPoolSize;
    }
    
    public int getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public int getSocketTimeout() {
        return socketTimeout;
    }
    
    public void setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
    }
    
    public int getIdleTimeout() {
        return idleTimeout;
    }
    
    public void setIdleTimeout(int idleTimeout) {
        this.idleTimeout = idleTimeout;
    }
    
    public int getMaxLifetime() {
        return maxLifetime;
    }
    
    public void setMaxLifetime(int maxLifetime) {
        this.maxLifetime = maxLifetime;
    }
    
    public int getProgressReportInterval() {
        return progressReportInterval;
    }
    
    public void setProgressReportInterval(int progressReportInterval) {
        this.progressReportInterval = progressReportInterval;
    }
    
    public boolean isEnableDetailedLogging() {
        return enableDetailedLogging;
    }
    
    public void setEnableDetailedLogging(boolean enableDetailedLogging) {
        this.enableDetailedLogging = enableDetailedLogging;
    }
    
    public boolean isEnablePerformanceMetrics() {
        return enablePerformanceMetrics;
    }
    
    public void setEnablePerformanceMetrics(boolean enablePerformanceMetrics) {
        this.enablePerformanceMetrics = enablePerformanceMetrics;
    }
    
    public boolean isEnableBatchRewrite() {
        return enableBatchRewrite;
    }
    
    public void setEnableBatchRewrite(boolean enableBatchRewrite) {
        this.enableBatchRewrite = enableBatchRewrite;
    }
    
    public boolean isEnablePreparedStatementCache() {
        return enablePreparedStatementCache;
    }
    
    public void setEnablePreparedStatementCache(boolean enablePreparedStatementCache) {
        this.enablePreparedStatementCache = enablePreparedStatementCache;
    }
    
    public boolean isEnableConnectionOptimization() {
        return enableConnectionOptimization;
    }
    
    public void setEnableConnectionOptimization(boolean enableConnectionOptimization) {
        this.enableConnectionOptimization = enableConnectionOptimization;
    }
    
    public boolean isEnableDatabaseSpecificOptimization() {
        return enableDatabaseSpecificOptimization;
    }
    
    public void setEnableDatabaseSpecificOptimization(boolean enableDatabaseSpecificOptimization) {
        this.enableDatabaseSpecificOptimization = enableDatabaseSpecificOptimization;
    }
    
    public int getFetchSize() {
        return fetchSize;
    }
    
    public void setFetchSize(int fetchSize) {
        this.fetchSize = fetchSize;
    }
    
    public int getResultSetCacheSize() {
        return resultSetCacheSize;
    }
    
    public void setResultSetCacheSize(int resultSetCacheSize) {
        this.resultSetCacheSize = resultSetCacheSize;
    }
    
    public int getMaxRetryAttempts() {
        return maxRetryAttempts;
    }
    
    public void setMaxRetryAttempts(int maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }
    
    public int getRetryDelayMs() {
        return retryDelayMs;
    }
    
    public void setRetryDelayMs(int retryDelayMs) {
        this.retryDelayMs = retryDelayMs;
    }
    
    public boolean isFailFastOnError() {
        return failFastOnError;
    }
    
    public void setFailFastOnError(boolean failFastOnError) {
        this.failFastOnError = failFastOnError;
    }
    
    public long getLargeDatasetThreshold() {
        return largeDatasetThreshold;
    }
    
    public void setLargeDatasetThreshold(long largeDatasetThreshold) {
        this.largeDatasetThreshold = largeDatasetThreshold;
    }
    
    public long getHugeDatasetThreshold() {
        return hugeDatasetThreshold;
    }
    
    public void setHugeDatasetThreshold(long hugeDatasetThreshold) {
        this.hugeDatasetThreshold = hugeDatasetThreshold;
    }
    
    public double getMinAcceptableSpeed() {
        return minAcceptableSpeed;
    }
    
    public void setMinAcceptableSpeed(double minAcceptableSpeed) {
        this.minAcceptableSpeed = minAcceptableSpeed;
    }
    
    /**
     * 根据数据量计算最优配置
     */
    public MigrationStrategy calculateOptimalStrategy(long totalRows) {
        MigrationStrategy strategy = new MigrationStrategy();
        
        if (totalRows < largeDatasetThreshold) {
            // 中等数据量
            strategy.setBatchSize(Math.min(optimalBatchSize, maxBatchSize));
            strategy.setThreadCount(Math.min(4, maxParallelThreads));
            strategy.setShardSize(shardSize);
        } else if (totalRows < hugeDatasetThreshold) {
            // 大数据量
            strategy.setBatchSize(maxBatchSize);
            strategy.setThreadCount(Math.min(8, maxParallelThreads));
            strategy.setShardSize(shardSize / 2); // 更小的分片
        } else {
            // 超大数据量
            strategy.setBatchSize(maxBatchSize);
            strategy.setThreadCount(maxParallelThreads);
            strategy.setShardSize(shardSize / 4); // 最小分片
        }
        
        return strategy;
    }
    
    /**
     * 迁移策略
     */
    public static class MigrationStrategy {
        private int batchSize;
        private int threadCount;
        private int shardSize;
        
        // Getters and Setters
        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
        
        public int getThreadCount() { return threadCount; }
        public void setThreadCount(int threadCount) { this.threadCount = threadCount; }
        
        public int getShardSize() { return shardSize; }
        public void setShardSize(int shardSize) { this.shardSize = shardSize; }
    }
}
