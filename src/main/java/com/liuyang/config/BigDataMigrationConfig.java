package com.liuyang.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 大数据量迁移配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.migration.big-data")
public class BigDataMigrationConfig {
    
    /**
     * 大数据量阈值（超过此数量使用优化策略）
     */
    private long largeDataThreshold = 100000;
    
    /**
     * 小数据量批次大小
     */
    private int smallBatchSize = 2000;
    
    /**
     * 中等数据量批次大小
     */
    private int mediumBatchSize = 5000;
    
    /**
     * 大数据量批次大小
     */
    private int largeBatchSize = 8000;
    
    /**
     * 超大数据量批次大小
     */
    private int xlargeBatchSize = 10000;
    
    /**
     * 最小线程数
     */
    private int minThreads = 1;
    
    /**
     * 最大线程数
     */
    private int maxThreads = 8;
    
    /**
     * 连接池大小
     */
    private int connectionPoolSize = 10;
    
    /**
     * 查询超时时间（秒）
     */
    private int queryTimeoutSeconds = 300;
    
    /**
     * 批处理超时时间（秒）
     */
    private int batchTimeoutSeconds = 600;
    
    /**
     * 进度报告间隔（毫秒）
     */
    private long progressReportInterval = 10000;
    
    /**
     * 内存监控阈值（MB）
     */
    private long memoryThresholdMB = 1024;
    
    /**
     * 是否启用内存监控
     */
    private boolean enableMemoryMonitoring = true;
    
    /**
     * 是否启用性能统计
     */
    private boolean enablePerformanceStats = true;
    
    /**
     * 错误重试次数
     */
    private int maxRetryAttempts = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    private long retryIntervalMs = 5000;
    
    // Getters and Setters
    
    public long getLargeDataThreshold() {
        return largeDataThreshold;
    }
    
    public void setLargeDataThreshold(long largeDataThreshold) {
        this.largeDataThreshold = largeDataThreshold;
    }
    
    public int getSmallBatchSize() {
        return smallBatchSize;
    }
    
    public void setSmallBatchSize(int smallBatchSize) {
        this.smallBatchSize = smallBatchSize;
    }
    
    public int getMediumBatchSize() {
        return mediumBatchSize;
    }
    
    public void setMediumBatchSize(int mediumBatchSize) {
        this.mediumBatchSize = mediumBatchSize;
    }
    
    public int getLargeBatchSize() {
        return largeBatchSize;
    }
    
    public void setLargeBatchSize(int largeBatchSize) {
        this.largeBatchSize = largeBatchSize;
    }
    
    public int getXlargeBatchSize() {
        return xlargeBatchSize;
    }
    
    public void setXlargeBatchSize(int xlargeBatchSize) {
        this.xlargeBatchSize = xlargeBatchSize;
    }
    
    public int getMinThreads() {
        return minThreads;
    }
    
    public void setMinThreads(int minThreads) {
        this.minThreads = minThreads;
    }
    
    public int getMaxThreads() {
        return maxThreads;
    }
    
    public void setMaxThreads(int maxThreads) {
        this.maxThreads = maxThreads;
    }
    
    public int getConnectionPoolSize() {
        return connectionPoolSize;
    }
    
    public void setConnectionPoolSize(int connectionPoolSize) {
        this.connectionPoolSize = connectionPoolSize;
    }
    
    public int getQueryTimeoutSeconds() {
        return queryTimeoutSeconds;
    }
    
    public void setQueryTimeoutSeconds(int queryTimeoutSeconds) {
        this.queryTimeoutSeconds = queryTimeoutSeconds;
    }
    
    public int getBatchTimeoutSeconds() {
        return batchTimeoutSeconds;
    }
    
    public void setBatchTimeoutSeconds(int batchTimeoutSeconds) {
        this.batchTimeoutSeconds = batchTimeoutSeconds;
    }
    
    public long getProgressReportInterval() {
        return progressReportInterval;
    }
    
    public void setProgressReportInterval(long progressReportInterval) {
        this.progressReportInterval = progressReportInterval;
    }
    
    public long getMemoryThresholdMB() {
        return memoryThresholdMB;
    }
    
    public void setMemoryThresholdMB(long memoryThresholdMB) {
        this.memoryThresholdMB = memoryThresholdMB;
    }
    
    public boolean isEnableMemoryMonitoring() {
        return enableMemoryMonitoring;
    }
    
    public void setEnableMemoryMonitoring(boolean enableMemoryMonitoring) {
        this.enableMemoryMonitoring = enableMemoryMonitoring;
    }
    
    public boolean isEnablePerformanceStats() {
        return enablePerformanceStats;
    }
    
    public void setEnablePerformanceStats(boolean enablePerformanceStats) {
        this.enablePerformanceStats = enablePerformanceStats;
    }
    
    public int getMaxRetryAttempts() {
        return maxRetryAttempts;
    }
    
    public void setMaxRetryAttempts(int maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }
    
    public long getRetryIntervalMs() {
        return retryIntervalMs;
    }
    
    public void setRetryIntervalMs(long retryIntervalMs) {
        this.retryIntervalMs = retryIntervalMs;
    }
    
    /**
     * 根据数据量获取推荐的批次大小
     */
    public int getRecommendedBatchSize(long totalRows) {
        if (totalRows < 100000) {
            return smallBatchSize;
        } else if (totalRows < 1000000) {
            return mediumBatchSize;
        } else if (totalRows < 5000000) {
            return largeBatchSize;
        } else {
            return xlargeBatchSize;
        }
    }
    
    /**
     * 根据数据量获取推荐的线程数
     */
    public int getRecommendedThreadCount(long totalRows) {
        if (totalRows < 100000) {
            return 1;
        } else if (totalRows < 1000000) {
            return 2;
        } else if (totalRows < 5000000) {
            return 4;
        } else {
            return Math.min(6, maxThreads);
        }
    }
}
