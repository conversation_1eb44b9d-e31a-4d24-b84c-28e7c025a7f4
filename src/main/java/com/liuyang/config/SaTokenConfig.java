package com.liuyang.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
                .addPathPatterns("/api/**")  // 只拦截API路径
                .excludePathPatterns(
                    // 认证相关接口
                    "/api/auth/login",
                    "/api/auth/register",
                    "/api/auth/logout",
                    "/api/satoken/login",
                    "/api/satoken/register",
                    "/api/satoken/logout",
                    "/api/satoken/current-user",
                    "/api/satoken/token-info",
                    
                    // 静态资源
                    "/static/**",
                    "/js/**",
                    "/libs/**",
                    "/favicon.ico",
                    
                    // 页面
                    "/",
                    "/index.html",
                    "/login.html",
                    "/register.html",
                    "/register-working.html",
                    
                    // WebSocket
                    "/ws/**",
                    
                    // 监控端点
                    "/actuator/**",
                    "/error"
                );
    }

    /**
     * Sa-Token整合JWT (Simple模式)
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }
}
