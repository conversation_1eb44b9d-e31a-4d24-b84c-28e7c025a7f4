package com.liuyang.config;

import com.liuyang.util.SnowflakeIdGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 雪花算法ID生成器配置
 */
@Configuration
public class IdGeneratorConfig {

    /**
     * 配置雪花算法ID生成器
     */
    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        // 设置机器ID和数据中心ID（在集群环境中每个实例应该有不同的ID）
        long machineId = 1L;
        long datacenterId = 1L;
        
        return new SnowflakeIdGenerator(machineId, datacenterId);
    }
}
