package com.liuyang.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * AI配置检查器 - 启动时验证AI服务配置
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "app.ai.field-naming.enabled", havingValue = "true", matchIfMissing = false)
public class AiConfigurationChecker implements CommandLineRunner {

    private static final String[] INVALID_KEYS = {"your-dashscope-api-key"};

    @Autowired(required = false)
    @Qualifier("fieldNamingChatClient")
    private ChatClient chatClient;

    @Value("${spring.ai.dashscope.api-key:}")
    private String apiKey;

    @Override
    public void run(String... args) {
        boolean apiKeyValid = isValidApiKey();
        boolean chatClientReady = isChatClientReady();

        String status = (apiKeyValid && chatClientReady) ? "✅ AI功能正常" : "⚠️ AI功能降级";
        log.info("🤖 AI配置检查: API密钥[{}] ChatClient[{}] - {}",
                apiKeyValid ? "✅" : "❌",
                chatClientReady ? "✅" : "❌",
                status);

        if (!apiKeyValid || !chatClientReady) {
            log.warn("💡 请设置正确的API密钥: export AI_DASHSCOPE_API_KEY=your-api-key");
        }
    }

    /**
     * 检查API密钥是否有效
     */
    private boolean isValidApiKey() {
        if (!StringUtils.hasText(apiKey)) {
            return false;
        }

        for (String invalidKey : INVALID_KEYS) {
            if (apiKey.contains(invalidKey)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查ChatClient是否就绪
     */
    private boolean isChatClientReady() {
        if (chatClient == null) {
            return false;
        }

        try {
            chatClient.prompt("test").call().content();
            return true;
        } catch (Exception e) {
            log.debug("ChatClient测试失败: {}", e.getMessage());
            return false;
        }
    }
}
