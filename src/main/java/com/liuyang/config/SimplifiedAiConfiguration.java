package com.liuyang.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 简化的AI配置类
 * 依赖Spring AI Alibaba的自动配置
 */
@Configuration
@ConditionalOnProperty(name = "app.ai.field-naming.enabled", havingValue = "true", matchIfMissing = false)
public class SimplifiedAiConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(SimplifiedAiConfiguration.class);

    @Autowired(required = false)
    private ChatModel chatModel;

    /**
     * 配置字段命名专用ChatClient
     * 依赖Spring AI Alibaba自动配置的ChatModel
     */
    @Bean("fieldNamingChatClient")
    public ChatClient fieldNamingChatClient() {
        logger.info("🔧 开始配置字段命名专用ChatClient...");

        // 检查ChatModel是否可用
        if (chatModel == null) {
            logger.error("❌ ChatModel未找到！");
            logger.error("可能的原因：");
            logger.error("1. Spring AI Alibaba自动配置失败");
            logger.error("2. API密钥未正确配置或无效");
            logger.error("3. 网络连接问题");
            logger.error("4. 依赖版本不兼容");
            logger.error("请检查：");
            logger.error("- 环境变量 AI_DASHSCOPE_API_KEY 是否正确设置");
            logger.error("- 网络是否能访问 dashscope.aliyuncs.com");
            logger.error("- spring-ai-alibaba-starter-dashscope 依赖是否正确");
            return null;
        }

        try {
            logger.info("✅ 找到ChatModel: {}", chatModel.getClass().getSimpleName());
            logger.info("🔧 创建ChatClient...");

            ChatClient chatClient = ChatClient.builder(chatModel)
                    .defaultSystem(getFieldNamingSystemPrompt())
                    .build();

            logger.info("✅ 字段命名ChatClient创建成功！");
            return chatClient;

        } catch (Exception e) {
            logger.error("❌ 字段命名ChatClient创建失败", e);
            logger.error("错误详情: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 配置表名优化专用ChatClient
     */
    @Bean("tableNamingChatClient")
    public ChatClient tableNamingChatClient() {
        logger.info("🔧 开始配置表名优化专用ChatClient...");

        if (chatModel == null) {
            logger.error("❌ ChatModel未找到，表名优化ChatClient创建失败");
            return null;
        }

        try {
            ChatClient chatClient = ChatClient.builder(chatModel)
                    .defaultSystem(getTableNamingSystemPrompt())
                    .build();

            logger.info("✅ 表名优化ChatClient创建成功！");
            return chatClient;

        } catch (Exception e) {
            logger.error("❌ 表名优化ChatClient创建失败", e);
            return null;
        }
    }

    /**
     * 字段命名系统提示词
     */
    private String getFieldNamingSystemPrompt() {
        return "你是一位专业的数据库设计专家，专门负责将中文或其他语言的字段名转换为标准的英文snake_case格式。\n\n" +

               "核心要求：\n" +
               "1. 必须转换为有意义的英文单词\n" +
               "2. 必须使用snake_case格式（单词间用下划线分隔，全部小写）\n" +
               "3. 字段名要准确反映业务含义\n" +
               "4. 避免使用拼音，必须使用标准英文单词\n" +
               "5. 保持简洁但要表意清晰\n\n" +

               "特殊规则：\n" +
               "- 源数据库表的主键'id'字段必须优化为'business_id'（业务主键）\n" +
               "- 这是系统的标准规范，所有主键id都要转换为business_id\n\n" +

               "转换规则：\n" +
               "- 分析字段的业务含义\n" +
               "- 选择最准确的英文单词表达\n" +
               "- 使用下划线连接多个单词\n" +
               "- 优先使用常见的数据库字段命名约定\n\n" +

               "示例转换：\n" +
               "- id（主键） → business_id\n" +
               "- 用户名 → user_name\n" +
               "- 实际免费数量 → actual_free_count\n" +
               "- 交易前免费数量 → free_count_before_trade\n" +
               "- 商品价格 → product_price\n" +
               "- 订单状态 → order_status\n" +
               "- 创建时间 → create_time\n" +
               "- 是否有效 → is_valid\n" +
               "- 总金额 → total_amount\n\n" +
               
               "请根据字段的实际业务含义，给出最合适的英文snake_case字段名。只返回字段名，不要包含其他说明。";
    }

    /**
     * 表名优化系统提示词
     */
    private String getTableNamingSystemPrompt() {
        return "你是一位专业的数据库设计专家，专门负责将中文表名转换为标准的英文snake_case格式。\n\n" +

               "核心要求：\n" +
               "1. 必须以 'hc_' 开头（hc代表系统前缀）\n" +
               "2. 必须转换为有意义的英文单词\n" +
               "3. 使用snake_case格式（单词间用下划线分隔，全部小写）\n" +
               "4. 表名要准确反映表的业务用途\n" +
               "5. 避免使用拼音，必须使用标准英文单词\n" +
               "6. 通常使用复数形式（如users, orders, products）\n" +
               "7. 保持简洁但要表意清晰\n\n" +

               "转换规则：\n" +
               "- 分析表的业务用途\n" +
               "- 选择最准确的英文单词表达\n" +
               "- 在hc_前缀后使用下划线连接多个单词\n" +
               "- 遵循数据库表命名最佳实践\n\n" +

               "示例转换：\n" +
               "- 用户表 → hc_users\n" +
               "- 订单信息 → hc_orders\n" +
               "- 商品详情 → hc_product_details\n" +
               "- 系统配置 → hc_system_config\n" +
               "- 用户权限 → hc_user_permissions\n" +
               "- 操作日志 → hc_operation_logs\n" +
               "- 车主信息 → hc_car_owners\n" +
               "- 认证记录 → hc_auth_records\n\n" +

               "请根据表的实际业务用途，给出最合适的以hc_开头的英文snake_case表名。只返回表名，不要包含其他说明。";
    }
}
