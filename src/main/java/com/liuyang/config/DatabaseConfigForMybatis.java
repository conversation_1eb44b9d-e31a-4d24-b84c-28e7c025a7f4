package com.liuyang.config;

import com.liuyang.dto.DatabaseType;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据库配置类
 * 配置数据源
 */
@Configuration
public class DatabaseConfigForMybatis {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfigForMybatis.class);
    
    // 配置管理数据库连接配置
    @Value("${app.config.database.host}")
    private String configDbHost;

    @Value("${app.config.database.port}")
    private Integer configDbPort;

    @Value("${app.config.database.username}")
    private String configDbUsername;

    @Value("${app.config.database.password}")
    private String configDbPassword;
    
    @Value("${app.copy.batch-size:1000}")
    private Integer batchSize;
    
    /**
     * 配置数据库配置管理专用的数据源
     */
    @Bean("configDataSource")
    @Primary
    public DataSource configDataSource() {
        logger.info("🔧 配置数据库配置管理专用数据源...");
        
        HikariConfig config = new HikariConfig();
        
        // 数据库连接配置
        String jdbcUrl = String.format(
            "****************************************************************************************************",
            configDbHost, configDbPort
        );

        config.setJdbcUrl(jdbcUrl);
        config.setUsername(configDbUsername);
        config.setPassword(configDbPassword);
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 连接池配置
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(20000);
        config.setIdleTimeout(300000);
        config.setMaxLifetime(1200000);
        config.setLeakDetectionThreshold(60000);
        
        // 连接池名称
        config.setPoolName("ConfigDB-HikariCP");
        
        // 连接测试
        config.setConnectionTestQuery("SELECT 1");
        
        logger.info("✅ 数据库配置管理数据源配置完成: {}", jdbcUrl);
        
        return new HikariDataSource(config);
    }
    
    // Getter methods for compatibility

    public String getConfigDbHost() {
        return configDbHost;
    }

    public Integer getConfigDbPort() {
        return configDbPort;
    }

    public String getConfigDbUsername() {
        return configDbUsername;
    }

    public String getConfigDbPassword() {
        return configDbPassword;
    }

    public Integer getBatchSize() {
        return batchSize;
    }
}
