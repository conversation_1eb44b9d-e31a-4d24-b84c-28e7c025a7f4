package com.liuyang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 数据库连接配置DTO
 */
public class DatabaseConnectionConfig {
    
    @NotNull(message = "数据库类型不能为空")
    private DatabaseType databaseType;
    
    @NotBlank(message = "主机地址不能为空")
    private String host;
    
    @NotNull(message = "端口号不能为空")
    private Integer port;
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    private String description; // 连接描述
    private boolean testConnection = true; // 是否测试连接
    
    public DatabaseConnectionConfig() {}
    
    public DatabaseConnectionConfig(DatabaseType databaseType, String host, Integer port, String username, String password) {
        this.databaseType = databaseType;
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }
    
    public DatabaseType getDatabaseType() {
        return databaseType;
    }
    
    public void setDatabaseType(DatabaseType databaseType) {
        this.databaseType = databaseType;
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public Integer getPort() {
        return port;
    }
    
    public void setPort(Integer port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isTestConnection() {
        return testConnection;
    }
    
    public void setTestConnection(boolean testConnection) {
        this.testConnection = testConnection;
    }
    
    /**
     * 生成JDBC URL
     */
    public String getJdbcUrl(String databaseName) {
        switch (databaseType) {
            case POSTGRESQL:
                return String.format("jdbc:postgresql://%s:%d/%s", host, port, databaseName);
            case MYSQL:
                return String.format("**********************************************************************************", 
                    host, port, databaseName);
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + databaseType);
        }
    }
    
    @Override
    public String toString() {
        return "DatabaseConnectionConfig{" +
                "databaseType=" + databaseType +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", username='" + username + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
} 