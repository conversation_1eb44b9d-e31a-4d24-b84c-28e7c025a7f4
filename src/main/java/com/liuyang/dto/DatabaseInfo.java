package com.liuyang.dto;

/**
 * 数据库信息DTO
 */
public class DatabaseInfo {
    private String name;
    private String description;
    private boolean accessible;
    private DatabaseType databaseType;
    private String host;
    private Integer port;

    public DatabaseInfo() {}

    public DatabaseInfo(String name, String description, boolean accessible) {
        this.name = name;
        this.description = description;
        this.accessible = accessible;
        this.databaseType = DatabaseType.POSTGRESQL; // 默认PostgreSQL
    }

    public DatabaseInfo(String name, String description, boolean accessible, DatabaseType databaseType, String host, Integer port) {
        this.name = name;
        this.description = description;
        this.accessible = accessible;
        this.databaseType = databaseType;
        this.host = host;
        this.port = port;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isAccessible() {
        return accessible;
    }

    public void setAccessible(boolean accessible) {
        this.accessible = accessible;
    }

    public DatabaseType getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(DatabaseType databaseType) {
        this.databaseType = databaseType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    /**
     * 获取连接字符串（用于前端显示）
     */
    public String getConnectionString() {
        if (databaseType == null) {
            return name;
        }
        return String.format("%s (%s:%s)", name, 
            databaseType.getDisplayName(), 
            port != null ? port : databaseType.getDefaultPort());
    }

    @Override
    public String toString() {
        return "DatabaseInfo{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", accessible=" + accessible +
                ", databaseType=" + databaseType +
                ", host='" + host + '\'' +
                ", port=" + port +
                '}';
    }
}
