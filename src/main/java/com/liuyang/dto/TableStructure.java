package com.liuyang.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * 表结构信息DTO
 */
public class TableStructure {
    private String tableName;
    private String schema;
    private String tableComment;
    private List<ColumnInfo> columns;
    private List<String> primaryKeys;
    private String createTableSql;

    public TableStructure() {
        this.columns = new ArrayList<>();
        this.primaryKeys = new ArrayList<>();
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public String getTableComment() {
        return tableComment;
    }

    public void setTableComment(String tableComment) {
        this.tableComment = tableComment;
    }

    public List<ColumnInfo> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnInfo> columns) {
        this.columns = columns;
    }

    public List<String> getPrimaryKeys() {
        return primaryKeys;
    }

    public void setPrimaryKeys(List<String> primaryKeys) {
        this.primaryKeys = primaryKeys;
    }

    public String getCreateTableSql() {
        return createTableSql;
    }

    public void setCreateTableSql(String createTableSql) {
        this.createTableSql = createTableSql;
    }

    @Override
    public String toString() {
        return "TableStructure{" +
                "tableName='" + tableName + '\'' +
                ", schema='" + schema + '\'' +
                ", tableComment='" + tableComment + '\'' +
                ", columns=" + columns +
                ", primaryKeys=" + primaryKeys +
                ", createTableSql='" + createTableSql + '\'' +
                '}';
    }
}
