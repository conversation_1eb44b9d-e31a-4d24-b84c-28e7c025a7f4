package com.liuyang.dto;

/**
 * 增强的字段映射信息
 * 包含字段类型标记和优化信息
 */
public class EnhancedFieldMapping extends FieldMapping {
    
    /**
     * 字段类型
     */
    private FieldType fieldType;
    
    /**
     * 优化说明
     */
    private String optimizationNote;
    
    /**
     * 是否为英文字段名
     */
    private boolean isEnglishName;
    
    /**
     * 是否符合snake_case规范
     */
    private boolean isSnakeCase;
    
    /**
     * 原始字段的业务含义（中文描述）
     */
    private String businessMeaning;
    
    /**
     * AI分析的置信度（0-1）
     */
    private double confidence;
    
    /**
     * 字段重要性级别
     */
    private String importanceLevel;
    
    // 构造函数
    public EnhancedFieldMapping() {
        super();
        this.fieldType = FieldType.UNKNOWN;
        this.confidence = 0.0;
        this.isEnglishName = false;
        this.isSnakeCase = false;
    }
    
    public EnhancedFieldMapping(FieldMapping baseMapping) {
        super();
        this.setSourceField(baseMapping.getSourceField());
        this.setTargetField(baseMapping.getTargetField());
        this.setSourceType(baseMapping.getSourceType());
        this.setTargetType(baseMapping.getTargetType());
        this.setSourceSize(baseMapping.getSourceSize());
        this.setTargetSize(baseMapping.getTargetSize());
        this.setNullable(baseMapping.isNullable());
        this.setComment(baseMapping.getComment());
        
        this.fieldType = FieldType.UNKNOWN;
        this.confidence = 0.0;
        this.isEnglishName = false;
        this.isSnakeCase = false;
    }
    
    // Getter and Setter methods
    public FieldType getFieldType() {
        return fieldType;
    }
    
    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }
    
    public String getOptimizationNote() {
        return optimizationNote;
    }
    
    public void setOptimizationNote(String optimizationNote) {
        this.optimizationNote = optimizationNote;
    }
    
    public boolean isEnglishName() {
        return isEnglishName;
    }
    
    public void setEnglishName(boolean englishName) {
        isEnglishName = englishName;
    }
    
    public boolean isSnakeCase() {
        return isSnakeCase;
    }
    
    public void setSnakeCase(boolean snakeCase) {
        isSnakeCase = snakeCase;
    }
    
    public String getBusinessMeaning() {
        return businessMeaning;
    }
    
    public void setBusinessMeaning(String businessMeaning) {
        this.businessMeaning = businessMeaning;
    }
    
    public double getConfidence() {
        return confidence;
    }
    
    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }
    
    public String getImportanceLevel() {
        return importanceLevel;
    }
    
    public void setImportanceLevel(String importanceLevel) {
        this.importanceLevel = importanceLevel;
    }
    
    /**
     * 获取字段类型描述
     */
    public String getFieldTypeDescription() {
        return fieldType != null ? fieldType.getDescription() : "未知";
    }
    
    /**
     * 检查字段是否需要AI优化
     */
    public boolean needsAiOptimization() {
        return fieldType == FieldType.UNKNOWN || 
               (!isEnglishName || !isSnakeCase);
    }
    
    /**
     * 获取优化建议
     */
    public String getOptimizationSuggestion() {
        if (fieldType == FieldType.AI_OPTIMIZED) {
            return "AI分析优化为英文snake_case格式";
        } else if (!isEnglishName) {
            return "建议转换为英文字段名";
        } else if (!isSnakeCase) {
            return "建议转换为snake_case格式";
        } else {
            return "字段名符合规范";
        }
    }
    
    @Override
    public String toString() {
        return "EnhancedFieldMapping{" +
                "sourceField='" + getSourceField() + '\'' +
                ", targetField='" + getTargetField() + '\'' +
                ", fieldType=" + fieldType +
                ", isEnglishName=" + isEnglishName +
                ", isSnakeCase=" + isSnakeCase +
                ", confidence=" + confidence +
                ", optimizationNote='" + optimizationNote + '\'' +
                '}';
    }
}
