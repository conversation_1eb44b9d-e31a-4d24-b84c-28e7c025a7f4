package com.liuyang.dto;

/**
 * 字段映射配置DTO
 */
public class FieldMapping {
    private String sourceField;      // 源字段名
    private String targetField;      // 目标字段名
    private String sourceType;       // 源字段类型
    private String targetType;       // 目标字段类型
    private Integer sourceSize;      // 源字段长度
    private Integer targetSize;      // 目标字段长度
    private String defaultValue;     // 默认值
    private boolean nullable;        // 是否允许为空
    private String comment;          // 字段注释

    public FieldMapping() {}

    public FieldMapping(String sourceField, String targetField, String sourceType, String targetType) {
        this.sourceField = sourceField;
        this.targetField = targetField;
        this.sourceType = sourceType;
        this.targetType = targetType;
    }

    public String getSourceField() {
        return sourceField;
    }

    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }

    public String getTargetField() {
        return targetField;
    }

    public void setTargetField(String targetField) {
        this.targetField = targetField;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public Integer getSourceSize() {
        return sourceSize;
    }

    public void setSourceSize(Integer sourceSize) {
        this.sourceSize = sourceSize;
    }

    public Integer getTargetSize() {
        return targetSize;
    }

    public void setTargetSize(Integer targetSize) {
        this.targetSize = targetSize;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public boolean isNullable() {
        return nullable;
    }

    public void setNullable(boolean nullable) {
        this.nullable = nullable;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public String toString() {
        return "FieldMapping{" +
                "sourceField='" + sourceField + '\'' +
                ", targetField='" + targetField + '\'' +
                ", sourceType='" + sourceType + '\'' +
                ", targetType='" + targetType + '\'' +
                ", sourceSize=" + sourceSize +
                ", targetSize=" + targetSize +
                ", defaultValue='" + defaultValue + '\'' +
                ", nullable=" + nullable +
                ", comment='" + comment + '\'' +
                '}';
    }
}
