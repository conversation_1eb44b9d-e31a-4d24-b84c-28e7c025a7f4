package com.liuyang.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * 复制表请求DTO
 */
public class CopyTableRequest {
    @NotBlank(message = "源数据库名称不能为空")
    private String sourceDatabase;
    
    @NotBlank(message = "目标数据库名称不能为空")
    private String targetDatabase;
    
    @NotBlank(message = "表名不能为空")
    private String tableName;
    
    private boolean copyData = true;
    private boolean dropIfExists = false;
    private String targetTableName; // 目标表名，如果为空则使用源表名

    // 数据库配置ID
    private Long sourceConfigId; // 源数据库配置ID
    private Long targetConfigId; // 目标数据库配置ID

    // 数据库类型
    private DatabaseType sourceDbType = DatabaseType.POSTGRESQL;
    private DatabaseType targetDbType = DatabaseType.MYSQL;
    
    // 双主键设计
    private boolean useDualPrimaryKey = true; // 是否使用双主键设计
    private String businessKeyFieldName = "business_id"; // 业务主键字段名
    private String newPrimaryKeyFieldName = "id"; // 新主键字段名
    
    // 预生成的字段映射信息（避免重复调用AI）
    private java.util.List<EnhancedFieldMapping> fieldMappings;

    // 业务上下文描述（用于AI优化）
    private String businessContext;

    public CopyTableRequest() {}

    public String getSourceDatabase() {
        return sourceDatabase;
    }

    public void setSourceDatabase(String sourceDatabase) {
        this.sourceDatabase = sourceDatabase;
    }

    public String getTargetDatabase() {
        return targetDatabase;
    }

    public void setTargetDatabase(String targetDatabase) {
        this.targetDatabase = targetDatabase;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public boolean isCopyData() {
        return copyData;
    }

    public void setCopyData(boolean copyData) {
        this.copyData = copyData;
    }

    public boolean isDropIfExists() {
        return dropIfExists;
    }

    public void setDropIfExists(boolean dropIfExists) {
        this.dropIfExists = dropIfExists;
    }

    public String getTargetTableName() {
        return targetTableName != null && !targetTableName.trim().isEmpty() 
               ? targetTableName : tableName;
    }

    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }

    public Long getSourceConfigId() {
        return sourceConfigId;
    }

    public void setSourceConfigId(Long sourceConfigId) {
        this.sourceConfigId = sourceConfigId;
    }

    public Long getTargetConfigId() {
        return targetConfigId;
    }

    public void setTargetConfigId(Long targetConfigId) {
        this.targetConfigId = targetConfigId;
    }

    public DatabaseType getSourceDbType() {
        return sourceDbType;
    }

    public void setSourceDbType(DatabaseType sourceDbType) {
        this.sourceDbType = sourceDbType;
    }

    public DatabaseType getTargetDbType() {
        return targetDbType;
    }

    public void setTargetDbType(DatabaseType targetDbType) {
        this.targetDbType = targetDbType;
    }

    public boolean isUseDualPrimaryKey() {
        return useDualPrimaryKey;
    }

    public void setUseDualPrimaryKey(boolean useDualPrimaryKey) {
        this.useDualPrimaryKey = useDualPrimaryKey;
    }

    public String getBusinessKeyFieldName() {
        return businessKeyFieldName;
    }

    public void setBusinessKeyFieldName(String businessKeyFieldName) {
        this.businessKeyFieldName = businessKeyFieldName;
    }

    public String getNewPrimaryKeyFieldName() {
        return newPrimaryKeyFieldName;
    }

    public void setNewPrimaryKeyFieldName(String newPrimaryKeyFieldName) {
        this.newPrimaryKeyFieldName = newPrimaryKeyFieldName;
    }

    public java.util.List<EnhancedFieldMapping> getFieldMappings() {
        return fieldMappings;
    }

    public void setFieldMappings(java.util.List<EnhancedFieldMapping> fieldMappings) {
        this.fieldMappings = fieldMappings;
    }

    public String getBusinessContext() {
        return businessContext;
    }

    public void setBusinessContext(String businessContext) {
        this.businessContext = businessContext;
    }

    /**
     * 是否为跨数据库迁移
     */
    public boolean isCrossDatabaseMigration() {
        return sourceDbType != targetDbType;
    }

    @Override
    public String toString() {
        return "CopyTableRequest{" +
                "sourceDatabase='" + sourceDatabase + '\'' +
                ", targetDatabase='" + targetDatabase + '\'' +
                ", tableName='" + tableName + '\'' +
                ", copyData=" + copyData +
                ", dropIfExists=" + dropIfExists +
                ", targetTableName='" + targetTableName + '\'' +
                ", sourceConfigId=" + sourceConfigId +
                ", targetConfigId=" + targetConfigId +
                ", sourceDbType=" + sourceDbType +
                ", targetDbType=" + targetDbType +
                ", useDualPrimaryKey=" + useDualPrimaryKey +
                ", businessKeyFieldName='" + businessKeyFieldName + '\'' +
                ", newPrimaryKeyFieldName='" + newPrimaryKeyFieldName + '\'' +
                ", fieldMappings=" + (fieldMappings != null ? fieldMappings.size() + " mappings" : "null") +
                ", businessContext='" + businessContext + '\'' +
                '}';
    }
}
