package com.liuyang.dto;

/**
 * 数据库配置统计信息DTO
 */
public class DatabaseConfigStatistics {
    private String environment;
    private Integer totalCount;
    private Integer activeCount;
    private Integer sourceCount;
    private Integer targetCount;

    // 构造函数
    public DatabaseConfigStatistics() {}

    public DatabaseConfigStatistics(String environment, Integer totalCount, Integer activeCount, 
                                  Integer sourceCount, Integer targetCount) {
        this.environment = environment;
        this.totalCount = totalCount;
        this.activeCount = activeCount;
        this.sourceCount = sourceCount;
        this.targetCount = targetCount;
    }

    // Getters and Setters
    public String getEnvironment() { 
        return environment; 
    }
    
    public void setEnvironment(String environment) { 
        this.environment = environment; 
    }

    public Integer getTotalCount() { 
        return totalCount; 
    }
    
    public void setTotalCount(Integer totalCount) { 
        this.totalCount = totalCount; 
    }

    public Integer getActiveCount() { 
        return activeCount; 
    }
    
    public void setActiveCount(Integer activeCount) { 
        this.activeCount = activeCount; 
    }

    public Integer getSourceCount() { 
        return sourceCount; 
    }
    
    public void setSourceCount(Integer sourceCount) { 
        this.sourceCount = sourceCount; 
    }

    public Integer getTargetCount() { 
        return targetCount; 
    }
    
    public void setTargetCount(Integer targetCount) { 
        this.targetCount = targetCount; 
    }

    @Override
    public String toString() {
        return "DatabaseConfigStatistics{" +
                "environment='" + environment + '\'' +
                ", totalCount=" + totalCount +
                ", activeCount=" + activeCount +
                ", sourceCount=" + sourceCount +
                ", targetCount=" + targetCount +
                '}';
    }
} 