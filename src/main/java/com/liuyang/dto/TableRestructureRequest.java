package com.liuyang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 表重构请求DTO
 */
public class TableRestructureRequest {
    @NotBlank(message = "源数据库名称不能为空")
    private String sourceDatabase;
    
    @NotBlank(message = "目标数据库名称不能为空")
    private String targetDatabase;
    
    @NotBlank(message = "源表名不能为空")
    private String sourceTableName;
    
    @NotBlank(message = "目标表名不能为空")
    private String targetTableName;
    
    @NotEmpty(message = "字段映射不能为空")
    private List<FieldMapping> fieldMappings;
    
    private boolean copyData = true;                    // 是否复制数据
    private boolean dropIfExists = false;               // 如果目标表存在是否删除
    private boolean createIndexes = true;               // 是否创建索引
    private boolean useDualPrimaryKey = false;          // 是否使用双主键设计
    private String businessIdField = "business_id";     // 业务ID字段名
    private String newPrimaryKeyField = "id";           // 新主键字段名
    private List<String> indexFields;                   // 需要创建索引的字段

    public TableRestructureRequest() {}

    public String getSourceDatabase() {
        return sourceDatabase;
    }

    public void setSourceDatabase(String sourceDatabase) {
        this.sourceDatabase = sourceDatabase;
    }

    public String getTargetDatabase() {
        return targetDatabase;
    }

    public void setTargetDatabase(String targetDatabase) {
        this.targetDatabase = targetDatabase;
    }

    public String getSourceTableName() {
        return sourceTableName;
    }

    public void setSourceTableName(String sourceTableName) {
        this.sourceTableName = sourceTableName;
    }

    public String getTargetTableName() {
        return targetTableName;
    }

    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }

    public List<FieldMapping> getFieldMappings() {
        return fieldMappings;
    }

    public void setFieldMappings(List<FieldMapping> fieldMappings) {
        this.fieldMappings = fieldMappings;
    }

    public boolean isCopyData() {
        return copyData;
    }

    public void setCopyData(boolean copyData) {
        this.copyData = copyData;
    }

    public boolean isDropIfExists() {
        return dropIfExists;
    }

    public void setDropIfExists(boolean dropIfExists) {
        this.dropIfExists = dropIfExists;
    }

    public boolean isCreateIndexes() {
        return createIndexes;
    }

    public void setCreateIndexes(boolean createIndexes) {
        this.createIndexes = createIndexes;
    }

    public boolean isUseDualPrimaryKey() {
        return useDualPrimaryKey;
    }

    public void setUseDualPrimaryKey(boolean useDualPrimaryKey) {
        this.useDualPrimaryKey = useDualPrimaryKey;
    }

    public String getBusinessIdField() {
        return businessIdField;
    }

    public void setBusinessIdField(String businessIdField) {
        this.businessIdField = businessIdField;
    }

    public String getNewPrimaryKeyField() {
        return newPrimaryKeyField;
    }

    public void setNewPrimaryKeyField(String newPrimaryKeyField) {
        this.newPrimaryKeyField = newPrimaryKeyField;
    }

    public List<String> getIndexFields() {
        return indexFields;
    }

    public void setIndexFields(List<String> indexFields) {
        this.indexFields = indexFields;
    }

    @Override
    public String toString() {
        return "TableRestructureRequest{" +
                "sourceDatabase='" + sourceDatabase + '\'' +
                ", targetDatabase='" + targetDatabase + '\'' +
                ", sourceTableName='" + sourceTableName + '\'' +
                ", targetTableName='" + targetTableName + '\'' +
                ", fieldMappings=" + fieldMappings +
                ", copyData=" + copyData +
                ", dropIfExists=" + dropIfExists +
                ", createIndexes=" + createIndexes +
                ", useDualPrimaryKey=" + useDualPrimaryKey +
                ", businessIdField='" + businessIdField + '\'' +
                ", newPrimaryKeyField='" + newPrimaryKeyField + '\'' +
                ", indexFields=" + indexFields +
                '}';
    }
}
