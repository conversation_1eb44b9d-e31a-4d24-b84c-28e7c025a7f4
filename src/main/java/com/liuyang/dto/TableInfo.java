package com.liuyang.dto;

/**
 * 表信息DTO
 */
public class TableInfo {
    private String tableName;
    private String tableComment;
    private Long rowCount;
    private String tableType;
    private String schema;

    public TableInfo() {}

    public TableInfo(String tableName, String tableComment, Long rowCount, String tableType, String schema) {
        this.tableName = tableName;
        this.tableComment = tableComment;
        this.rowCount = rowCount;
        this.tableType = tableType;
        this.schema = schema;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTableComment() {
        return tableComment;
    }

    public void setTableComment(String tableComment) {
        this.tableComment = tableComment;
    }

    public Long getRowCount() {
        return rowCount;
    }

    public void setRowCount(Long rowCount) {
        this.rowCount = rowCount;
    }

    public String getTableType() {
        return tableType;
    }

    public void setTableType(String tableType) {
        this.tableType = tableType;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    @Override
    public String toString() {
        return "TableInfo{" +
                "tableName='" + tableName + '\'' +
                ", tableComment='" + tableComment + '\'' +
                ", rowCount=" + rowCount +
                ", tableType='" + tableType + '\'' +
                ", schema='" + schema + '\'' +
                '}';
    }
}
