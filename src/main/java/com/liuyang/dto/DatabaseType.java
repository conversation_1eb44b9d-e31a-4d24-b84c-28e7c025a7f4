package com.liuyang.dto;

/**
 * 数据库类型枚举
 */
public enum DatabaseType {
    POSTGRESQL("PostgreSQL", "jdbc:postgresql", 5432),
    MYSQL("MySQL", "jdbc:mysql", 3306);
    
    private final String displayName;
    private final String jdbcPrefix;
    private final int defaultPort;
    
    DatabaseType(String displayName, String jdbcPrefix, int defaultPort) {
        this.displayName = displayName;
        this.jdbcPrefix = jdbcPrefix;
        this.defaultPort = defaultPort;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getJdbcPrefix() {
        return jdbcPrefix;
    }
    
    public int getDefaultPort() {
        return defaultPort;
    }
    
    /**
     * 根据JDBC URL判断数据库类型
     */
    public static DatabaseType fromJdbcUrl(String url) {
        if (url.startsWith("jdbc:postgresql")) {
            return POSTGRESQL;
        } else if (url.startsWith("jdbc:mysql")) {
            return MYSQL;
        }
        return null;
    }
} 