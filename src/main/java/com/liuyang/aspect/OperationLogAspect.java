package com.liuyang.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.liuyang.annotation.OperationLog;
import com.liuyang.entity.User;
import com.liuyang.service.OperationLogService;
import com.liuyang.service.UserService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 */
@Aspect
@Component
public class OperationLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(OperationLogAspect.class);

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 定义切点
     */
    @Pointcut("@annotation(com.liuyang.annotation.OperationLog)")
    public void operationLogPointcut() {}

    /**
     * 环绕通知
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog annotation = method.getAnnotation(OperationLog.class);
        
        // 获取请求信息
        HttpServletRequest request = getHttpServletRequest();
        
        // 创建日志对象
        com.liuyang.entity.OperationLog operationLog = new com.liuyang.entity.OperationLog();
        
        try {
            // 设置基本信息
            setupBasicInfo(operationLog, annotation, joinPoint, request);
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 设置成功信息
            long executionTime = System.currentTimeMillis() - startTime;
            operationLog.setExecutionTime(executionTime);
            operationLog.setResponseStatus(200);
            
            // 记录响应结果（如果需要）
            if (annotation.recordResponse() && result != null) {
                try {
                    String responseJson = objectMapper.writeValueAsString(result);
                    // 限制响应内容长度
                    if (responseJson.length() > 1000) {
                        responseJson = responseJson.substring(0, 1000) + "...";
                    }
                    operationLog.setRequestParams(operationLog.getRequestParams() + 
                        "\n[RESPONSE]: " + responseJson);
                } catch (Exception e) {
                    logger.warn("记录响应结果失败", e);
                }
            }
            
            // 保存日志
            saveLog(operationLog, annotation.async());
            
            return result;
            
        } catch (Throwable throwable) {
            // 设置错误信息
            long executionTime = System.currentTimeMillis() - startTime;
            operationLog.setExecutionTime(executionTime);
            operationLog.setResponseStatus(500);
            operationLog.setErrorMessage(throwable.getMessage());
            
            // 保存错误日志
            saveLog(operationLog, annotation.async());
            
            throw throwable;
        }
    }

    /**
     * 设置基本信息
     */
    private void setupBasicInfo(com.liuyang.entity.OperationLog operationLog, 
                               OperationLog annotation, 
                               ProceedingJoinPoint joinPoint, 
                               HttpServletRequest request) {
        
        // 设置操作信息
        operationLog.setOperationType(annotation.operationType());
        operationLog.setOperationDesc(parseSpelExpression(annotation.operationDesc(), joinPoint));
        operationLog.setTargetType(annotation.targetType());
        operationLog.setTargetId(parseSpelExpression(annotation.targetId(), joinPoint));

        // 设置用户信息（在targetId设置之后，以便可以从targetId中提取用户信息）
        setupUserInfo(operationLog);
        
        // 设置请求信息
        if (request != null) {
            operationLog.setRequestMethod(request.getMethod());
            operationLog.setRequestUrl(request.getRequestURI());
            operationLog.setClientIp(getClientIp(request));
            operationLog.setUserAgent(request.getHeader("User-Agent"));
        }
        
        // 设置请求参数
        if (annotation.recordParams()) {
            String params = getRequestParams(joinPoint, annotation.ignoreParams());
            operationLog.setRequestParams(params);
        }
        
        // 设置创建时间
        operationLog.setCreatedTime(LocalDateTime.now());
    }

    /**
     * 设置用户信息
     */
    private void setupUserInfo(com.liuyang.entity.OperationLog operationLog) {
        try {
            // 首先尝试从当前登录状态获取用户信息
            if (StpUtil.isLogin()) {
                Long userId = StpUtil.getLoginIdAsLong();
                User user = userService.getUserById(userId);
                if (user != null) {
                    operationLog.setUserId(userId);
                    operationLog.setUsername(user.getUsername());
                    return;
                }
            }

            // 如果无法从登录状态获取，尝试从targetId中提取用户名（适用于登录操作）
            if (operationLog.getTargetId() != null &&
                ("USER_LOGIN".equals(operationLog.getOperationType()) ||
                 "USER_REGISTER".equals(operationLog.getOperationType()))) {
                String username = operationLog.getTargetId();
                User user = userService.getUserByUsername(username);
                if (user != null) {
                    operationLog.setUserId(user.getId());
                    operationLog.setUsername(user.getUsername());
                }
            }
        } catch (Exception e) {
            logger.warn("获取用户信息失败", e);
        }
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpelExpression(String expression, ProceedingJoinPoint joinPoint) {
        if (expression == null || !expression.contains("#{")) {
            return expression;
        }

        try {
            // 创建评估上下文
            EvaluationContext context = new StandardEvaluationContext();

            // 获取方法参数
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Parameter[] parameters = signature.getMethod().getParameters();
            Object[] args = joinPoint.getArgs();

            // 设置参数到上下文 - 使用多种方式
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                String paramName = parameters[i].getName();
                Object arg = args[i];

                // 设置参数名和索引访问
                context.setVariable(paramName, arg);
                context.setVariable("arg" + i, arg);

                // 特殊处理：为常见的参数名设置别名
                if ("arg0".equals(paramName) || i == 0) {
                    context.setVariable("request", arg);
                }
                if ("arg1".equals(paramName) || i == 1) {
                    context.setVariable("param1", arg);
                }

                // 如果是Map类型，展开Map的内容
                if (arg instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) arg;
                    for (Map.Entry<?, ?> entry : map.entrySet()) {
                        context.setVariable(paramName + "['" + entry.getKey() + "']", entry.getValue());
                    }
                }
            }

            // 解析表达式
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);
            return value != null ? value.toString() : "";

        } catch (Exception e) {
            logger.warn("解析SpEL表达式失败: {} - {}", expression, e.getMessage());
            logger.debug("表达式: {}, 参数: {}", expression, joinPoint.getArgs());
            // 尝试简单的字符串替换作为fallback
            return trySimpleReplacement(expression, joinPoint);
        }
    }

    /**
     * 简单的字符串替换作为SpEL解析的fallback
     */
    private String trySimpleReplacement(String expression, ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            String result = expression;

            // 处理第一个参数（通常是请求对象）
            if (args.length > 0) {
                Object firstArg = args[0];

                // 如果是Map类型（如登录请求）
                if (firstArg instanceof Map) {
                    Map<?, ?> map = (Map<?, ?>) firstArg;
                    // 替换 #{#loginRequest['username']} 这样的表达式
                    if (result.contains("loginRequest['username']") && map.containsKey("username")) {
                        result = result.replace("#{#loginRequest['username']}", map.get("username").toString());
                    }
                    if (result.contains("registerRequest['username']") && map.containsKey("username")) {
                        result = result.replace("#{#registerRequest['username']}", map.get("username").toString());
                    }
                }

                // 如果是CopyTableRequest类型
                if (firstArg.getClass().getSimpleName().equals("CopyTableRequest")) {
                    try {
                        // 使用反射获取属性值
                        java.lang.reflect.Method getSourceDatabase = firstArg.getClass().getMethod("getSourceDatabase");
                        java.lang.reflect.Method getTargetDatabase = firstArg.getClass().getMethod("getTargetDatabase");
                        java.lang.reflect.Method getTableName = firstArg.getClass().getMethod("getTableName");
                        java.lang.reflect.Method getTargetTableName = firstArg.getClass().getMethod("getTargetTableName");

                        String sourceDb = (String) getSourceDatabase.invoke(firstArg);
                        String targetDb = (String) getTargetDatabase.invoke(firstArg);
                        String tableName = (String) getTableName.invoke(firstArg);
                        String targetTableName = (String) getTargetTableName.invoke(firstArg);

                        // 如果目标表名为空，使用源表名
                        if (targetTableName == null || targetTableName.trim().isEmpty()) {
                            targetTableName = tableName;
                        }

                        // 替换表达式
                        result = result.replace("#{#request.sourceDatabase}", sourceDb != null ? sourceDb : "");
                        result = result.replace("#{#request.targetDatabase}", targetDb != null ? targetDb : "");
                        result = result.replace("#{#request.tableName}", tableName != null ? tableName : "");
                        result = result.replace("#{#request.targetTableName}", targetTableName != null ? targetTableName : "");

                    } catch (Exception e) {
                        logger.debug("反射获取CopyTableRequest属性失败: {}", e.getMessage());
                    }
                }
            }

            return result;

        } catch (Exception e) {
            logger.debug("简单替换失败: {}", e.getMessage());
        }
        return expression;
    }

    /**
     * 获取请求参数
     */
    private String getRequestParams(ProceedingJoinPoint joinPoint, String[] ignoreParams) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Parameter[] parameters = signature.getMethod().getParameters();
            Object[] args = joinPoint.getArgs();
            
            Map<String, Object> params = new HashMap<>();
            for (int i = 0; i < parameters.length && i < args.length; i++) {
                String paramName = parameters[i].getName();
                
                // 检查是否需要忽略此参数
                if (Arrays.asList(ignoreParams).contains(paramName)) {
                    params.put(paramName, "***");
                } else {
                    params.put(paramName, args[i]);
                }
            }
            
            String paramsJson = objectMapper.writeValueAsString(params);
            // 限制参数长度
            if (paramsJson.length() > 2000) {
                paramsJson = paramsJson.substring(0, 2000) + "...";
            }
            return paramsJson;
            
        } catch (Exception e) {
            logger.warn("获取请求参数失败", e);
            return "";
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = null;

        // 尝试从各种代理头获取真实IP
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                break;
            }
        }

        // 如果代理头都没有，使用远程地址
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况（X-Forwarded-For可能包含多个IP，第一个是真实客户端IP）
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        // 将IPv6本地回环地址转换为IPv4格式，便于阅读
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            ip = "127.0.0.1";
        }

        return ip != null ? ip : "unknown";
    }

    /**
     * 保存日志
     */
    private void saveLog(com.liuyang.entity.OperationLog operationLog, boolean async) {
        try {
            if (async) {
                operationLogService.saveLogAsync(operationLog);
            } else {
                operationLogService.saveLog(operationLog);
            }
        } catch (Exception e) {
            logger.error("保存操作日志失败", e);
        }
    }
}
