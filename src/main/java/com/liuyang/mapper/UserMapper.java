package com.liuyang.mapper;

import com.liuyang.entity.User;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User selectByUsername(String username);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE id = #{id}")
    User selectById(Long id);
    
    /**
     * 查询所有用户
     */
    @Select("SELECT * FROM users ORDER BY created_time DESC")
    List<User> selectAll();
    
    /**
     * 查询启用的用户
     */
    @Select("SELECT * FROM users WHERE status = 1 ORDER BY created_time DESC")
    List<User> selectEnabled();
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO users (username, password, real_name, role, status, created_by) " +
            "VALUES (#{username}, #{password}, #{realName}, #{role}, #{status}, #{createdBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET real_name = #{realName}, role = #{role}, status = #{status}, updated_by = #{updatedBy}, " +
            "updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int update(User user);
    
    /**
     * 更新用户密码
     */
    @Update("UPDATE users SET password = #{password}, updated_by = #{updatedBy}, " +
            "updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password, @Param("updatedBy") String updatedBy);
    
    /**
     * 更新最后登录信息
     */
    @Update("UPDATE users SET last_login_time = #{lastLoginTime}, last_login_ip = #{lastLoginIp}, " +
            "updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateLastLogin(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime, 
                       @Param("lastLoginIp") String lastLoginIp);
    
    /**
     * 更新用户状态
     */
    @Update("UPDATE users SET status = #{status}, updated_by = #{updatedBy}, " +
            "updated_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updatedBy") String updatedBy);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM users WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username}")
    int countByUsername(String username);
    
    /**
     * 检查用户名是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND id != #{excludeId}")
    int countByUsernameExcludeId(@Param("username") String username, @Param("excludeId") Long excludeId);
    
    /**
     * 根据角色查询用户
     */
    @Select("SELECT * FROM users WHERE role = #{role} AND status = 1 ORDER BY created_time DESC")
    List<User> selectByRole(String role);
    
    /**
     * 分页查询用户
     */
    @Select("SELECT * FROM users ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<User> selectWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询用户总数
     */
    @Select("SELECT COUNT(*) FROM users")
    int countTotal();
    
    /**
     * 根据条件搜索用户
     */
    @Select("<script>" +
            "SELECT * FROM users WHERE 1=1 " +
            "<if test='username != null and username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='realName != null and realName != \"\"'>" +
            "AND real_name LIKE CONCAT('%', #{realName}, '%') " +
            "</if>" +
            "<if test='role != null and role != \"\"'>" +
            "AND role = #{role} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "ORDER BY created_time DESC " +
            "</script>")
    List<User> searchUsers(@Param("username") String username,
                          @Param("realName") String realName,
                          @Param("role") String role,
                          @Param("status") Integer status);
}
