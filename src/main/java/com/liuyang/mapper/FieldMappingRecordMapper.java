package com.liuyang.mapper;

import com.liuyang.entity.FieldMappingRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 字段映射记录Mapper接口
 */
@Mapper
public interface FieldMappingRecordMapper {
    
    /**
     * 插入字段映射记录
     */
    @Insert("""
            INSERT INTO field_mapping_records (
                migration_batch_id, migration_record_id, table_mapping_record_id,
                source_table_name, target_table_name, source_field_name, source_field_type,
                source_field_length, source_field_precision, source_field_scale,
                source_field_nullable, source_field_default_value, source_field_comment,
                source_field_is_primary, source_field_is_unique, source_field_is_index,
                target_field_name, target_field_type, target_field_length,
                target_field_precision, target_field_scale, target_field_nullable,
                target_field_default_value, target_field_comment, target_field_is_primary,
                target_field_is_unique, target_field_is_index, field_order_position,
                mapping_rule, conversion_notes
            ) VALUES (
                #{migrationBatchId}, #{migrationRecordId}, #{tableMappingRecordId},
                #{sourceTableName}, #{targetTableName}, #{sourceFieldName}, #{sourceFieldType},
                #{sourceFieldLength}, #{sourceFieldPrecision}, #{sourceFieldScale},
                #{sourceFieldNullable}, #{sourceFieldDefaultValue}, #{sourceFieldComment},
                #{sourceFieldIsPrimary}, #{sourceFieldIsUnique}, #{sourceFieldIsIndex},
                #{targetFieldName}, #{targetFieldType}, #{targetFieldLength},
                #{targetFieldPrecision}, #{targetFieldScale}, #{targetFieldNullable},
                #{targetFieldDefaultValue}, #{targetFieldComment}, #{targetFieldIsPrimary},
                #{targetFieldIsUnique}, #{targetFieldIsIndex}, #{fieldOrderPosition},
                #{mappingRule}, #{conversionNotes}
            )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(FieldMappingRecord fieldMappingRecord);
    
    /**
     * 根据ID更新字段映射记录
     */
    @Update("""
            UPDATE field_mapping_records SET
                source_field_type = #{sourceFieldType},
                source_field_length = #{sourceFieldLength},
                source_field_precision = #{sourceFieldPrecision},
                source_field_scale = #{sourceFieldScale},
                source_field_nullable = #{sourceFieldNullable},
                source_field_default_value = #{sourceFieldDefaultValue},
                source_field_comment = #{sourceFieldComment},
                source_field_is_primary = #{sourceFieldIsPrimary},
                source_field_is_unique = #{sourceFieldIsUnique},
                source_field_is_index = #{sourceFieldIsIndex},
                target_field_name = #{targetFieldName},
                target_field_type = #{targetFieldType},
                target_field_length = #{targetFieldLength},
                target_field_precision = #{targetFieldPrecision},
                target_field_scale = #{targetFieldScale},
                target_field_nullable = #{targetFieldNullable},
                target_field_default_value = #{targetFieldDefaultValue},
                target_field_comment = #{targetFieldComment},
                target_field_is_primary = #{targetFieldIsPrimary},
                target_field_is_unique = #{targetFieldIsUnique},
                target_field_is_index = #{targetFieldIsIndex},
                field_order_position = #{fieldOrderPosition},
                mapping_rule = #{mappingRule},
                conversion_notes = #{conversionNotes}
            WHERE id = #{id}
            """)
    int updateById(FieldMappingRecord fieldMappingRecord);
    
    /**
     * 根据ID查询字段映射记录
     */
    @Select("SELECT * FROM field_mapping_records WHERE id = #{id}")
    FieldMappingRecord selectById(Long id);
    
    /**
     * 根据迁移批次ID查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE migration_batch_id = #{migrationBatchId}
            ORDER BY field_order_position ASC, created_time ASC
            """)
    List<FieldMappingRecord> selectByMigrationBatchId(Long migrationBatchId);
    
    /**
     * 根据迁移记录ID查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE migration_record_id = #{migrationRecordId}
            ORDER BY field_order_position ASC, created_time ASC
            """)
    List<FieldMappingRecord> selectByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 根据表映射记录ID查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE table_mapping_record_id = #{tableMappingRecordId}
            ORDER BY field_order_position ASC, created_time ASC
            """)
    List<FieldMappingRecord> selectByTableMappingRecordId(Long tableMappingRecordId);
    
    /**
     * 根据表名查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE source_table_name = #{sourceTableName} 
               OR target_table_name = #{targetTableName}
            ORDER BY created_time DESC
            """)
    List<FieldMappingRecord> selectByTableName(@Param("sourceTableName") String sourceTableName, 
                                              @Param("targetTableName") String targetTableName);
    
    /**
     * 根据源字段名查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE source_field_name = #{sourceFieldName}
            ORDER BY created_time DESC
            """)
    List<FieldMappingRecord> selectBySourceFieldName(String sourceFieldName);
    
    /**
     * 根据目标字段名查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE target_field_name = #{targetFieldName}
            ORDER BY created_time DESC
            """)
    List<FieldMappingRecord> selectByTargetFieldName(String targetFieldName);
    
    /**
     * 根据映射规则查询字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE mapping_rule = #{mappingRule}
            ORDER BY created_time DESC
            """)
    List<FieldMappingRecord> selectByMappingRule(String mappingRule);
    
    /**
     * 查询主键字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE table_mapping_record_id = #{tableMappingRecordId}
            AND (source_field_is_primary = 1 OR target_field_is_primary = 1)
            ORDER BY field_order_position ASC
            """)
    List<FieldMappingRecord> selectPrimaryKeyFields(Long tableMappingRecordId);
    
    /**
     * 查询唯一索引字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE table_mapping_record_id = #{tableMappingRecordId}
            AND (source_field_is_unique = 1 OR target_field_is_unique = 1)
            ORDER BY field_order_position ASC
            """)
    List<FieldMappingRecord> selectUniqueFields(Long tableMappingRecordId);
    
    /**
     * 查询有索引的字段映射记录
     */
    @Select("""
            SELECT * FROM field_mapping_records 
            WHERE table_mapping_record_id = #{tableMappingRecordId}
            AND (source_field_is_index = 1 OR target_field_is_index = 1)
            ORDER BY field_order_position ASC
            """)
    List<FieldMappingRecord> selectIndexFields(Long tableMappingRecordId);
    
    /**
     * 统计指定表映射记录的字段统计信息
     */
    @Select("""
            SELECT 
                COUNT(*) as total_fields,
                COUNT(CASE WHEN mapping_rule = 'DIRECT' THEN 1 END) as direct_mapping_fields,
                COUNT(CASE WHEN mapping_rule = 'TYPE_CONVERT' THEN 1 END) as type_convert_fields,
                COUNT(CASE WHEN mapping_rule = 'NAME_CONVERT' THEN 1 END) as name_convert_fields,
                COUNT(CASE WHEN mapping_rule = 'CUSTOM' THEN 1 END) as custom_mapping_fields,
                COUNT(CASE WHEN source_field_is_primary = 1 THEN 1 END) as primary_key_fields,
                COUNT(CASE WHEN source_field_is_unique = 1 THEN 1 END) as unique_fields,
                COUNT(CASE WHEN source_field_is_index = 1 THEN 1 END) as index_fields
            FROM field_mapping_records 
            WHERE table_mapping_record_id = #{tableMappingRecordId}
            """)
    @Results({
        @Result(property = "totalFields", column = "total_fields"),
        @Result(property = "directMappingFields", column = "direct_mapping_fields"),
        @Result(property = "typeConvertFields", column = "type_convert_fields"),
        @Result(property = "nameConvertFields", column = "name_convert_fields"),
        @Result(property = "customMappingFields", column = "custom_mapping_fields"),
        @Result(property = "primaryKeyFields", column = "primary_key_fields"),
        @Result(property = "uniqueFields", column = "unique_fields"),
        @Result(property = "indexFields", column = "index_fields")
    })
    FieldMappingStatistics getFieldMappingStatistics(Long tableMappingRecordId);
    
    /**
     * 批量插入字段映射记录
     */
    @Insert({
        "<script>",
        "INSERT INTO field_mapping_records (",
        "migration_batch_id, migration_record_id, table_mapping_record_id,",
        "source_table_name, target_table_name, source_field_name, source_field_type,",
        "source_field_length, source_field_precision, source_field_scale,",
        "source_field_nullable, source_field_default_value, source_field_comment,",
        "source_field_is_primary, source_field_is_unique, source_field_is_index,",
        "target_field_name, target_field_type, target_field_length,",
        "target_field_precision, target_field_scale, target_field_nullable,",
        "target_field_default_value, target_field_comment, target_field_is_primary,",
        "target_field_is_unique, target_field_is_index, field_order_position,",
        "mapping_rule, conversion_notes",
        ") VALUES ",
        "<foreach collection='list' item='item' separator=','>",
        "(#{item.migrationBatchId}, #{item.migrationRecordId}, #{item.tableMappingRecordId},",
        "#{item.sourceTableName}, #{item.targetTableName}, #{item.sourceFieldName}, #{item.sourceFieldType},",
        "#{item.sourceFieldLength}, #{item.sourceFieldPrecision}, #{item.sourceFieldScale},",
        "#{item.sourceFieldNullable}, #{item.sourceFieldDefaultValue}, #{item.sourceFieldComment},",
        "#{item.sourceFieldIsPrimary}, #{item.sourceFieldIsUnique}, #{item.sourceFieldIsIndex},",
        "#{item.targetFieldName}, #{item.targetFieldType}, #{item.targetFieldLength},",
        "#{item.targetFieldPrecision}, #{item.targetFieldScale}, #{item.targetFieldNullable},",
        "#{item.targetFieldDefaultValue}, #{item.targetFieldComment}, #{item.targetFieldIsPrimary},",
        "#{item.targetFieldIsUnique}, #{item.targetFieldIsIndex}, #{item.fieldOrderPosition},",
        "#{item.mappingRule}, #{item.conversionNotes})",
        "</foreach>",
        "</script>"
    })
    int batchInsert(List<FieldMappingRecord> fieldMappingRecords);
    
    /**
     * 删除字段映射记录
     */
    @Delete("DELETE FROM field_mapping_records WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 根据表映射记录ID删除所有字段映射记录
     */
    @Delete("DELETE FROM field_mapping_records WHERE table_mapping_record_id = #{tableMappingRecordId}")
    int deleteByTableMappingRecordId(Long tableMappingRecordId);
    
    /**
     * 根据迁移记录ID删除所有字段映射记录
     */
    @Delete("DELETE FROM field_mapping_records WHERE migration_record_id = #{migrationRecordId}")
    int deleteByMigrationRecordId(Long migrationRecordId);
    
    /**
     * 根据迁移批次ID删除所有字段映射记录
     */
    @Delete("DELETE FROM field_mapping_records WHERE migration_batch_id = #{migrationBatchId}")
    int deleteByMigrationBatchId(Long migrationBatchId);
    
    /**
     * 字段映射统计信息DTO
     */
    class FieldMappingStatistics {
        private Long totalFields;
        private Long directMappingFields;
        private Long typeConvertFields;
        private Long nameConvertFields;
        private Long customMappingFields;
        private Long primaryKeyFields;
        private Long uniqueFields;
        private Long indexFields;
        
        // Getter和Setter方法
        public Long getTotalFields() { return totalFields; }
        public void setTotalFields(Long totalFields) { this.totalFields = totalFields; }
        
        public Long getDirectMappingFields() { return directMappingFields; }
        public void setDirectMappingFields(Long directMappingFields) { this.directMappingFields = directMappingFields; }
        
        public Long getTypeConvertFields() { return typeConvertFields; }
        public void setTypeConvertFields(Long typeConvertFields) { this.typeConvertFields = typeConvertFields; }
        
        public Long getNameConvertFields() { return nameConvertFields; }
        public void setNameConvertFields(Long nameConvertFields) { this.nameConvertFields = nameConvertFields; }
        
        public Long getCustomMappingFields() { return customMappingFields; }
        public void setCustomMappingFields(Long customMappingFields) { this.customMappingFields = customMappingFields; }
        
        public Long getPrimaryKeyFields() { return primaryKeyFields; }
        public void setPrimaryKeyFields(Long primaryKeyFields) { this.primaryKeyFields = primaryKeyFields; }
        
        public Long getUniqueFields() { return uniqueFields; }
        public void setUniqueFields(Long uniqueFields) { this.uniqueFields = uniqueFields; }
        
        public Long getIndexFields() { return indexFields; }
        public void setIndexFields(Long indexFields) { this.indexFields = indexFields; }
    }
} 