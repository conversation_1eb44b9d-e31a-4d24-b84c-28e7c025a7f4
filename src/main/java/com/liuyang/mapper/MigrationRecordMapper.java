package com.liuyang.mapper;

import com.liuyang.entity.MigrationRecord;
import lombok.Data;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 迁移记录主表Mapper接口
 */
@Mapper
public interface MigrationRecordMapper {
    
    /**
     * 插入迁移记录
     */
    @Insert("""
            INSERT INTO migration_records (
                migration_batch_id, operator_id, operator_name, source_config_id, target_config_id,
                source_config_name, target_config_name, source_database_type, target_database_type,
                source_database_name, target_database_name, migration_status,
                total_tables, successful_tables, failed_tables, total_records,
                migration_start_time, migration_end_time, migration_duration_ms,
                error_message, migration_notes, source_table_name, target_table_name, created_by
            ) VALUES (
                #{migrationBatchId}, #{operatorId}, #{operatorName}, #{sourceConfigId}, #{targetConfigId},
                #{sourceConfigName}, #{targetConfigName}, #{sourceDatabaseType}, #{targetDatabaseType},
                #{sourceDatabaseName}, #{targetDatabaseName}, #{migrationStatus},
                #{totalTables}, #{successfulTables}, #{failedTables}, #{totalRecords},
                #{migrationStartTime}, #{migrationEndTime}, #{migrationDurationMs},
                #{errorMessage}, #{migrationNotes}, #{sourceTableName}, #{targetTableName}, #{createdBy}
            )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(MigrationRecord migrationRecord);
    
    /**
     * 根据ID更新迁移记录
     */
    @Update("""
            UPDATE migration_records SET
                operator_id = #{operatorId},
                operator_name = #{operatorName},
                source_config_name = #{sourceConfigName},
                target_config_name = #{targetConfigName},
                source_database_type = #{sourceDatabaseType},
                target_database_type = #{targetDatabaseType},
                migration_status = #{migrationStatus},
                total_tables = #{totalTables},
                successful_tables = #{successfulTables},
                failed_tables = #{failedTables},
                total_records = #{totalRecords},
                migration_end_time = #{migrationEndTime},
                migration_duration_ms = #{migrationDurationMs},
                error_message = #{errorMessage},
                migration_notes = #{migrationNotes},
                source_table_name = #{sourceTableName},
                target_table_name = #{targetTableName}
            WHERE id = #{id}
            """)
    int updateById(MigrationRecord migrationRecord);
    
    /**
     * 根据ID查询迁移记录
     */
    @Select("SELECT * FROM migration_records WHERE id = #{id}")
    MigrationRecord selectById(Long id);
    
    /**
     * 根据批次ID查询迁移记录
     */
    @Select("SELECT * FROM migration_records WHERE migration_batch_id = #{migrationBatchId}")
    MigrationRecord selectByBatchId(Long migrationBatchId);
    
    /**
     * 查询所有迁移记录（分页）
     */
    @Select("""
            SELECT * FROM migration_records 
            ORDER BY migration_start_time DESC 
            LIMIT #{offset}, #{limit}
            """)
    List<MigrationRecord> selectAll(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询迁移记录总数
     */
    @Select("SELECT COUNT(*) FROM migration_records")
    int selectCount();
    
    /**
     * 根据源数据库配置ID查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE source_config_id = #{sourceConfigId} 
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectBySourceConfigId(Long sourceConfigId);
    
    /**
     * 根据目标数据库配置ID查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE target_config_id = #{targetConfigId} 
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByTargetConfigId(Long targetConfigId);
    
    /**
     * 根据迁移状态查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE migration_status = #{status} 
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByStatus(String status);
    
    /**
     * 查询最近的迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            ORDER BY migration_start_time DESC 
            LIMIT #{limit}
            """)
    List<MigrationRecord> selectRecent(int limit);
    
    /**
     * 删除迁移记录
     */
    @Delete("DELETE FROM migration_records WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 根据数据库类型查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE source_database_type = #{sourceDatabaseType} 
               OR target_database_type = #{targetDatabaseType}
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByDatabaseType(@Param("sourceDatabaseType") String sourceDatabaseType, 
                                              @Param("targetDatabaseType") String targetDatabaseType);
    
    /**
     * 根据配置名称查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE source_config_name LIKE CONCAT('%', #{configName}, '%') 
               OR target_config_name LIKE CONCAT('%', #{configName}, '%')
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByConfigName(String configName);
    
    /**
     * 根据源表名查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE source_table_name = #{sourceTableName}
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectBySourceTableName(String sourceTableName);
    
    /**
     * 根据目标表名查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE target_table_name = #{targetTableName}
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByTargetTableName(String targetTableName);
    
    /**
     * 根据源表名和目标表名查询迁移记录
     */
    @Select("""
            SELECT * FROM migration_records 
            WHERE source_table_name = #{sourceTableName} 
              AND target_table_name = #{targetTableName}
            ORDER BY migration_start_time DESC
            """)
    List<MigrationRecord> selectByTableMapping(@Param("sourceTableName") String sourceTableName, 
                                              @Param("targetTableName") String targetTableName);
    
    /**
     * 检查表迁移是否已存在
     */
    @Select("""
            SELECT COUNT(*) FROM migration_records 
            WHERE source_table_name = #{sourceTableName} 
              AND target_table_name = #{targetTableName}
              AND migration_status = 'SUCCESS'
            """)
    int checkTableMigrationExists(@Param("sourceTableName") String sourceTableName,
                                 @Param("targetTableName") String targetTableName);

    /**
     * 根据迁移批次ID删除迁移记录
     */
    @Delete("DELETE FROM migration_records WHERE migration_batch_id = #{migrationBatchId}")
    int deleteByMigrationBatchId(Long migrationBatchId);

    /**
     * 根据迁移批次ID查询迁移记录
     */
    @Select("SELECT * FROM migration_records WHERE migration_batch_id = #{migrationBatchId}")
    List<MigrationRecord> selectByMigrationBatchId(Long migrationBatchId);
    
    /**
     * 查询迁移汇总统计信息
     */
    @Select("""
            SELECT 
                COUNT(*) as total_migrations,
                SUM(CASE WHEN migration_status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_migrations,
                SUM(CASE WHEN migration_status = 'FAILED' THEN 1 ELSE 0 END) as failed_migrations,
                SUM(CASE WHEN migration_status = 'PARTIAL' THEN 1 ELSE 0 END) as partial_migrations,
                SUM(total_tables) as total_migrated_tables,
                SUM(total_records) as total_migrated_records,
                AVG(migration_duration_ms) as avg_migration_duration
            FROM migration_records
            """)
    @Results({
        @Result(property = "totalMigrations", column = "total_migrations"),
        @Result(property = "successfulMigrations", column = "successful_migrations"),
        @Result(property = "failedMigrations", column = "failed_migrations"),
        @Result(property = "partialMigrations", column = "partial_migrations"),
        @Result(property = "totalMigratedTables", column = "total_migrated_tables"),
        @Result(property = "totalMigratedRecords", column = "total_migrated_records"),
        @Result(property = "avgMigrationDuration", column = "avg_migration_duration")
    })
    MigrationStatistics getMigrationStatistics();
    
    /**
     * 迁移统计信息DTO
     */
    @Data
    class MigrationStatistics {
        private Long totalMigrations;
        private Long successfulMigrations;
        private Long failedMigrations;
        private Long partialMigrations;
        private Long totalMigratedTables;
        private Long totalMigratedRecords;
        private Double avgMigrationDuration;
    }
} 