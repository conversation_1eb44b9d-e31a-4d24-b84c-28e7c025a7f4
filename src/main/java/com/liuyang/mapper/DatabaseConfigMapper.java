package com.liuyang.mapper;

import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.dto.DatabaseConfigStatistics;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 数据库配置Mapper接口
 */
@Mapper
public interface DatabaseConfigMapper {

    /**
     * 根据ID查询配置
     */
    @Select("SELECT * FROM database_configs WHERE id = #{id}")
    DatabaseConfigEntity selectById(@Param("id") Long id);

    /**
     * 查询所有配置
     */
    @Select("SELECT * FROM database_configs ORDER BY config_type, config_name")
    List<DatabaseConfigEntity> selectList();

    /**
     * 插入新配置
     */
    @Insert({
        "INSERT INTO database_configs (config_name, config_type, database_type, host, port, username, password, ",
        "default_database, extra_params, is_active, environment, description, created_time, updated_time) ",
        "VALUES (#{configName}, #{configType}, #{databaseType}, #{host}, #{port}, #{username}, #{password}, ",
        "#{defaultDatabase}, #{extraParams}, #{isActive}, #{environment}, #{description}, #{createdTime}, #{updatedTime})"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DatabaseConfigEntity entity);

    /**
     * 更新配置
     */
    @Update({
        "UPDATE database_configs SET config_name=#{configName}, config_type=#{configType}, database_type=#{databaseType}, ",
        "host=#{host}, port=#{port}, username=#{username}, password=#{password}, default_database=#{defaultDatabase}, ",
        "extra_params=#{extraParams}, is_active=#{isActive}, environment=#{environment}, description=#{description}, ",
        "updated_time=#{updatedTime} WHERE id=#{id}"
    })
    int updateById(DatabaseConfigEntity entity);

    /**
     * 删除配置
     */
    @Delete("DELETE FROM database_configs WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 根据配置类型和环境查询启用的配置
     */
    @Select("SELECT * FROM database_configs WHERE config_type = #{configType} AND environment = #{environment} AND is_active = 1")
    List<DatabaseConfigEntity> findByConfigTypeAndEnvironment(@Param("configType") String configType, 
                                                              @Param("environment") String environment);

    /**
     * 根据配置名称和环境查询配置
     */
    @Select("SELECT * FROM database_configs WHERE config_name = #{configName} AND environment = #{environment}")
    DatabaseConfigEntity findByConfigNameAndEnvironment(@Param("configName") String configName, 
                                                       @Param("environment") String environment);

    /**
     * 根据数据库类型和环境查询配置
     */
    @Select("SELECT * FROM database_configs WHERE database_type = #{databaseType} AND environment = #{environment} AND is_active = 1")
    List<DatabaseConfigEntity> findByDatabaseTypeAndEnvironment(@Param("databaseType") String databaseType, 
                                                               @Param("environment") String environment);

    /**
     * 查询指定环境的所有启用配置
     */
    @Select("SELECT * FROM database_configs WHERE environment = #{environment} AND is_active = 1 ORDER BY config_type, config_name")
    List<DatabaseConfigEntity> findActiveByEnvironment(@Param("environment") String environment);

    /**
     * 根据配置类型查询默认配置（优先返回指定环境，然后是default环境）
     */
    @Select({
        "<script>",
        "SELECT * FROM database_configs ",
        "WHERE config_type = #{configType} AND is_active = 1 ",
        "AND environment IN (#{environment}, 'default') ",
        "ORDER BY CASE WHEN environment = #{environment} THEN 1 ELSE 2 END, config_name ",
        "LIMIT 1",
        "</script>"
    })
    DatabaseConfigEntity findDefaultByConfigType(@Param("configType") String configType, 
                                                @Param("environment") String environment);

    /**
     * 检查配置名称在指定环境下是否已存在
     */
    @Select("SELECT COUNT(*) FROM database_configs WHERE config_name = #{configName} AND environment = #{environment}")
    int countByConfigNameAndEnvironment(@Param("configName") String configName, 
                                      @Param("environment") String environment);

    /**
     * 获取所有环境列表
     */
    @Select("SELECT DISTINCT environment FROM database_configs ORDER BY environment")
    List<String> findAllEnvironments();

    /**
     * 查询指定环境的配置统计信息
     */
    @Select({
        "SELECT ",
        "  environment,",
        "  COUNT(*) as total_count,",
        "  SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,",
        "  SUM(CASE WHEN config_type = 'SOURCE' THEN 1 ELSE 0 END) as source_count,",
        "  SUM(CASE WHEN config_type = 'TARGET' THEN 1 ELSE 0 END) as target_count",
        "FROM database_configs ",
        "WHERE environment = #{environment}",
        "GROUP BY environment"
    })
    List<DatabaseConfigStatistics> getEnvironmentStatistics(@Param("environment") String environment);

} 