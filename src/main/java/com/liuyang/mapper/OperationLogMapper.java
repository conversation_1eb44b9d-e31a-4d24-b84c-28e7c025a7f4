package com.liuyang.mapper;

import com.liuyang.entity.OperationLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志Mapper接口
 */
@Mapper
public interface OperationLogMapper {

    /**
     * 插入操作日志
     */
    @Insert({
        "INSERT INTO operation_logs (",
        "user_id, username, operation_type, operation_desc, target_type, target_id,",
        "request_method, request_url, request_params, response_status, error_message,",
        "execution_time, client_ip, user_agent, created_time",
        ") VALUES (",
        "#{userId}, #{username}, #{operationType}, #{operationDesc}, #{targetType}, #{targetId},",
        "#{requestMethod}, #{requestUrl}, #{requestParams}, #{responseStatus}, #{errorMessage},",
        "#{executionTime}, #{clientIp}, #{userAgent}, #{createdTime}",
        ")"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(OperationLog operationLog);

    /**
     * 根据ID查询操作日志
     */
    @Select("SELECT * FROM operation_logs WHERE id = #{id}")
    OperationLog selectById(@Param("id") Long id);

    /**
     * 分页查询操作日志
     */
    List<OperationLog> selectByPage(@Param("userId") Long userId,
                                   @Param("username") String username,
                                   @Param("operationType") String operationType,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("offset") int offset,
                                   @Param("pageSize") int pageSize);

    /**
     * 统计操作日志总数
     */
    long countByCondition(@Param("userId") Long userId,
                         @Param("username") String username,
                         @Param("operationType") String operationType,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询操作类型统计
     */
    List<Map<String, Object>> selectOperationTypeStats(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户操作统计
     */
    List<Map<String, Object>> selectUserOperationStats(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询每日操作统计
     */
    List<Map<String, Object>> selectDailyOperationStats(@Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的日志
     */
    @Delete("DELETE FROM operation_logs WHERE created_time < #{beforeTime}")
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询最近的操作日志
     */
    @Select({
        "SELECT * FROM operation_logs",
        "ORDER BY created_time DESC",
        "LIMIT #{limit}"
    })
    List<OperationLog> selectRecent(@Param("limit") int limit);

    /**
     * 查询错误日志统计
     */
    Map<String, Object> selectErrorStats(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
}
