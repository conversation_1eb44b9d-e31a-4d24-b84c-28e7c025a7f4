package com.liuyang.mapper;

import com.liuyang.entity.InvitationCode;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 邀请码数据访问层
 */
@Mapper
public interface InvitationCodeMapper {
    
    /**
     * 根据ID查询邀请码
     */
    @Select("SELECT * FROM invitation_codes WHERE id = #{id}")
    InvitationCode selectById(Long id);
    
    /**
     * 查询所有激活的邀请码
     */
    @Select("SELECT * FROM invitation_codes WHERE is_active = 1 ORDER BY created_time DESC")
    List<InvitationCode> selectAllActive();
    
    /**
     * 查询所有邀请码
     */
    @Select("SELECT * FROM invitation_codes ORDER BY created_time DESC")
    List<InvitationCode> selectAll();
    
    /**
     * 根据哈希值查询邀请码
     */
    @Select("SELECT * FROM invitation_codes WHERE code_hash = #{codeHash}")
    InvitationCode selectByCodeHash(String codeHash);
    
    /**
     * 插入邀请码
     */
    @Insert("INSERT INTO invitation_codes (code_hash, description, is_active, usage_count, max_usage, created_by) " +
            "VALUES (#{codeHash}, #{description}, #{isActive}, #{usageCount}, #{maxUsage}, #{createdBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(InvitationCode invitationCode);
    
    /**
     * 更新邀请码信息
     */
    @Update("UPDATE invitation_codes SET description = #{description}, is_active = #{isActive}, " +
            "max_usage = #{maxUsage}, updated_by = #{updatedBy}, updated_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int update(InvitationCode invitationCode);
    
    /**
     * 更新使用次数
     */
    @Update("UPDATE invitation_codes SET usage_count = #{usageCount}, updated_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int updateUsageCount(@Param("id") Long id, @Param("usageCount") Integer usageCount);
    
    /**
     * 增加使用次数
     */
    @Update("UPDATE invitation_codes SET usage_count = usage_count + 1, updated_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int incrementUsageCount(Long id);
    
    /**
     * 删除邀请码
     */
    @Delete("DELETE FROM invitation_codes WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查邀请码哈希是否存在
     */
    @Select("SELECT COUNT(*) FROM invitation_codes WHERE code_hash = #{codeHash}")
    int countByCodeHash(String codeHash);
    
    /**
     * 查询可用的邀请码（激活且未超过使用限制）
     */
    @Select("SELECT * FROM invitation_codes WHERE is_active = 1 AND " +
            "(max_usage = -1 OR usage_count < max_usage) ORDER BY created_time DESC")
    List<InvitationCode> selectAvailable();
}
