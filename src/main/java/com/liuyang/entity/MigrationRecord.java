package com.liuyang.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移记录主表实体类
 */
public class MigrationRecord {
    
    private Long id;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long migrationBatchId;

    // 操作人信息
    private Long operatorId;
    private String operatorName;

    private Long sourceConfigId;
    private Long targetConfigId;
    private String sourceConfigName;
    private String targetConfigName;
    private String sourceDatabaseType;
    private String targetDatabaseType;
    private String sourceDatabaseName;
    private String targetDatabaseName;
    private String migrationStatus;
    private Integer totalTables;
    private Integer successfulTables;
    private Integer failedTables;
    private Long totalRecords;
    private LocalDateTime migrationStartTime;
    private LocalDateTime migrationEndTime;
    private Long migrationDurationMs;
    private String errorMessage;
    private String migrationNotes;
    private String sourceTableName;
    private String targetTableName;
    private String createdBy;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    
    // 关联的表映射记录
    private List<TableMappingRecord> tableMappingRecords;
    
    // 构造函数
    public MigrationRecord() {}
    
    public MigrationRecord(Long migrationBatchId, Long sourceConfigId, Long targetConfigId, 
                          String sourceDatabaseName, String targetDatabaseName) {
        this.migrationBatchId = migrationBatchId;
        this.sourceConfigId = sourceConfigId;
        this.targetConfigId = targetConfigId;
        this.sourceDatabaseName = sourceDatabaseName;
        this.targetDatabaseName = targetDatabaseName;
        this.migrationStatus = "IN_PROGRESS";
        this.totalTables = 0;
        this.successfulTables = 0;
        this.failedTables = 0;
        this.totalRecords = 0L;
        this.migrationStartTime = LocalDateTime.now();
        this.createdBy = "system";
    }
    
    // 重载构造函数，包含冗余字段
    public MigrationRecord(Long migrationBatchId, Long sourceConfigId, Long targetConfigId, 
                          String sourceConfigName, String targetConfigName,
                          String sourceDatabaseType, String targetDatabaseType,
                          String sourceDatabaseName, String targetDatabaseName) {
        this(migrationBatchId, sourceConfigId, targetConfigId, sourceDatabaseName, targetDatabaseName);
        this.sourceConfigName = sourceConfigName;
        this.targetConfigName = targetConfigName;
        this.sourceDatabaseType = sourceDatabaseType;
        this.targetDatabaseType = targetDatabaseType;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMigrationBatchId() {
        return migrationBatchId;
    }
    
    public void setMigrationBatchId(Long migrationBatchId) {
        this.migrationBatchId = migrationBatchId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Long getSourceConfigId() {
        return sourceConfigId;
    }
    
    public void setSourceConfigId(Long sourceConfigId) {
        this.sourceConfigId = sourceConfigId;
    }
    
    public Long getTargetConfigId() {
        return targetConfigId;
    }
    
    public void setTargetConfigId(Long targetConfigId) {
        this.targetConfigId = targetConfigId;
    }
    
    public String getSourceConfigName() {
        return sourceConfigName;
    }
    
    public void setSourceConfigName(String sourceConfigName) {
        this.sourceConfigName = sourceConfigName;
    }
    
    public String getTargetConfigName() {
        return targetConfigName;
    }
    
    public void setTargetConfigName(String targetConfigName) {
        this.targetConfigName = targetConfigName;
    }
    
    public String getSourceDatabaseType() {
        return sourceDatabaseType;
    }
    
    public void setSourceDatabaseType(String sourceDatabaseType) {
        this.sourceDatabaseType = sourceDatabaseType;
    }
    
    public String getTargetDatabaseType() {
        return targetDatabaseType;
    }
    
    public void setTargetDatabaseType(String targetDatabaseType) {
        this.targetDatabaseType = targetDatabaseType;
    }
    
    public String getSourceDatabaseName() {
        return sourceDatabaseName;
    }
    
    public void setSourceDatabaseName(String sourceDatabaseName) {
        this.sourceDatabaseName = sourceDatabaseName;
    }
    
    public String getTargetDatabaseName() {
        return targetDatabaseName;
    }
    
    public void setTargetDatabaseName(String targetDatabaseName) {
        this.targetDatabaseName = targetDatabaseName;
    }
    
    public String getMigrationStatus() {
        return migrationStatus;
    }
    
    public void setMigrationStatus(String migrationStatus) {
        this.migrationStatus = migrationStatus;
    }
    
    public Integer getTotalTables() {
        return totalTables;
    }
    
    public void setTotalTables(Integer totalTables) {
        this.totalTables = totalTables;
    }
    
    public Integer getSuccessfulTables() {
        return successfulTables;
    }
    
    public void setSuccessfulTables(Integer successfulTables) {
        this.successfulTables = successfulTables;
    }
    
    public Integer getFailedTables() {
        return failedTables;
    }
    
    public void setFailedTables(Integer failedTables) {
        this.failedTables = failedTables;
    }
    
    public Long getTotalRecords() {
        return totalRecords;
    }
    
    public void setTotalRecords(Long totalRecords) {
        this.totalRecords = totalRecords;
    }
    
    public LocalDateTime getMigrationStartTime() {
        return migrationStartTime;
    }
    
    public void setMigrationStartTime(LocalDateTime migrationStartTime) {
        this.migrationStartTime = migrationStartTime;
    }
    
    public LocalDateTime getMigrationEndTime() {
        return migrationEndTime;
    }
    
    public void setMigrationEndTime(LocalDateTime migrationEndTime) {
        this.migrationEndTime = migrationEndTime;
    }
    
    public Long getMigrationDurationMs() {
        return migrationDurationMs;
    }
    
    public void setMigrationDurationMs(Long migrationDurationMs) {
        this.migrationDurationMs = migrationDurationMs;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getMigrationNotes() {
        return migrationNotes;
    }
    
    public void setMigrationNotes(String migrationNotes) {
        this.migrationNotes = migrationNotes;
    }
    
    public String getSourceTableName() {
        return sourceTableName;
    }
    
    public void setSourceTableName(String sourceTableName) {
        this.sourceTableName = sourceTableName;
    }
    
    public String getTargetTableName() {
        return targetTableName;
    }
    
    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public List<TableMappingRecord> getTableMappingRecords() {
        return tableMappingRecords;
    }
    
    public void setTableMappingRecords(List<TableMappingRecord> tableMappingRecords) {
        this.tableMappingRecords = tableMappingRecords;
    }
    
    // 业务方法
    public void markAsCompleted() {
        this.migrationEndTime = LocalDateTime.now();
        if (this.migrationStartTime != null) {
            this.migrationDurationMs = java.time.Duration.between(this.migrationStartTime, this.migrationEndTime).toMillis();
        }
        
        if (this.failedTables == null || this.failedTables == 0) {
            this.migrationStatus = "SUCCESS";
        } else if (this.successfulTables == null || this.successfulTables == 0) {
            this.migrationStatus = "FAILED";
        } else {
            this.migrationStatus = "PARTIAL";
        }
    }
    
    public void incrementSuccessfulTables() {
        if (this.successfulTables == null) {
            this.successfulTables = 0;
        }
        this.successfulTables++;
    }
    
    public void incrementFailedTables() {
        if (this.failedTables == null) {
            this.failedTables = 0;
        }
        this.failedTables++;
    }
    
    public void addTotalRecords(long count) {
        if (this.totalRecords == null) {
            this.totalRecords = 0L;
        }
        this.totalRecords += count;
    }
    
    @Override
    public String toString() {
        return "MigrationRecord{" +
                "id=" + id +
                ", migrationBatchId=" + migrationBatchId +
                ", sourceConfigName='" + sourceConfigName + '\'' +
                ", targetConfigName='" + targetConfigName + '\'' +
                ", sourceDatabaseType='" + sourceDatabaseType + '\'' +
                ", targetDatabaseType='" + targetDatabaseType + '\'' +
                ", sourceDatabaseName='" + sourceDatabaseName + '\'' +
                ", targetDatabaseName='" + targetDatabaseName + '\'' +
                ", migrationStatus='" + migrationStatus + '\'' +
                ", totalTables=" + totalTables +
                ", successfulTables=" + successfulTables +
                ", failedTables=" + failedTables +
                ", totalRecords=" + totalRecords +
                ", migrationStartTime=" + migrationStartTime +
                ", migrationEndTime=" + migrationEndTime +
                '}';
    }
} 