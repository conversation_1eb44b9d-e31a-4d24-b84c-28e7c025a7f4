package com.liuyang.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码(加密后) - 序列化时忽略
     */
    @JsonIgnore
    private String password;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 角色: ADMIN, USER
     */
    private String role;
    
    /**
     * 状态: 1-启用, 0-禁用
     */
    private Integer status;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 用户角色枚举
     */
    public enum Role {
        ADMIN("ADMIN", "管理员"),
        USER("USER", "普通用户");
        
        private final String code;
        private final String name;
        
        Role(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 用户状态枚举
     */
    public enum Status {
        DISABLED(0, "禁用"),
        ENABLED(1, "启用");
        
        private final Integer code;
        private final String name;
        
        Status(Integer code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public Integer getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return Status.ENABLED.getCode().equals(this.status);
    }
    
    /**
     * 检查用户是否是管理员
     */
    public boolean isAdmin() {
        return Role.ADMIN.getCode().equals(this.role);
    }
    
    /**
     * 获取显示名称（优先使用真实姓名，否则使用用户名）
     */
    public String getDisplayName() {
        return realName != null && !realName.trim().isEmpty() ? realName : username;
    }
}
