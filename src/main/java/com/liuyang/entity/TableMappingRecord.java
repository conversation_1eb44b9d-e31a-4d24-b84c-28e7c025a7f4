package com.liuyang.entity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 表映射记录实体类
 */
public class TableMappingRecord {
    
    private Long id;
    private Long migrationBatchId;  // 改为Long类型支持雪花算法（冗余字段，便于直接查询）

    // 操作人信息
    private Long operatorId;
    private String operatorName;

    private Long migrationRecordId;
    private String sourceTableName;
    private String sourceTableSchema;
    private String sourceTableComment;
    private String sourceTableType;
    private String targetTableName;
    private String targetTableComment;
    private String aiPromptDescription;  // AI优化表名时使用的提示词描述
    private String targetDdlStatement;
    private String tableMappingStatus;
    private Long sourceRecordCount;
    private Long targetRecordCount;
    private String dataMigrationStatus;
    private Integer fieldCount;
    private String errorMessage;
    private LocalDateTime migrationStartTime;
    private LocalDateTime migrationEndTime;
    private Long migrationDurationMs;
    private LocalDateTime createdTime;
    
    // 关联的字段映射记录
    private List<FieldMappingRecord> fieldMappingRecords;
    
    // 构造函数
    public TableMappingRecord() {}
    
    public TableMappingRecord(Long migrationRecordId, String sourceTableName, String targetTableName) {
        this.migrationRecordId = migrationRecordId;
        this.sourceTableName = sourceTableName;
        this.targetTableName = targetTableName;
        this.tableMappingStatus = "IN_PROGRESS";
        this.dataMigrationStatus = "PENDING";
        this.sourceRecordCount = 0L;
        this.targetRecordCount = 0L;
        this.fieldCount = 0;
        this.migrationStartTime = LocalDateTime.now();
    }
    
    // 重载构造函数，包含冗余字段
    public TableMappingRecord(Long migrationBatchId, Long migrationRecordId, String sourceTableName, String targetTableName) {
        this(migrationRecordId, sourceTableName, targetTableName);
        this.migrationBatchId = migrationBatchId;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMigrationBatchId() {
        return migrationBatchId;
    }
    
    public void setMigrationBatchId(Long migrationBatchId) {
        this.migrationBatchId = migrationBatchId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Long getMigrationRecordId() {
        return migrationRecordId;
    }
    
    public void setMigrationRecordId(Long migrationRecordId) {
        this.migrationRecordId = migrationRecordId;
    }
    
    public String getSourceTableName() {
        return sourceTableName;
    }
    
    public void setSourceTableName(String sourceTableName) {
        this.sourceTableName = sourceTableName;
    }
    
    public String getSourceTableSchema() {
        return sourceTableSchema;
    }
    
    public void setSourceTableSchema(String sourceTableSchema) {
        this.sourceTableSchema = sourceTableSchema;
    }
    
    public String getSourceTableComment() {
        return sourceTableComment;
    }
    
    public void setSourceTableComment(String sourceTableComment) {
        this.sourceTableComment = sourceTableComment;
    }
    
    public String getSourceTableType() {
        return sourceTableType;
    }
    
    public void setSourceTableType(String sourceTableType) {
        this.sourceTableType = sourceTableType;
    }
    
    public String getTargetTableName() {
        return targetTableName;
    }
    
    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }
    
    public String getTargetTableComment() {
        return targetTableComment;
    }
    
    public void setTargetTableComment(String targetTableComment) {
        this.targetTableComment = targetTableComment;
    }

    public String getAiPromptDescription() {
        return aiPromptDescription;
    }

    public void setAiPromptDescription(String aiPromptDescription) {
        this.aiPromptDescription = aiPromptDescription;
    }

    public String getTargetDdlStatement() {
        return targetDdlStatement;
    }
    
    public void setTargetDdlStatement(String targetDdlStatement) {
        this.targetDdlStatement = targetDdlStatement;
    }
    
    public String getTableMappingStatus() {
        return tableMappingStatus;
    }
    
    public void setTableMappingStatus(String tableMappingStatus) {
        this.tableMappingStatus = tableMappingStatus;
    }
    
    public Long getSourceRecordCount() {
        return sourceRecordCount;
    }
    
    public void setSourceRecordCount(Long sourceRecordCount) {
        this.sourceRecordCount = sourceRecordCount;
    }
    
    public Long getTargetRecordCount() {
        return targetRecordCount;
    }
    
    public void setTargetRecordCount(Long targetRecordCount) {
        this.targetRecordCount = targetRecordCount;
    }
    
    public String getDataMigrationStatus() {
        return dataMigrationStatus;
    }
    
    public void setDataMigrationStatus(String dataMigrationStatus) {
        this.dataMigrationStatus = dataMigrationStatus;
    }
    
    public Integer getFieldCount() {
        return fieldCount;
    }
    
    public void setFieldCount(Integer fieldCount) {
        this.fieldCount = fieldCount;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public LocalDateTime getMigrationStartTime() {
        return migrationStartTime;
    }
    
    public void setMigrationStartTime(LocalDateTime migrationStartTime) {
        this.migrationStartTime = migrationStartTime;
    }
    
    public LocalDateTime getMigrationEndTime() {
        return migrationEndTime;
    }
    
    public void setMigrationEndTime(LocalDateTime migrationEndTime) {
        this.migrationEndTime = migrationEndTime;
    }
    
    public Long getMigrationDurationMs() {
        return migrationDurationMs;
    }
    
    public void setMigrationDurationMs(Long migrationDurationMs) {
        this.migrationDurationMs = migrationDurationMs;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public List<FieldMappingRecord> getFieldMappingRecords() {
        return fieldMappingRecords;
    }
    
    public void setFieldMappingRecords(List<FieldMappingRecord> fieldMappingRecords) {
        this.fieldMappingRecords = fieldMappingRecords;
    }
    
    // 业务方法
    public void markAsCompleted() {
        this.migrationEndTime = LocalDateTime.now();
        if (this.migrationStartTime != null) {
            this.migrationDurationMs = java.time.Duration.between(this.migrationStartTime, this.migrationEndTime).toMillis();
        }
        this.tableMappingStatus = "SUCCESS";
    }
    
    public void markAsFailed(String errorMessage) {
        this.migrationEndTime = LocalDateTime.now();
        if (this.migrationStartTime != null) {
            this.migrationDurationMs = java.time.Duration.between(this.migrationStartTime, this.migrationEndTime).toMillis();
        }
        this.tableMappingStatus = "FAILED";
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return "TableMappingRecord{" +
                "id=" + id +
                ", migrationBatchId=" + migrationBatchId +
                ", migrationRecordId=" + migrationRecordId +
                ", sourceTableName='" + sourceTableName + '\'' +
                ", targetTableName='" + targetTableName + '\'' +
                ", tableMappingStatus='" + tableMappingStatus + '\'' +
                ", sourceRecordCount=" + sourceRecordCount +
                ", targetRecordCount=" + targetRecordCount +
                ", fieldCount=" + fieldCount +
                '}';
    }
} 