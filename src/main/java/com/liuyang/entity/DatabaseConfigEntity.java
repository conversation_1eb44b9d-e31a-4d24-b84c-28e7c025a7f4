package com.liuyang.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

/**
 * 数据库配置实体类
 */
public class DatabaseConfigEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型：SOURCE(源数据库), TARGET(目标数据库)
     */
    private String configType;

    /**
     * 数据库类型：POSTGRESQL, MYSQL
     */
    private String databaseType;

    /**
     * 数据库主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 默认数据库名
     */
    private String defaultDatabase;

    /**
     * 额外连接参数（JSON格式）
     */
    private String extraParams;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean isActive;

    /**
     * 环境：dev, test, prod, default
     */
    private String environment;

    /**
     * 配置描述
     */
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 默认构造函数
    public DatabaseConfigEntity() {}

    // 全参构造函数
    public DatabaseConfigEntity(String configName, String configType, String databaseType, 
                               String host, Integer port, String username, String password,
                               String defaultDatabase, String environment, String description) {
        this.configName = configName;
        this.configType = configType;
        this.databaseType = databaseType;
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.defaultDatabase = defaultDatabase;
        this.environment = environment;
        this.description = description;
        this.isActive = true;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getDatabaseType() {
        return databaseType;
    }

    public void setDatabaseType(String databaseType) {
        this.databaseType = databaseType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDefaultDatabase() {
        return defaultDatabase;
    }

    public void setDefaultDatabase(String defaultDatabase) {
        this.defaultDatabase = defaultDatabase;
    }

    public String getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(String extraParams) {
        this.extraParams = extraParams;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 生成JDBC URL
     */
    public String getJdbcUrl(String databaseName) {
        String dbName = databaseName != null ? databaseName : 
                       (defaultDatabase != null ? defaultDatabase : "");
        
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return String.format("jdbc:postgresql://%s:%d/%s", host, port, dbName);
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            String params = extraParams != null && !extraParams.trim().isEmpty() 
                ? "?" + parseExtraParams() : "";
            return String.format("***********************", host, port, dbName, params);
        }
        throw new IllegalArgumentException("不支持的数据库类型: " + databaseType);
    }

    /**
     * 解析额外参数
     */
    private String parseExtraParams() {
        if (extraParams == null || extraParams.trim().isEmpty()) {
            return "";
        }
        
        // 简单的JSON解析，将{"key":"value"}转换为key=value&格式
        try {
            return extraParams.replaceAll("[{}\"\\s]", "")
                    .replace(":", "=")
                    .replace(",", "&");
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String toString() {
        return "DatabaseConfigEntity{" +
                "id=" + id +
                ", configName='" + configName + '\'' +
                ", configType='" + configType + '\'' +
                ", databaseType='" + databaseType + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", username='" + username + '\'' +
                ", defaultDatabase='" + defaultDatabase + '\'' +
                ", isActive=" + isActive +
                ", environment='" + environment + '\'' +
                ", description='" + description + '\'' +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }

    /**
     * 配置类型枚举
     */
    public static class ConfigType {
        public static final String SOURCE = "SOURCE";
        public static final String TARGET = "TARGET";
    }

    /**
     * 数据库类型枚举
     */
    public static class DatabaseType {
        public static final String POSTGRESQL = "POSTGRESQL";
        public static final String MYSQL = "MYSQL";
    }
} 