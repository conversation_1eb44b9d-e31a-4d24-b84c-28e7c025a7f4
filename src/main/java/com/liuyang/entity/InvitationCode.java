package com.liuyang.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 邀请码实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvitationCode {
    
    /**
     * 邀请码ID
     */
    private Long id;
    
    /**
     * 邀请码哈希值(BCrypt加密)
     */
    private String codeHash;
    
    /**
     * 邀请码描述
     */
    private String description;
    
    /**
     * 是否激活: 1-激活, 0-禁用
     */
    private Integer isActive;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 最大使用次数(-1表示无限制)
     */
    private Integer maxUsage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 检查邀请码是否可用
     */
    public boolean isAvailable() {
        return isActive != null && isActive == 1 && 
               (maxUsage == null || maxUsage == -1 || 
                (usageCount != null && usageCount < maxUsage));
    }
    
    /**
     * 检查是否还有使用次数
     */
    public boolean hasUsageLeft() {
        return maxUsage == null || maxUsage == -1 || 
               (usageCount != null && usageCount < maxUsage);
    }
    
    /**
     * 增加使用次数
     */
    public void incrementUsage() {
        if (usageCount == null) {
            usageCount = 0;
        }
        usageCount++;
    }
}
