package com.liuyang.entity;

import java.time.LocalDateTime;

/**
 * 字段映射记录实体类
 */
public class FieldMappingRecord {
    
    private Long id;
    private Long migrationBatchId;  // 改为Long类型支持雪花算法（冗余字段，便于直接查询）
    private Long migrationRecordId;   // 新增：所属迁移记录ID（冗余字段）
    private Long tableMappingRecordId;

    // 操作人信息
    private Long operatorId;
    private String operatorName;

    private String sourceTableName;   // 新增：源表名（冗余字段）
    private String targetTableName;   // 新增：目标表名（冗余字段）
    
    // 源字段信息
    private String sourceFieldName;
    private String sourceFieldType;
    private Integer sourceFieldLength;
    private Integer sourceFieldPrecision;
    private Integer sourceFieldScale;
    private Boolean sourceFieldNullable;
    private String sourceFieldDefaultValue;
    private String sourceFieldComment;
    private Boolean sourceFieldIsPrimary;
    private Boolean sourceFieldIsUnique;
    private Boolean sourceFieldIsIndex;
    
    // 目标字段信息
    private String targetFieldName;
    private String targetFieldType;
    private Integer targetFieldLength;
    private Integer targetFieldPrecision;
    private Integer targetFieldScale;
    private Boolean targetFieldNullable;
    private String targetFieldDefaultValue;
    private String targetFieldComment;
    private Boolean targetFieldIsPrimary;
    private Boolean targetFieldIsUnique;
    private Boolean targetFieldIsIndex;
    
    // 映射信息
    private Integer fieldOrderPosition;
    private String mappingRule;
    private String conversionNotes;
    private LocalDateTime createdTime;
    
    // 构造函数
    public FieldMappingRecord() {}
    
    public FieldMappingRecord(Long tableMappingRecordId, String sourceFieldName, String targetFieldName) {
        this.tableMappingRecordId = tableMappingRecordId;
        this.sourceFieldName = sourceFieldName;
        this.targetFieldName = targetFieldName;
        this.mappingRule = "DIRECT";
        this.sourceFieldNullable = true;
        this.targetFieldNullable = true;
        this.sourceFieldIsPrimary = false;
        this.sourceFieldIsUnique = false;
        this.sourceFieldIsIndex = false;
        this.targetFieldIsPrimary = false;
        this.targetFieldIsUnique = false;
        this.targetFieldIsIndex = false;
    }
    
    // 重载构造函数，包含冗余字段
    public FieldMappingRecord(Long migrationBatchId, Long migrationRecordId, Long tableMappingRecordId, 
                             String sourceTableName, String targetTableName, 
                             String sourceFieldName, String targetFieldName) {
        this(tableMappingRecordId, sourceFieldName, targetFieldName);
        this.migrationBatchId = migrationBatchId;
        this.migrationRecordId = migrationRecordId;
        this.sourceTableName = sourceTableName;
        this.targetTableName = targetTableName;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getMigrationBatchId() {
        return migrationBatchId;
    }
    
    public void setMigrationBatchId(Long migrationBatchId) {
        this.migrationBatchId = migrationBatchId;
    }
    
    public Long getMigrationRecordId() {
        return migrationRecordId;
    }
    
    public void setMigrationRecordId(Long migrationRecordId) {
        this.migrationRecordId = migrationRecordId;
    }
    
    public Long getTableMappingRecordId() {
        return tableMappingRecordId;
    }
    
    public void setTableMappingRecordId(Long tableMappingRecordId) {
        this.tableMappingRecordId = tableMappingRecordId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getSourceTableName() {
        return sourceTableName;
    }
    
    public void setSourceTableName(String sourceTableName) {
        this.sourceTableName = sourceTableName;
    }
    
    public String getTargetTableName() {
        return targetTableName;
    }
    
    public void setTargetTableName(String targetTableName) {
        this.targetTableName = targetTableName;
    }
    
    public String getSourceFieldName() {
        return sourceFieldName;
    }
    
    public void setSourceFieldName(String sourceFieldName) {
        this.sourceFieldName = sourceFieldName;
    }
    
    public String getSourceFieldType() {
        return sourceFieldType;
    }
    
    public void setSourceFieldType(String sourceFieldType) {
        this.sourceFieldType = sourceFieldType;
    }
    
    public Integer getSourceFieldLength() {
        return sourceFieldLength;
    }
    
    public void setSourceFieldLength(Integer sourceFieldLength) {
        this.sourceFieldLength = sourceFieldLength;
    }
    
    public Integer getSourceFieldPrecision() {
        return sourceFieldPrecision;
    }
    
    public void setSourceFieldPrecision(Integer sourceFieldPrecision) {
        this.sourceFieldPrecision = sourceFieldPrecision;
    }
    
    public Integer getSourceFieldScale() {
        return sourceFieldScale;
    }
    
    public void setSourceFieldScale(Integer sourceFieldScale) {
        this.sourceFieldScale = sourceFieldScale;
    }
    
    public Boolean getSourceFieldNullable() {
        return sourceFieldNullable;
    }
    
    public void setSourceFieldNullable(Boolean sourceFieldNullable) {
        this.sourceFieldNullable = sourceFieldNullable;
    }
    
    public String getSourceFieldDefaultValue() {
        return sourceFieldDefaultValue;
    }
    
    public void setSourceFieldDefaultValue(String sourceFieldDefaultValue) {
        this.sourceFieldDefaultValue = sourceFieldDefaultValue;
    }
    
    public String getSourceFieldComment() {
        return sourceFieldComment;
    }
    
    public void setSourceFieldComment(String sourceFieldComment) {
        this.sourceFieldComment = sourceFieldComment;
    }
    
    public Boolean getSourceFieldIsPrimary() {
        return sourceFieldIsPrimary;
    }
    
    public void setSourceFieldIsPrimary(Boolean sourceFieldIsPrimary) {
        this.sourceFieldIsPrimary = sourceFieldIsPrimary;
    }
    
    public Boolean getSourceFieldIsUnique() {
        return sourceFieldIsUnique;
    }
    
    public void setSourceFieldIsUnique(Boolean sourceFieldIsUnique) {
        this.sourceFieldIsUnique = sourceFieldIsUnique;
    }
    
    public Boolean getSourceFieldIsIndex() {
        return sourceFieldIsIndex;
    }
    
    public void setSourceFieldIsIndex(Boolean sourceFieldIsIndex) {
        this.sourceFieldIsIndex = sourceFieldIsIndex;
    }
    
    public String getTargetFieldName() {
        return targetFieldName;
    }
    
    public void setTargetFieldName(String targetFieldName) {
        this.targetFieldName = targetFieldName;
    }
    
    public String getTargetFieldType() {
        return targetFieldType;
    }
    
    public void setTargetFieldType(String targetFieldType) {
        this.targetFieldType = targetFieldType;
    }
    
    public Integer getTargetFieldLength() {
        return targetFieldLength;
    }
    
    public void setTargetFieldLength(Integer targetFieldLength) {
        this.targetFieldLength = targetFieldLength;
    }
    
    public Integer getTargetFieldPrecision() {
        return targetFieldPrecision;
    }
    
    public void setTargetFieldPrecision(Integer targetFieldPrecision) {
        this.targetFieldPrecision = targetFieldPrecision;
    }
    
    public Integer getTargetFieldScale() {
        return targetFieldScale;
    }
    
    public void setTargetFieldScale(Integer targetFieldScale) {
        this.targetFieldScale = targetFieldScale;
    }
    
    public Boolean getTargetFieldNullable() {
        return targetFieldNullable;
    }
    
    public void setTargetFieldNullable(Boolean targetFieldNullable) {
        this.targetFieldNullable = targetFieldNullable;
    }
    
    public String getTargetFieldDefaultValue() {
        return targetFieldDefaultValue;
    }
    
    public void setTargetFieldDefaultValue(String targetFieldDefaultValue) {
        this.targetFieldDefaultValue = targetFieldDefaultValue;
    }
    
    public String getTargetFieldComment() {
        return targetFieldComment;
    }
    
    public void setTargetFieldComment(String targetFieldComment) {
        this.targetFieldComment = targetFieldComment;
    }
    
    public Boolean getTargetFieldIsPrimary() {
        return targetFieldIsPrimary;
    }
    
    public void setTargetFieldIsPrimary(Boolean targetFieldIsPrimary) {
        this.targetFieldIsPrimary = targetFieldIsPrimary;
    }
    
    public Boolean getTargetFieldIsUnique() {
        return targetFieldIsUnique;
    }
    
    public void setTargetFieldIsUnique(Boolean targetFieldIsUnique) {
        this.targetFieldIsUnique = targetFieldIsUnique;
    }
    
    public Boolean getTargetFieldIsIndex() {
        return targetFieldIsIndex;
    }
    
    public void setTargetFieldIsIndex(Boolean targetFieldIsIndex) {
        this.targetFieldIsIndex = targetFieldIsIndex;
    }
    
    public Integer getFieldOrderPosition() {
        return fieldOrderPosition;
    }
    
    public void setFieldOrderPosition(Integer fieldOrderPosition) {
        this.fieldOrderPosition = fieldOrderPosition;
    }
    
    public String getMappingRule() {
        return mappingRule;
    }
    
    public void setMappingRule(String mappingRule) {
        this.mappingRule = mappingRule;
    }
    
    public String getConversionNotes() {
        return conversionNotes;
    }
    
    public void setConversionNotes(String conversionNotes) {
        this.conversionNotes = conversionNotes;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    @Override
    public String toString() {
        return "FieldMappingRecord{" +
                "id=" + id +
                ", migrationBatchId=" + migrationBatchId +
                ", migrationRecordId=" + migrationRecordId +
                ", tableMappingRecordId=" + tableMappingRecordId +
                ", sourceTableName='" + sourceTableName + '\'' +
                ", targetTableName='" + targetTableName + '\'' +
                ", sourceFieldName='" + sourceFieldName + '\'' +
                ", sourceFieldType='" + sourceFieldType + '\'' +
                ", targetFieldName='" + targetFieldName + '\'' +
                ", targetFieldType='" + targetFieldType + '\'' +
                ", mappingRule='" + mappingRule + '\'' +
                '}';
    }
} 