package com.liuyang.service;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.EnhancedFieldMapping;
import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.impl.FieldMappingAnalysisServiceimpl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public interface FieldMappingAnalysisService {
    /**
     * 分析源表结构并生成智能字段映射建议
     */
    public FieldMappingAnalysisServiceimpl.FieldMappingAnalysisResult analyzeFieldMappings(
            TableStructure sourceTable,
            List<FieldMapping> templateMappings) ;

    /**
     * 分析源表结构并生成智能字段映射建议（支持AI增强）
     */
    public FieldMappingAnalysisServiceimpl.FieldMappingAnalysisResult analyzeFieldMappings(
            TableStructure sourceTable,
            List<FieldMapping> templateMappings,
            String targetTableName,
            String businessContext) ;
}
