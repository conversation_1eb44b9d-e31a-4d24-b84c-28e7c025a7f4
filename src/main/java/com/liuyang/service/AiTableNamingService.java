package com.liuyang.service;

public interface AiTableNamingService {
    /**
     * 生成AI优化的表名
     *
     * @param originalTableName 原表名
     * @param tableComment 表注释/说明
     * @param businessContext 业务上下文
     * @return 优化后的表名，以hc_开头，使用snake_case格式
     */
    public String generateOptimizedTableName(String originalTableName, String tableComment, String businessContext) ;
    /**
     * 检查AI服务是否可用
     */
    public boolean isAiServiceAvailable();
}
