package com.liuyang.service;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.EnhancedFieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.impl.EnhancedFieldMappingServiceImpl;

import java.util.ArrayList;
import java.util.List;

public interface EnhancedFieldMappingService {
    /**
     * 获取字段映射统计信息
     */
    public EnhancedFieldMappingServiceImpl.FieldMappingStatistics getStatistics(List<EnhancedFieldMapping> mappings) ;
    /**
     * 分析并生成增强的字段映射
     */
    public List<EnhancedFieldMapping> analyzeFieldMappings(TableStructure sourceTable, String businessContext) ;
}
