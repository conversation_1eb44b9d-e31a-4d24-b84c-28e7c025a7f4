package com.liuyang.service;

import com.liuyang.entity.OperationLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务接口
 */
public interface OperationLogService {

    /**
     * 保存操作日志
     * @param operationLog 操作日志对象
     * @return 是否保存成功
     */
    boolean saveLog(OperationLog operationLog);

    /**
     * 异步保存操作日志
     * @param operationLog 操作日志对象
     */
    void saveLogAsync(OperationLog operationLog);

    /**
     * 根据ID查询操作日志
     * @param id 日志ID
     * @return 操作日志对象
     */
    OperationLog getLogById(Long id);

    /**
     * 分页查询操作日志
     * @param userId 用户ID (可选)
     * @param username 用户名 (可选)
     * @param operationType 操作类型 (可选)
     * @param startTime 开始时间 (可选)
     * @param endTime 结束时间 (可选)
     * @param page 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    Map<String, Object> getLogsByPage(Long userId, String username, String operationType,
                                     LocalDateTime startTime, LocalDateTime endTime,
                                     int page, int pageSize);

    /**
     * 获取操作类型统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getOperationTypeStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户操作统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getUserOperationStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取每日操作统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getDailyOperationStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取错误日志统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    Map<String, Object> getErrorStats(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最近的操作日志
     * @param limit 限制数量
     * @return 操作日志列表
     */
    List<OperationLog> getRecentLogs(int limit);

    /**
     * 清理过期日志
     * @param beforeTime 清理此时间之前的日志
     * @return 清理的日志数量
     */
    int cleanExpiredLogs(LocalDateTime beforeTime);

    /**
     * 记录用户登录日志
     * @param username 用户名
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param success 是否成功
     * @param errorMessage 错误信息 (如果失败)
     */
    void logUserLogin(String username, String clientIp, String userAgent, boolean success, String errorMessage);

    /**
     * 记录用户登出日志
     * @param username 用户名
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     */
    void logUserLogout(String username, String clientIp, String userAgent);

    /**
     * 记录数据库迁移日志
     * @param username 用户名
     * @param sourceDb 源数据库
     * @param targetDb 目标数据库
     * @param tableName 表名
     * @param success 是否成功
     * @param executionTime 执行时间
     * @param errorMessage 错误信息 (如果失败)
     */
    void logDatabaseMigration(String username, String sourceDb, String targetDb, String tableName,
                             boolean success, long executionTime, String errorMessage);

    /**
     * 记录配置管理日志
     * @param username 用户名
     * @param operation 操作类型 (CREATE/UPDATE/DELETE)
     * @param configName 配置名称
     * @param configId 配置ID
     * @param success 是否成功
     * @param errorMessage 错误信息 (如果失败)
     */
    void logConfigManagement(String username, String operation, String configName, String configId,
                            boolean success, String errorMessage);
}
