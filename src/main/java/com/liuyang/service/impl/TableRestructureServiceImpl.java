package com.liuyang.service.impl;

import com.liuyang.config.DatabaseConfigForMybatis;
import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableRestructureRequest;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseService;
import com.liuyang.service.IdGeneratorService;
import com.liuyang.service.TableRestructureService;
import com.liuyang.service.TableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表重构服务类
 */
@Service
public class TableRestructureServiceImpl implements TableRestructureService {
    
    private static final Logger logger = LoggerFactory.getLogger(TableRestructureService.class);
    
    @Autowired
    private DatabaseService databaseService;
    
    @Autowired
    private TableService tableService;
    
    @Autowired
    private DatabaseConfigForMybatis databaseConfig;

    @Autowired
    private IdGeneratorService idGeneratorService;
    
    /**
     * 执行表重构
     */
    @Override
    public void restructureTable(TableRestructureRequest request) {
        logger.info("开始表重构: {}", request);
        
        // 验证源数据库和表是否存在
        validateSourceTable(request);
        
        // 验证目标数据库是否存在
        validateTargetDatabase(request);
        
        try {
            // 1. 创建重构后的表结构
            createRestructuredTable(request);
            
            // 2. 复制和转换数据
            if (request.isCopyData()) {
                copyAndTransformData(request);
            }
            
            // 3. 创建索引
            if (request.isCreateIndexes()) {
                createIndexes(request);
            }
            
            logger.info("表重构完成: {} -> {}", request.getSourceTableName(), request.getTargetTableName());
            
        } catch (Exception e) {
            logger.error("表重构失败", e);
            throw new DatabaseException("表重构失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证源表
     */
    private void validateSourceTable(TableRestructureRequest request) {
        if (!databaseService.databaseExists(request.getSourceDatabase())) {
            throw new DatabaseException("源数据库不存在: " + request.getSourceDatabase());
        }
        
        if (!tableService.tableExists(request.getSourceDatabase(), request.getSourceTableName())) {
            throw new DatabaseException("源表不存在: " + request.getSourceTableName());
        }
    }
    
    /**
     * 验证目标数据库
     */
    private void validateTargetDatabase(TableRestructureRequest request) {
        if (!databaseService.databaseExists(request.getTargetDatabase())) {
            throw new DatabaseException("目标数据库不存在: " + request.getTargetDatabase());
        }
    }
    
    /**
     * 创建重构后的表结构
     */
    private void createRestructuredTable(TableRestructureRequest request) {
        try (Connection conn = databaseService.getConnection(request.getTargetDatabase())) {
            
            // 如果需要，先删除已存在的表
            if (request.isDropIfExists()) {
                dropTableIfExists(conn, request.getTargetTableName());
            } else {
                // 如果表已存在且不删除，检查是否有数据冲突
                if (tableExists(conn, request.getTargetTableName())) {
                    logger.warn("目标表 {} 已存在，可能会有数据冲突", request.getTargetTableName());
                }
            }
            
            // 生成创建表的SQL
            String createTableSql = generateRestructuredTableSql(request);
            logger.debug("创建重构表SQL: {}", createTableSql);
            
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createTableSql);
                logger.info("创建重构表成功: {}", request.getTargetTableName());
            }
            
        } catch (SQLException e) {
            logger.error("创建重构表失败", e);
            throw new DatabaseException("创建重构表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除表（如果存在）
     */
    private void dropTableIfExists(Connection conn, String tableName) throws SQLException {
        String dropSql = "DROP TABLE IF EXISTS \"" + tableName + "\"";
        try (Statement stmt = conn.createStatement()) {
            stmt.execute(dropSql);
            logger.info("删除已存在的表: {}", tableName);
        }
    }
    
    /**
     * 生成重构表的创建SQL
     */
    private String generateRestructuredTableSql(TableRestructureRequest request) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE \"").append(request.getTargetTableName()).append("\" (\n");
        
        List<String> columnDefinitions = new ArrayList<>();
        
        // 如果使用双主键设计，先添加新的Long主键
        if (request.isUseDualPrimaryKey()) {
            columnDefinitions.add("    \"" + request.getNewPrimaryKeyField() + "\" int8 NOT NULL");
        }
        
        // 添加字段映射定义
        for (FieldMapping mapping : request.getFieldMappings()) {
            StringBuilder columnDef = new StringBuilder();
            columnDef.append("    \"").append(mapping.getTargetField()).append("\" ");
            
            // 数据类型 - 智能映射
            String dataType = mapPostgreSQLDataTypeWithFieldName(
                mapping.getTargetType(),
                mapping.getTargetSize(),
                mapping.getTargetField()
            );
            columnDef.append(dataType);
            
            // 是否允许NULL
            if (!mapping.isNullable()) {
                columnDef.append(" NOT NULL");
            }
            
            // 默认值
            if (mapping.getDefaultValue() != null && !mapping.getDefaultValue().trim().isEmpty()) {
                columnDef.append(" DEFAULT ").append(mapping.getDefaultValue());
            }
            
            columnDefinitions.add(columnDef.toString());
        }
        
        sql.append(String.join(",\n", columnDefinitions));
        
        // 添加主键约束
        if (request.isUseDualPrimaryKey()) {
            sql.append(",\n    CONSTRAINT \"pk_").append(request.getTargetTableName()).append("\" PRIMARY KEY (\"")
               .append(request.getNewPrimaryKeyField()).append("\")");
            
            // 添加业务ID唯一约束
            sql.append(",\n    CONSTRAINT \"uk_").append(request.getTargetTableName()).append("_")
               .append(request.getBusinessIdField()).append("\" UNIQUE (\"")
               .append(request.getBusinessIdField()).append("\")");
        }
        
        sql.append("\n)");
        
        return sql.toString();
    }
    
    /**
     * 映射PostgreSQL数据类型
     */
    private String mapPostgreSQLDataType(String dataType, Integer size) {
        if (dataType == null) return "text";

        switch (dataType.toLowerCase()) {
            case "varchar":
            case "character varying":
                return size != null ? "varchar(" + size + ")" : "varchar";
            case "char":
            case "character":
                return size != null ? "char(" + size + ")" : "char";
            case "text":
                return "text";
            case "int8":
            case "bigint":
                return "int8";
            case "int4":
            case "integer":
                return "int4";
            case "int2":
            case "smallint":
                return "int2";
            case "decimal":
            case "numeric":
                // 智能处理numeric类型
                return mapNumericType(size);
            case "boolean":
                return "boolean";
            case "timestamp":
                return "timestamp";
            case "date":
                return "date";
            case "time":
                return "time";
            default:
                return dataType;
        }
    }

    /**
     * 智能映射PostgreSQL数据类型（考虑字段名称）
     */
    private String mapPostgreSQLDataTypeWithFieldName(String dataType, Integer size, String fieldName) {
        if (dataType == null) return "text";

        String lowerDataType = dataType.toLowerCase();
        String lowerFieldName = fieldName != null ? fieldName.toLowerCase() : "";

        // 对于numeric类型，根据字段名称智能判断
        if ("numeric".equals(lowerDataType) || "decimal".equals(lowerDataType)) {
            return mapNumericTypeByFieldName(size, lowerFieldName);
        }

        // 其他类型使用原有映射
        return mapPostgreSQLDataType(dataType, size);
    }

    /**
     * 智能映射numeric类型
     * 根据字段大小和字段名称选择合适的数据类型
     */
    private String mapNumericType(Integer size) {
        if (size == null) {
            return "decimal";
        }

        // 对于大数值字段（如ID、区域代码等），使用bigint
        if (size >= 15) {
            return "int8";  // 使用bigint避免精度问题
        }

        // 对于中等数值字段，使用integer
        if (size >= 10) {
            return "int4";
        }

        // 对于小数值字段，使用decimal
        if (size <= 9) {
            return "decimal(" + size + ",2)";
        }

        return "decimal";
    }

    /**
     * 根据字段名称智能映射numeric类型
     */
    private String mapNumericTypeByFieldName(Integer size, String fieldName) {
        // ID类字段 - 使用bigint
        if (fieldName.contains("id") || fieldName.contains("_id") ||
            fieldName.equals("id") || fieldName.endsWith("_id")) {
            return "int8";
        }

        // 区域代码、编码类字段 - 使用bigint
        if (fieldName.contains("area") || fieldName.contains("code") ||
            fieldName.contains("region") || fieldName.contains("zone")) {
            return "int8";
        }

        // 数量、计数类字段 - 使用integer或bigint
        if (fieldName.contains("count") || fieldName.contains("num") ||
            fieldName.contains("quantity") || fieldName.contains("amount")) {
            if (size != null && size >= 15) {
                return "int8";
            } else if (size != null && size >= 10) {
                return "int4";
            } else {
                return "int4";
            }
        }

        // 金额、价格类字段 - 使用decimal
        if (fieldName.contains("price") || fieldName.contains("amount") ||
            fieldName.contains("money") || fieldName.contains("fee") ||
            fieldName.contains("balance") || fieldName.contains("due")) {
            return "decimal(18,2)";
        }

        // 状态、类型字段 - 使用integer
        if (fieldName.contains("status") || fieldName.contains("type") ||
            fieldName.contains("flag") || fieldName.contains("state")) {
            return "int4";
        }

        // 默认根据大小判断
        return mapNumericType(size);
    }

    /**
     * 复制和转换数据
     */
    private void copyAndTransformData(TableRestructureRequest request) {
        logger.info("开始复制和转换数据: {} -> {}", request.getSourceTableName(), request.getTargetTableName());

        try (Connection sourceConn = databaseService.getConnection(request.getSourceDatabase());
             Connection targetConn = databaseService.getConnection(request.getTargetDatabase())) {

            targetConn.setAutoCommit(false);

            try {
                // 获取源表数据总数
                long totalRows = getTotalRows(sourceConn, request.getSourceTableName());
                logger.info("源表总行数: {}", totalRows);

                if (totalRows == 0) {
                    logger.info("源表无数据，跳过数据复制");
                    return;
                }

                // 生成查询和插入SQL
                String selectSql = generateSelectSqlWithMapping(request);
                String insertSql = generateInsertSqlWithMapping(request);

                logger.debug("查询SQL: {}", selectSql);
                logger.debug("插入SQL: {}", insertSql);

                // 分批复制数据
                copyDataInBatches(sourceConn, targetConn, selectSql, insertSql, request, totalRows);

                targetConn.commit();
                logger.info("数据复制完成，共复制 {} 行", totalRows);

            } catch (SQLException e) {
                targetConn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            logger.error("复制数据失败", e);
            throw new DatabaseException("复制数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取表总行数
     */
    private long getTotalRows(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM \"" + tableName + "\"";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }

    /**
     * 生成带字段映射的查询SQL
     */
    private String generateSelectSqlWithMapping(TableRestructureRequest request) {
        List<String> sourceFields = request.getFieldMappings().stream()
                .map(FieldMapping::getSourceField)
                .collect(Collectors.toList());

        return "SELECT \"" + String.join("\", \"", sourceFields) + "\" FROM \"" + request.getSourceTableName() + "\"";
    }

    /**
     * 生成带字段映射的插入SQL
     */
    private String generateInsertSqlWithMapping(TableRestructureRequest request) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO \"").append(request.getTargetTableName()).append("\" (");

        List<String> targetFields = new ArrayList<>();

        // 如果使用双主键，添加新主键字段
        if (request.isUseDualPrimaryKey()) {
            targetFields.add("\"" + request.getNewPrimaryKeyField() + "\"");
        }

        // 添加映射字段
        targetFields.addAll(request.getFieldMappings().stream()
                .map(mapping -> "\"" + mapping.getTargetField() + "\"")
                .collect(Collectors.toList()));

        sql.append(String.join(", ", targetFields));
        sql.append(") VALUES (");

        // 生成占位符
        List<String> placeholders = new ArrayList<>();
        for (int i = 0; i < targetFields.size(); i++) {
            placeholders.add("?");
        }
        sql.append(String.join(", ", placeholders));
        sql.append(")");

        return sql.toString();
    }

    /**
     * 分批复制数据
     */
    private void copyDataInBatches(Connection sourceConn, Connection targetConn,
                                   String selectSql, String insertSql,
                                   TableRestructureRequest request, long totalRows) throws SQLException {

        int batchSize = databaseConfig.getBatchSize();
        long copiedRows = 0;
        int offset = 0;

        while (copiedRows < totalRows) {
            String pagedSelectSql = selectSql + " LIMIT " + batchSize + " OFFSET " + offset;

            try (PreparedStatement selectStmt = sourceConn.prepareStatement(pagedSelectSql);
                 PreparedStatement insertStmt = targetConn.prepareStatement(insertSql);
                 ResultSet rs = selectStmt.executeQuery()) {

                int batchCount = 0;
                while (rs.next()) {
                    int paramIndex = 1;

                    // 如果使用双主键，先设置新主键值（使用雪花算法生成）
                    if (request.isUseDualPrimaryKey()) {
                        long newId = generateSnowflakeId();
                        insertStmt.setLong(paramIndex++, newId);
                        logger.debug("为记录生成新ID: {}", newId);
                    }

                    // 设置映射字段的值
                    for (int i = 0; i < request.getFieldMappings().size(); i++) {
                        Object value = rs.getObject(i + 1);
                        insertStmt.setObject(paramIndex++, value);
                    }

                    insertStmt.addBatch();
                    batchCount++;
                }

                if (batchCount > 0) {
                    try {
                        insertStmt.executeBatch();
                        copiedRows += batchCount;
                        logger.info("已复制 {} / {} 行数据", copiedRows, totalRows);
                    } catch (SQLException e) {
                        // 如果是重复键错误，尝试逐条插入
                        if (e.getMessage().contains("duplicate key")) {
                            logger.warn("批量插入遇到重复键，尝试逐条插入跳过重复数据");
                            copiedRows += handleDuplicateKeyError(sourceConn, targetConn, selectSql, insertSql, request, offset, batchSize);
                        } else {
                            throw e;
                        }
                    }
                }

                offset += batchSize;

                if (batchCount < batchSize) {
                    break;
                }
            }
        }
    }

    /**
     * 生成雪花算法ID
     */
    private long generateSnowflakeId() {
        return idGeneratorService.generateId();
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT 1 FROM information_schema.tables WHERE table_name = ? AND table_type = 'BASE TABLE'";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    /**
     * 处理重复键错误，逐条插入数据
     */
    private int handleDuplicateKeyError(Connection sourceConn, Connection targetConn,
                                       String selectSql, String insertSql,
                                       TableRestructureRequest request, int offset, int batchSize) throws SQLException {

        String pagedSelectSql = selectSql + " LIMIT " + batchSize + " OFFSET " + offset;
        int successCount = 0;

        try (PreparedStatement selectStmt = sourceConn.prepareStatement(pagedSelectSql);
             PreparedStatement insertStmt = targetConn.prepareStatement(insertSql);
             ResultSet rs = selectStmt.executeQuery()) {

            while (rs.next()) {
                try {
                    int paramIndex = 1;

                    // 如果使用双主键，先设置新主键值
                    if (request.isUseDualPrimaryKey()) {
                        long newId = generateSnowflakeId();
                        insertStmt.setLong(paramIndex++, newId);
                    }

                    // 设置映射字段的值
                    for (int i = 0; i < request.getFieldMappings().size(); i++) {
                        Object value = rs.getObject(i + 1);
                        insertStmt.setObject(paramIndex++, value);
                    }

                    insertStmt.executeUpdate();
                    successCount++;

                } catch (SQLException e) {
                    if (e.getMessage().contains("duplicate key")) {
                        logger.debug("跳过重复数据");
                    } else {
                        logger.warn("插入数据失败: {}", e.getMessage());
                    }
                }
            }
        }

        logger.info("逐条插入完成，成功插入 {} 行", successCount);
        return successCount;
    }

    /**
     * 创建索引
     */
    private void createIndexes(TableRestructureRequest request) {
        if (request.getIndexFields() == null || request.getIndexFields().isEmpty()) {
            return;
        }

        logger.info("开始创建索引");

        try (Connection conn = databaseService.getConnection(request.getTargetDatabase())) {

            for (String field : request.getIndexFields()) {
                String indexName = "idx_" + request.getTargetTableName() + "_" + field;
                String indexSql = "CREATE INDEX \"" + indexName + "\" ON \"" +
                                request.getTargetTableName() + "\" (\"" + field + "\")";

                try (Statement stmt = conn.createStatement()) {
                    stmt.execute(indexSql);
                    logger.info("创建索引成功: {}", indexName);
                } catch (SQLException e) {
                    logger.warn("创建索引失败: {} - {}", indexName, e.getMessage());
                }
            }

        } catch (SQLException e) {
            logger.error("创建索引失败", e);
            throw new DatabaseException("创建索引失败: " + e.getMessage(), e);
        }
    }
}
