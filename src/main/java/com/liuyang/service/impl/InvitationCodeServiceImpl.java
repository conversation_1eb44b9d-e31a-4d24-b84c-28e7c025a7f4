package com.liuyang.service.impl;

import com.liuyang.entity.InvitationCode;
import com.liuyang.mapper.InvitationCodeMapper;
import com.liuyang.service.InvitationCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 邀请码服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvitationCodeServiceImpl implements InvitationCodeService {
    
    private final InvitationCodeMapper invitationCodeMapper;
    private final BCryptPasswordEncoder passwordEncoder;
    
    /**
     * 应用启动后初始化默认邀请码
     */
    @PostConstruct
    public void init() {
        initializeDefaultInvitationCodes();
    }
    
    @Override
    public boolean validateInvitationCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            log.warn("邀请码验证失败：邀请码为空");
            return false;
        }
        
        try {
            // 获取所有激活的邀请码
            List<InvitationCode> activeCodes = invitationCodeMapper.selectAllActive();
            
            // 遍历所有激活的邀请码，使用BCrypt验证
            for (InvitationCode invitationCode : activeCodes) {
                if (passwordEncoder.matches(code.trim(), invitationCode.getCodeHash())) {
                    // 检查是否可用（未超过使用限制）
                    if (invitationCode.isAvailable()) {
                        log.info("邀请码验证成功：{}", code);
                        return true;
                    } else {
                        log.warn("邀请码已达到使用限制：{}", code);
                        return false;
                    }
                }
            }
            
            log.warn("邀请码验证失败：无效的邀请码 {}", code);
            return false;
            
        } catch (Exception e) {
            log.error("邀请码验证异常：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean useInvitationCode(String code, String username) {
        if (code == null || code.trim().isEmpty()) {
            log.warn("使用邀请码失败：邀请码为空");
            return false;
        }
        
        try {
            // 获取所有激活的邀请码
            List<InvitationCode> activeCodes = invitationCodeMapper.selectAllActive();
            
            // 遍历所有激活的邀请码，使用BCrypt验证
            for (InvitationCode invitationCode : activeCodes) {
                if (passwordEncoder.matches(code.trim(), invitationCode.getCodeHash())) {
                    // 检查是否可用
                    if (invitationCode.isAvailable()) {
                        // 增加使用次数
                        int result = invitationCodeMapper.incrementUsageCount(invitationCode.getId());
                        if (result > 0) {
                            log.info("邀请码使用成功：{} 被用户 {} 使用", code, username);
                            return true;
                        } else {
                            log.error("邀请码使用失败：数据库更新失败");
                            return false;
                        }
                    } else {
                        log.warn("邀请码使用失败：邀请码已达到使用限制 {}", code);
                        return false;
                    }
                }
            }
            
            log.warn("邀请码使用失败：无效的邀请码 {}", code);
            return false;
            
        } catch (Exception e) {
            log.error("邀请码使用异常：{}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public InvitationCode createInvitationCode(String code, String description, Integer maxUsage, String createdBy) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("邀请码不能为空");
        }
        
        try {
            // 加密邀请码
            String codeHash = passwordEncoder.encode(code.trim());
            
            // 创建邀请码对象
            InvitationCode invitationCode = InvitationCode.builder()
                    .codeHash(codeHash)
                    .description(description)
                    .isActive(1)
                    .usageCount(0)
                    .maxUsage(maxUsage != null ? maxUsage : -1)
                    .createdBy(createdBy)
                    .build();
            
            // 插入数据库
            int result = invitationCodeMapper.insert(invitationCode);
            if (result > 0) {
                log.info("邀请码创建成功：{} (ID: {})", description, invitationCode.getId());
                return invitationCode;
            } else {
                throw new RuntimeException("邀请码创建失败：数据库插入失败");
            }
            
        } catch (Exception e) {
            log.error("邀请码创建异常：{}", e.getMessage(), e);
            throw new RuntimeException("邀请码创建失败：" + e.getMessage());
        }
    }

    @Override
    public InvitationCode getById(Long id) {
        return invitationCodeMapper.selectById(id);
    }

    @Override
    public List<InvitationCode> getAllActive() {
        return invitationCodeMapper.selectAllActive();
    }

    @Override
    public List<InvitationCode> getAll() {
        return invitationCodeMapper.selectAll();
    }

    @Override
    public List<InvitationCode> getAllAvailable() {
        return invitationCodeMapper.selectAvailable();
    }

    @Override
    @Transactional
    public boolean updateInvitationCode(InvitationCode invitationCode) {
        try {
            int result = invitationCodeMapper.update(invitationCode);
            if (result > 0) {
                log.info("邀请码更新成功：ID {}", invitationCode.getId());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("邀请码更新异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean toggleInvitationCode(Long id, boolean active, String updatedBy) {
        try {
            InvitationCode invitationCode = invitationCodeMapper.selectById(id);
            if (invitationCode == null) {
                log.warn("邀请码切换状态失败：邀请码不存在 ID {}", id);
                return false;
            }

            invitationCode.setIsActive(active ? 1 : 0);
            invitationCode.setUpdatedBy(updatedBy);

            int result = invitationCodeMapper.update(invitationCode);
            if (result > 0) {
                log.info("邀请码状态切换成功：ID {} -> {}", id, active ? "激活" : "禁用");
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("邀请码状态切换异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteInvitationCode(Long id) {
        try {
            int result = invitationCodeMapper.deleteById(id);
            if (result > 0) {
                log.info("邀请码删除成功：ID {}", id);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("邀请码删除异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }

        try {
            // 获取所有邀请码，检查是否有匹配的
            List<InvitationCode> allCodes = invitationCodeMapper.selectAll();
            for (InvitationCode invitationCode : allCodes) {
                if (passwordEncoder.matches(code.trim(), invitationCode.getCodeHash())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("检查邀请码存在性异常：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public void initializeDefaultInvitationCodes() {
        try {
            // 检查是否已存在 "healthcar" 邀请码
            if (!existsByCode("healthcar")) {
                createInvitationCode("healthcar", "默认邀请码 - healthcar", -1, "system");
                log.info("默认邀请码初始化完成：healthcar");
            } else {
                log.info("默认邀请码已存在，跳过初始化");
            }
        } catch (Exception e) {
            log.error("默认邀请码初始化失败：{}", e.getMessage(), e);
        }
    }
}
