package com.liuyang.service.impl;

import com.liuyang.service.IdGeneratorService;
import com.liuyang.util.SnowflakeIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ID生成器服务
 */
@Service
public class IdGeneratorServiceImpl implements IdGeneratorService {

    private static final Logger logger = LoggerFactory.getLogger(IdGeneratorService.class);

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    /**
     * 生成雪花算法ID
     */
    public long generateId() {
        long id = snowflakeIdGenerator.nextId();
        logger.debug("生成雪花算法ID: {}", id);
        return id;
    }
    
    /**
     * 生成字符串格式的ID
     */
    public String generateIdAsString() {
        return String.valueOf(generateId());
    }
    
    /**
     * 批量生成ID
     */
    public long[] generateIds(int count) {
        long[] ids = new long[count];
        for (int i = 0; i < count; i++) {
            ids[i] = generateId();
        }
        return ids;
    }
}
