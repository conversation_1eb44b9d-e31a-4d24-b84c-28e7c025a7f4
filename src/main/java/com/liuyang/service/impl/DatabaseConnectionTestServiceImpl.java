package com.liuyang.service.impl;

import com.liuyang.dto.DatabaseType;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.service.DatabaseConfigManagementService;
import com.liuyang.service.DatabaseConnectionTestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 数据库连接测试服务实现
 */
@Service
public class DatabaseConnectionTestServiceImpl implements DatabaseConnectionTestService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConnectionTestServiceImpl.class);
    
    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;
    
    /**
     * 测试数据库连接
     */
    @Override
    public Map<String, Object> testConnection(DatabaseType dbType, String databaseName) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("🔍 开始测试数据库连接: {} - {}", dbType, databaseName);
            
            // 1. 获取配置
            DatabaseConfigEntity config = getConfig(dbType);
            String originalUrl = config.getJdbcUrl(databaseName);
            
            // 2. 添加超时参数
            String optimizedUrl = addTimeoutParameters(originalUrl, dbType);
            
            result.put("originalUrl", originalUrl.replaceAll("password=[^&]*", "password=***"));
            result.put("optimizedUrl", optimizedUrl.replaceAll("password=[^&]*", "password=***"));
            
            // 3. 测试连接
            Connection conn = testConnectionWithTimeout(optimizedUrl, config.getUsername(), config.getPassword());
            
            if (conn != null) {
                // 4. 测试基本操作
                testBasicOperations(conn, dbType);
                
                conn.close();
                
                long duration = System.currentTimeMillis() - startTime;
                result.put("success", true);
                result.put("message", "连接测试成功");
                result.put("duration", duration);
                
                logger.info("✅ 数据库连接测试成功: {} (耗时: {}ms)", dbType, duration);
            } else {
                result.put("success", false);
                result.put("message", "连接失败");
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            result.put("success", false);
            result.put("message", "连接失败: " + e.getMessage());
            result.put("duration", duration);
            result.put("error", e.getClass().getSimpleName());
            
            logger.error("❌ 数据库连接测试失败: {} (耗时: {}ms)", dbType, duration, e);
        }
        
        return result;
    }
    
    /**
     * 获取数据库配置
     */
    private DatabaseConfigEntity getConfig(DatabaseType dbType) throws Exception {
        if (dbType == DatabaseType.POSTGRESQL) {
            return databaseConfigManagementService.getSourceDatabaseConfig();
        } else if (dbType == DatabaseType.MYSQL) {
            return databaseConfigManagementService.getTargetDatabaseConfig();
        } else {
            throw new SQLException("不支持的数据库类型: " + dbType);
        }
    }
    
    /**
     * 添加超时参数
     */
    private String addTimeoutParameters(String url, DatabaseType dbType) {
        if (dbType == DatabaseType.MYSQL) {
            if (url.contains("?")) {
                return url + "&connectTimeout=120000&socketTimeout=300000&autoReconnect=true&failOverReadOnly=false&maxReconnects=3&useSSL=false";
            } else {
                return url + "?connectTimeout=120000&socketTimeout=300000&autoReconnect=true&failOverReadOnly=false&maxReconnects=3&useSSL=false";
            }
        } else if (dbType == DatabaseType.POSTGRESQL) {
            if (url.contains("?")) {
                return url + "&connectTimeout=120&socketTimeout=300&loginTimeout=120";
            } else {
                return url + "?connectTimeout=120&socketTimeout=300&loginTimeout=120";
            }
        }
        return url;
    }
    
    /**
     * 带超时的连接测试
     */
    private Connection testConnectionWithTimeout(String url, String username, String password) throws Exception {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        
        try {
            // 设置DriverManager超时
            DriverManager.setLoginTimeout(120);
            
            Future<Connection> future = executor.submit(() -> {
                try {
                    logger.info("🔗 尝试建立连接...");
                    return DriverManager.getConnection(url, username, password);
                } catch (SQLException e) {
                    logger.error("连接异常: {}", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
            
            // 等待最多2分钟
            return future.get(120, TimeUnit.SECONDS);
            
        } catch (TimeoutException e) {
            logger.error("⏰ 连接超时 (120秒)");
            throw new SQLException("连接超时", e);
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 测试基本数据库操作
     */
    private void testBasicOperations(Connection conn, DatabaseType dbType) throws SQLException {
        logger.info("🧪 测试基本数据库操作...");
        
        try (Statement stmt = conn.createStatement()) {
            // 测试查询
            if (dbType == DatabaseType.MYSQL) {
                try (ResultSet rs = stmt.executeQuery("SELECT 1 as test_value")) {
                    if (rs.next()) {
                        logger.info("✅ MySQL查询测试成功: {}", rs.getInt("test_value"));
                    }
                }
                
                // 测试MySQL特定设置
                stmt.execute("SET SESSION innodb_lock_wait_timeout = 300");
                stmt.execute("SET SESSION wait_timeout = 3600");
                logger.info("✅ MySQL会话参数设置成功");
                
            } else if (dbType == DatabaseType.POSTGRESQL) {
                try (ResultSet rs = stmt.executeQuery("SELECT 1 as test_value")) {
                    if (rs.next()) {
                        logger.info("✅ PostgreSQL查询测试成功: {}", rs.getInt("test_value"));
                    }
                }
            }
        }
    }
    
    /**
     * 批量测试所有数据库连接
     */
    @Override
    public Map<String, Object> testAllConnections() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试源数据库 (PostgreSQL)
            Map<String, Object> sourceResult = testConnection(DatabaseType.POSTGRESQL, "qxrtoc_fee");
            result.put("source", sourceResult);
            
            // 测试目标数据库 (MySQL)
            Map<String, Object> targetResult = testConnection(DatabaseType.MYSQL, "health_car");
            result.put("target", targetResult);
            
            boolean allSuccess = (Boolean) sourceResult.get("success") && (Boolean) targetResult.get("success");
            result.put("allSuccess", allSuccess);
            
            if (allSuccess) {
                result.put("message", "所有数据库连接测试成功");
            } else {
                result.put("message", "部分数据库连接测试失败");
            }
            
        } catch (Exception e) {
            result.put("allSuccess", false);
            result.put("message", "连接测试异常: " + e.getMessage());
            logger.error("批量连接测试失败", e);
        }
        
        return result;
    }
}
