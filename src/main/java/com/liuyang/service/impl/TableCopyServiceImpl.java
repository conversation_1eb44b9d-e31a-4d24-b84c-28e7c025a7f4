package com.liuyang.service.impl;

import com.liuyang.config.DatabaseConfigForMybatis;
import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.CopyTableRequest;
import com.liuyang.dto.TableStructure;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseService;
import com.liuyang.service.TableCopyService;
import com.liuyang.service.TableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 表复制服务类
 */
@Service
public class TableCopyServiceImpl implements TableCopyService {
    
    private static final Logger logger = LoggerFactory.getLogger(TableCopyService.class);
    
    @Autowired
    private DatabaseService databaseService;
    
    @Autowired
    private TableService tableService;
    
    @Autowired
    private DatabaseConfigForMybatis databaseConfig;
    
    /**
     * 复制表（表结构和数据）
     */
    public void copyTable(CopyTableRequest request) {
        logger.info("开始复制表: {}", request);
        
        // 验证源数据库和表是否存在
        if (!databaseService.databaseExists(request.getSourceDatabase())) {
            throw new DatabaseException("源数据库不存在: " + request.getSourceDatabase());
        }
        
        if (!tableService.tableExists(request.getSourceDatabase(), request.getTableName())) {
            throw new DatabaseException("源表不存在: " + request.getTableName());
        }
        
        // 验证目标数据库是否存在
        if (!databaseService.databaseExists(request.getTargetDatabase())) {
            throw new DatabaseException("目标数据库不存在: " + request.getTargetDatabase());
        }
        
        try {
            // 1. 获取源表结构
            TableStructure structure = tableService.getTableStructure(
                request.getSourceDatabase(), 
                request.getTableName()
            );
            
            // 2. 在目标数据库创建表
            createTable(request.getTargetDatabase(), request.getTargetTableName(), structure, request.isDropIfExists());
            
            // 3. 复制数据（如果需要）
            if (request.isCopyData()) {
                copyTableData(request, structure);
            }
            
            logger.info("表复制完成: {} -> {}", request.getTableName(), request.getTargetTableName());
            
        } catch (Exception e) {
            logger.error("复制表失败", e);
            throw new DatabaseException("复制表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 在目标数据库创建表
     */
    private void createTable(String targetDatabase, String targetTableName, TableStructure structure, boolean dropIfExists) {
        try (Connection conn = databaseService.getConnection(targetDatabase)) {
            
            // 如果需要，先删除已存在的表
            if (dropIfExists) {
                String dropSql = "DROP TABLE IF EXISTS " + targetTableName;
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute(dropSql);
                    logger.info("删除已存在的表: {}", targetTableName);
                }
            }
            
            // 生成创建表的SQL
            String createTableSql = generateCreateTableSql(targetTableName, structure);
            logger.debug("创建表SQL: {}", createTableSql);
            
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createTableSql);
                logger.info("创建表成功: {}", targetTableName);
            }
            
        } catch (SQLException e) {
            logger.error("创建表失败", e);
            throw new DatabaseException("创建表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成创建表的SQL语句
     */
    private String generateCreateTableSql(String tableName, TableStructure structure) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        List<String> columnDefinitions = new ArrayList<>();
        
        // 添加列定义
        for (ColumnInfo column : structure.getColumns()) {
            StringBuilder columnDef = new StringBuilder();
            columnDef.append("    ").append(column.getColumnName()).append(" ");
            
            // 数据类型
            String dataType = mapDataType(column.getDataType(), column.getColumnSize(), column.getDecimalDigits());
            columnDef.append(dataType);
            
            // 是否允许NULL
            if (!column.isNullable()) {
                columnDef.append(" NOT NULL");
            }
            
            // 默认值
            if (column.getDefaultValue() != null && !column.getDefaultValue().trim().isEmpty()) {
                columnDef.append(" DEFAULT ").append(column.getDefaultValue());
            }
            
            columnDefinitions.add(columnDef.toString());
        }
        
        sql.append(String.join(",\n", columnDefinitions));
        
        // 添加主键约束
        if (structure.getPrimaryKeys() != null && !structure.getPrimaryKeys().isEmpty()) {
            sql.append(",\n    PRIMARY KEY (");
            sql.append(String.join(", ", structure.getPrimaryKeys()));
            sql.append(")");
        }
        
        sql.append("\n)");
        
        // 添加表注释
        if (structure.getTableComment() != null && !structure.getTableComment().trim().isEmpty()) {
            // PostgreSQL中表注释需要单独的COMMENT语句
        }
        
        return sql.toString();
    }
    
    /**
     * 映射数据类型
     */
    private String mapDataType(String dataType, Integer columnSize, Integer decimalDigits) {
        switch (dataType.toLowerCase()) {
            case "character varying":
            case "varchar":
                return columnSize != null ? "VARCHAR(" + columnSize + ")" : "VARCHAR";
            case "character":
            case "char":
                return columnSize != null ? "CHAR(" + columnSize + ")" : "CHAR";
            case "numeric":
                if (columnSize != null && columnSize > 0 && decimalDigits != null && decimalDigits >= 0) {
                    return "NUMERIC(" + columnSize + "," + decimalDigits + ")";
                } else if (columnSize != null && columnSize > 0) {
                    return "NUMERIC(" + columnSize + ")";
                }
                return "NUMERIC";
            case "integer":
                return "INTEGER";
            case "bigint":
                return "BIGINT";
            case "smallint":
                return "SMALLINT";
            case "boolean":
                return "BOOLEAN";
            case "timestamp without time zone":
                return "TIMESTAMP";
            case "timestamp with time zone":
                return "TIMESTAMPTZ";
            case "date":
                return "DATE";
            case "time without time zone":
                return "TIME";
            case "text":
                return "TEXT";
            default:
                return dataType.toUpperCase();
        }
    }

    /**
     * 复制表数据
     */
    private void copyTableData(CopyTableRequest request, TableStructure structure) {
        logger.info("开始复制表数据: {} -> {}", request.getTableName(), request.getTargetTableName());

        try (Connection sourceConn = databaseService.getConnection(request.getSourceDatabase());
             Connection targetConn = databaseService.getConnection(request.getTargetDatabase())) {

            // 设置自动提交为false，使用事务
            targetConn.setAutoCommit(false);

            try {
                // 获取源表数据总数
                long totalRows = getTotalRows(sourceConn, request.getTableName());
                logger.info("源表总行数: {}", totalRows);

                if (totalRows == 0) {
                    logger.info("源表无数据，跳过数据复制");
                    return;
                }

                // 生成列名列表
                List<String> columnNames = new ArrayList<>();
                for (ColumnInfo column : structure.getColumns()) {
                    columnNames.add(column.getColumnName());
                }

                // 生成查询和插入SQL
                String selectSql = generateSelectSql(request.getTableName(), columnNames);
                String insertSql = generateInsertSql(request.getTargetTableName(), columnNames);

                logger.debug("查询SQL: {}", selectSql);
                logger.debug("插入SQL: {}", insertSql);

                // 分批复制数据
                int batchSize = databaseConfig.getBatchSize();
                long copiedRows = 0;
                int offset = 0;

                while (copiedRows < totalRows) {
                    String pagedSelectSql = selectSql + " LIMIT " + batchSize + " OFFSET " + offset;

                    try (PreparedStatement selectStmt = sourceConn.prepareStatement(pagedSelectSql);
                         PreparedStatement insertStmt = targetConn.prepareStatement(insertSql);
                         ResultSet rs = selectStmt.executeQuery()) {

                        int batchCount = 0;
                        while (rs.next()) {
                            // 设置插入参数
                            for (int i = 1; i <= columnNames.size(); i++) {
                                insertStmt.setObject(i, rs.getObject(i));
                            }
                            insertStmt.addBatch();
                            batchCount++;
                        }

                        if (batchCount > 0) {
                            insertStmt.executeBatch();
                            copiedRows += batchCount;
                            logger.info("已复制 {} / {} 行数据", copiedRows, totalRows);
                        }

                        offset += batchSize;

                        // 如果这批数据少于batchSize，说明已经是最后一批
                        if (batchCount < batchSize) {
                            break;
                        }
                    }
                }

                targetConn.commit();
                logger.info("数据复制完成，共复制 {} 行", copiedRows);

            } catch (SQLException e) {
                targetConn.rollback();
                throw e;
            }

        } catch (SQLException e) {
            logger.error("复制表数据失败", e);
            throw new DatabaseException("复制表数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取表总行数
     */
    private long getTotalRows(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }

    /**
     * 生成查询SQL
     */
    private String generateSelectSql(String tableName, List<String> columnNames) {
        return "SELECT " + String.join(", ", columnNames) + " FROM " + tableName;
    }

    /**
     * 生成插入SQL
     */
    private String generateInsertSql(String tableName, List<String> columnNames) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        sql.append(String.join(", ", columnNames));
        sql.append(") VALUES (");

        List<String> placeholders = new ArrayList<>();
        for (int i = 0; i < columnNames.size(); i++) {
            placeholders.add("?");
        }
        sql.append(String.join(", ", placeholders));
        sql.append(")");

        return sql.toString();
    }
}
