package com.liuyang.service.impl;

import com.liuyang.dto.DatabaseConnectionConfig;
import com.liuyang.dto.DatabaseInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DynamicDatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 动态数据库连接服务
 * 支持根据配置信息动态连接不同的数据库
 */
@Service
public class DynamicDatabaseServiceImpl implements DynamicDatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DynamicDatabaseService.class);
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection(DatabaseConnectionConfig config) {
        try {
            // 使用系统数据库进行连接测试
            String systemDb = getSystemDatabaseName(config.getDatabaseType());
            String testUrl = config.getJdbcUrl(systemDb);
            
            logger.debug("测试数据库连接: {}", testUrl);
            
            try (Connection conn = DriverManager.getConnection(
                testUrl, config.getUsername(), config.getPassword())) {
                return conn.isValid(5); // 5秒超时
            }
        } catch (SQLException e) {
            logger.warn("数据库连接测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取数据库列表
     */
    public List<DatabaseInfo> getDatabaseList(DatabaseConnectionConfig config) {
        List<DatabaseInfo> databases = new ArrayList<>();
        
        try {
            String systemDb = getSystemDatabaseName(config.getDatabaseType());
            String url = config.getJdbcUrl(systemDb);
            
            logger.debug("连接数据库获取列表: {}", url);
            
            try (Connection conn = DriverManager.getConnection(
                url, config.getUsername(), config.getPassword())) {
                
                if (config.getDatabaseType() == DatabaseType.MYSQL) {
                    databases = getMySQLDatabases(conn);
                } else if (config.getDatabaseType() == DatabaseType.POSTGRESQL) {
                    databases = getPostgreSQLDatabases(conn);
                }
            }
        } catch (SQLException e) {
            logger.error("获取数据库列表失败", e);
            throw new DatabaseException("获取数据库列表失败: " + e.getMessage(), e);
        }
        
        return databases;
    }
    
    /**
     * 获取MySQL数据库列表
     */
    private List<DatabaseInfo> getMySQLDatabases(Connection conn) throws SQLException {
        List<DatabaseInfo> databases = new ArrayList<>();
        
        String sql = "SHOW DATABASES";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String dbName = rs.getString(1);
                
                // 过滤系统数据库
                if (!isSystemDatabase(dbName, DatabaseType.MYSQL)) {
                    DatabaseInfo dbInfo = new DatabaseInfo();
                    dbInfo.setName(dbName);
                    dbInfo.setDatabaseType(DatabaseType.MYSQL);
                    dbInfo.setDescription("MySQL数据库");
                    dbInfo.setAccessible(true);
                    databases.add(dbInfo);
                }
            }
        }
        
        return databases;
    }
    
    /**
     * 获取PostgreSQL数据库列表
     */
    private List<DatabaseInfo> getPostgreSQLDatabases(Connection conn) throws SQLException {
        List<DatabaseInfo> databases = new ArrayList<>();
        
        String sql = "SELECT datname FROM pg_database WHERE datistemplate = false";
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String dbName = rs.getString("datname");
                
                // 过滤系统数据库
                if (!isSystemDatabase(dbName, DatabaseType.POSTGRESQL)) {
                    DatabaseInfo dbInfo = new DatabaseInfo();
                    dbInfo.setName(dbName);
                    dbInfo.setDatabaseType(DatabaseType.POSTGRESQL);
                    dbInfo.setDescription("PostgreSQL数据库");
                    dbInfo.setAccessible(true);
                    databases.add(dbInfo);
                }
            }
        }
        
        return databases;
    }
    
    /**
     * 获取系统数据库名称
     */
    private String getSystemDatabaseName(DatabaseType dbType) {
        switch (dbType) {
            case MYSQL:
                return "information_schema";
            case POSTGRESQL:
                return "postgres";
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + dbType);
        }
    }
    
    /**
     * 判断是否为系统数据库
     */
    private boolean isSystemDatabase(String dbName, DatabaseType dbType) {
        switch (dbType) {
            case MYSQL:
                return "information_schema".equals(dbName) || 
                       "performance_schema".equals(dbName) || 
                       "mysql".equals(dbName) || 
                       "sys".equals(dbName);
            case POSTGRESQL:
                return "template0".equals(dbName) || 
                       "template1".equals(dbName);
            default:
                return false;
        }
    }
    
    /**
     * 获取数据库连接
     */
    public Connection getConnection(DatabaseConnectionConfig config, String databaseName) throws SQLException {
        String url = config.getJdbcUrl(databaseName);
        logger.debug("创建数据库连接: {}", url);
        return DriverManager.getConnection(url, config.getUsername(), config.getPassword());
    }
    
    /**
     * 获取表列表（使用动态连接）
     */
    public List<String> getTableList(DatabaseConnectionConfig config, String databaseName) {
        List<String> tables = new ArrayList<>();

        try (Connection conn = getConnection(config, databaseName)) {
            if (config.getDatabaseType() == DatabaseType.POSTGRESQL) {
                // PostgreSQL使用SQL查询，只获取public schema的表
                String sql = "SELECT table_name FROM information_schema.tables " +
                           "WHERE table_schema = 'public' AND table_type = 'BASE TABLE' " +
                           "ORDER BY table_name";

                logger.info("执行PostgreSQL表查询SQL: {}", sql);
                logger.info("查询数据库: {}", databaseName);

                try (PreparedStatement stmt = conn.prepareStatement(sql);
                     ResultSet rs = stmt.executeQuery()) {

                    int totalCount = 0;
                    int filteredCount = 0;
                    while (rs.next()) {
                        String tableName = rs.getString("table_name");
                        totalCount++;

                        // 过滤带数字后缀的表名（如 table_0, table_1 等）
                        if (tableName.matches(".*_\\d+$")) {
                            logger.debug("过滤掉分区表: {}", tableName);
                            filteredCount++;
                            continue;
                        }

                        tables.add(tableName);
                        logger.debug("找到表: {}", tableName);
                    }
                    logger.info("PostgreSQL数据库 {} 中总共 {} 个表，过滤掉 {} 个分区表，返回 {} 个主表",
                               databaseName, totalCount, filteredCount, tables.size());
                }
            } else {
                // MySQL等其他数据库使用DatabaseMetaData
                logger.info("使用DatabaseMetaData查询MySQL表列表");
                DatabaseMetaData metaData = conn.getMetaData();

                try (ResultSet rs = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                    int count = 0;
                    while (rs.next()) {
                        String tableName = rs.getString("TABLE_NAME");
                        tables.add(tableName);
                        count++;
                        logger.debug("找到表: {}", tableName);
                    }
                    logger.info("MySQL数据库 {} 中找到 {} 个表", databaseName, count);
                }
            }
        } catch (SQLException e) {
            logger.error("获取表列表失败", e);
            throw new DatabaseException("获取表列表失败: " + e.getMessage(), e);
        }

        return tables;
    }
} 