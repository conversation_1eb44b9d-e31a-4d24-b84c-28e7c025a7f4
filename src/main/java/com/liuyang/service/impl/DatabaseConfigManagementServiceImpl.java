package com.liuyang.service.impl;

import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.mapper.DatabaseConfigMapper;
import com.liuyang.service.DatabaseConfigManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库配置管理服务
 */
@Service
@Transactional
public class DatabaseConfigManagementServiceImpl implements DatabaseConfigManagementService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfigManagementService.class);

    @Autowired
    private DatabaseConfigMapper databaseConfigMapper;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    /**
     * 获取当前环境的源数据库配置
     */
    public DatabaseConfigEntity getSourceDatabaseConfig() {
        return getSourceDatabaseConfig(activeProfile);
    }

    /**
     * 获取指定环境的源数据库配置
     */
    public DatabaseConfigEntity getSourceDatabaseConfig(String environment) {
        logger.debug("获取{}环境的源数据库配置", environment);
        
        DatabaseConfigEntity config = databaseConfigMapper.findDefaultByConfigType(
            DatabaseConfigEntity.ConfigType.SOURCE, environment);
        
        if (config == null) {
            logger.warn("未找到{}环境的源数据库配置，尝试获取默认配置", environment);
            config = databaseConfigMapper.findDefaultByConfigType(
                DatabaseConfigEntity.ConfigType.SOURCE, "default");
        }
        
        if (config == null) {
            throw new RuntimeException("未找到可用的源数据库配置");
        }
        
        logger.debug("获取到源数据库配置: {}", config.getConfigName());
        return config;
    }

    /**
     * 获取当前环境的目标数据库配置
     */
    public DatabaseConfigEntity getTargetDatabaseConfig() {
        return getTargetDatabaseConfig(activeProfile);
    }

    /**
     * 获取指定环境的目标数据库配置
     */
    public DatabaseConfigEntity getTargetDatabaseConfig(String environment) {
        logger.debug("获取{}环境的目标数据库配置", environment);
        
        DatabaseConfigEntity config = databaseConfigMapper.findDefaultByConfigType(
            DatabaseConfigEntity.ConfigType.TARGET, environment);
        
        if (config == null) {
            logger.warn("未找到{}环境的目标数据库配置，尝试获取默认配置", environment);
            config = databaseConfigMapper.findDefaultByConfigType(
                DatabaseConfigEntity.ConfigType.TARGET, "default");
        }
        
        if (config == null) {
            throw new RuntimeException("未找到可用的目标数据库配置");
        }
        
        logger.debug("获取到目标数据库配置: {}", config.getConfigName());
        return config;
    }

    /**
     * 测试数据库连接
     */
    public boolean testConnection(DatabaseConfigEntity config) {
        return testConnection(config, null);
    }

    /**
     * 测试数据库连接（指定数据库名）
     */
    public boolean testConnection(DatabaseConfigEntity config, String databaseName) {
        logger.debug("测试数据库连接: {}@{}:{}", config.getUsername(), config.getHost(), config.getPort());
        
        try {
            String jdbcUrl = config.getJdbcUrl(databaseName);
            
            try (Connection conn = DriverManager.getConnection(
                    jdbcUrl, config.getUsername(), config.getPassword())) {
                
                boolean isValid = conn.isValid(5); // 5秒超时
                logger.debug("数据库连接测试结果: {}", isValid ? "成功" : "失败");
                return isValid;
            }
        } catch (SQLException e) {
            logger.warn("数据库连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建或更新配置
     */
    public DatabaseConfigEntity saveOrUpdateConfig(DatabaseConfigEntity config) {
        logger.info("保存或更新数据库配置: {}", config.getConfigName());
        
        // 设置时间戳
        LocalDateTime now = LocalDateTime.now();
        if (config.getId() == null) {
            // 新建配置
            config.setCreatedTime(now);
            config.setUpdatedTime(now);
            
            // 检查配置名称是否已存在
            int count = databaseConfigMapper.countByConfigNameAndEnvironment(
                config.getConfigName(), config.getEnvironment());
            if (count > 0) {
                throw new RuntimeException("配置名称已存在: " + config.getConfigName());
            }
            
            int result = databaseConfigMapper.insert(config);
            if (result <= 0) {
                throw new RuntimeException("保存配置失败");
            }
        } else {
            // 更新配置
            config.setUpdatedTime(now);
            int result = databaseConfigMapper.updateById(config);
            if (result <= 0) {
                throw new RuntimeException("更新配置失败");
            }
        }
        
        logger.info("配置保存成功: {} (ID: {})", config.getConfigName(), config.getId());
        return config;
    }

    /**
     * 根据ID查询配置
     */
    public DatabaseConfigEntity getConfigById(Long id) {
        return databaseConfigMapper.selectById(id);
    }

    /**
     * 查询所有配置
     */
    public List<DatabaseConfigEntity> getAllConfigs() {
        return databaseConfigMapper.selectList();
    }

    /**
     * 删除配置
     */
    public void deleteConfig(Long configId) {
        logger.info("删除配置: ID={}", configId);
        
        DatabaseConfigEntity config = databaseConfigMapper.selectById(configId);
        if (config == null) {
            throw new RuntimeException("配置不存在: " + configId);
        }
        
        int result = databaseConfigMapper.deleteById(configId);
        if (result <= 0) {
            throw new RuntimeException("删除配置失败");
        }
        
        logger.info("配置删除成功: {}", config.getConfigName());
    }

    /**
     * 获取环境列表
     */
    public List<String> getAllEnvironments() {
        return databaseConfigMapper.findAllEnvironments();
    }

    /**
     * 获取指定环境的所有启用配置
     */
    public List<DatabaseConfigEntity> getActiveConfigsByEnvironment(String environment) {
        return databaseConfigMapper.findActiveByEnvironment(environment);
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        logger.debug("获取配置统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 获取所有配置
            List<DatabaseConfigEntity> allConfigs = databaseConfigMapper.selectList();
            
            int totalCount = allConfigs.size();
            int activeCount = 0;
            int sourceCount = 0;
            int targetCount = 0;
            
            for (DatabaseConfigEntity config : allConfigs) {
                // 统计启用的配置
                if (config.getIsActive() != null && config.getIsActive()) {
                    activeCount++;
                }
                
                // 根据配置类型统计
                if (config.getConfigType() != null) {
                    if (DatabaseConfigEntity.ConfigType.SOURCE.equals(config.getConfigType())) {
                        sourceCount++;
                    } else if (DatabaseConfigEntity.ConfigType.TARGET.equals(config.getConfigType())) {
                        targetCount++;
                    }
                }
            }
            
            statistics.put("totalCount", totalCount);
            statistics.put("activeCount", activeCount);
            statistics.put("sourceCount", sourceCount);
            statistics.put("targetCount", targetCount);
            
            logger.debug("统计信息: 总数={}, 启用={}, 源={}, 目标={}", totalCount, activeCount, sourceCount, targetCount);
            
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            // 返回默认值
            statistics.put("totalCount", 0);
            statistics.put("activeCount", 0);
            statistics.put("sourceCount", 0);
            statistics.put("targetCount", 0);
        }
        
        return statistics;
    }

    /**
     * 批量测试数据库连接
     */
    public List<Map<String, Object>> batchTestConnection(List<Long> configIds) {
        logger.info("开始批量测试连接，配置ID列表: {}", configIds);
        
        List<Map<String, Object>> results = new java.util.ArrayList<>();
        
        for (Long configId : configIds) {
            Map<String, Object> result = new HashMap<>();
            try {
                DatabaseConfigEntity config = databaseConfigMapper.selectById(configId);
                if (config == null) {
                    result.put("configId", configId);
                    result.put("configName", "未知配置");
                    result.put("connected", false);
                    result.put("message", "配置不存在");
                } else {
                    boolean connected = testConnection(config);
                    result.put("configId", configId);
                    result.put("configName", config.getConfigName());
                    result.put("connected", connected);
                    result.put("message", connected ? "连接成功" : "连接失败");
                    
                    logger.debug("配置 {} 连接测试结果: {}", config.getConfigName(), connected ? "成功" : "失败");
                }
            } catch (Exception e) {
                logger.error("测试配置 {} 连接时发生异常", configId, e);
                result.put("configId", configId);
                result.put("configName", "配置ID: " + configId);
                result.put("connected", false);
                result.put("message", "测试异常: " + e.getMessage());
            }
            results.add(result);
        }
        
        long successCount = results.stream().mapToLong(r -> (Boolean) r.get("connected") ? 1 : 0).sum();
        logger.info("批量测试连接完成，总数: {}, 成功: {}, 失败: {}", 
            configIds.size(), successCount, configIds.size() - successCount);
        
        return results;
    }
} 