package com.liuyang.service.impl;

import com.liuyang.service.TableNameOptimizationService;
import com.liuyang.util.SnakeCaseConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 表名优化服务
 * 使用AI将表名转换为有意义的英文snake_case格式
 */
@Service
public class TableNameOptimizationServiceImpl implements TableNameOptimizationService {

    private static final Logger logger = LoggerFactory.getLogger(TableNameOptimizationService.class);

    @Autowired(required = false)
    @Qualifier("tableNamingChatClient")
    private ChatClient chatClient;

    // 英文表名检测模式
    private static final Pattern ENGLISH_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
    
    // Snake case检测模式
    private static final Pattern SNAKE_CASE_PATTERN = Pattern.compile("^[a-z][a-z0-9_]*$");

    // 常见表名映射
    private static final Map<String, String> COMMON_TABLE_MAPPINGS = new HashMap<>();
    
    static {
        // 用户相关表
        COMMON_TABLE_MAPPINGS.put("用户", "hc_users");
        COMMON_TABLE_MAPPINGS.put("用户表", "hc_users");
        COMMON_TABLE_MAPPINGS.put("用户信息", "hc_user_info");
        COMMON_TABLE_MAPPINGS.put("用户资料", "hc_user_profiles");

        // 订单相关表
        COMMON_TABLE_MAPPINGS.put("订单", "hc_orders");
        COMMON_TABLE_MAPPINGS.put("订单表", "hc_orders");
        COMMON_TABLE_MAPPINGS.put("订单信息", "hc_order_info");
        COMMON_TABLE_MAPPINGS.put("订单详情", "hc_order_details");

        // 商品相关表
        COMMON_TABLE_MAPPINGS.put("商品", "hc_products");
        COMMON_TABLE_MAPPINGS.put("商品表", "hc_products");
        COMMON_TABLE_MAPPINGS.put("商品信息", "hc_product_info");
        COMMON_TABLE_MAPPINGS.put("商品详情", "hc_product_details");

        // 系统相关表
        COMMON_TABLE_MAPPINGS.put("系统配置", "hc_system_config");
        COMMON_TABLE_MAPPINGS.put("系统参数", "hc_system_parameters");
        COMMON_TABLE_MAPPINGS.put("系统日志", "hc_system_logs");
        COMMON_TABLE_MAPPINGS.put("操作日志", "hc_operation_logs");

        // 权限相关表
        COMMON_TABLE_MAPPINGS.put("角色", "hc_roles");
        COMMON_TABLE_MAPPINGS.put("权限", "hc_permissions");
        COMMON_TABLE_MAPPINGS.put("菜单", "hc_menus");

        // 字典相关表
        COMMON_TABLE_MAPPINGS.put("字典", "hc_dictionaries");
        COMMON_TABLE_MAPPINGS.put("数据字典", "hc_data_dictionaries");
        COMMON_TABLE_MAPPINGS.put("字典项", "hc_dictionary_items");

        // 车辆相关表（针对您的业务场景）
        COMMON_TABLE_MAPPINGS.put("车主", "hc_car_owners");
        COMMON_TABLE_MAPPINGS.put("车主信息", "hc_car_owner_info");
        COMMON_TABLE_MAPPINGS.put("认证记录", "hc_auth_records");
        COMMON_TABLE_MAPPINGS.put("车主认证记录", "hc_car_owner_auth_records");
        COMMON_TABLE_MAPPINGS.put("操作员表", "hc_operators");
        COMMON_TABLE_MAPPINGS.put("操作员", "hc_operators");
    }

    /**
     * 优化表名
     */
    public TableNameOptimizationResult optimizeTableName(String originalTableName, String businessContext) {
        logger.info("开始优化表名: {}", originalTableName);
        
        TableNameOptimizationResult result = new TableNameOptimizationResult();
        result.setOriginalName(originalTableName);
        
        // 1. 如果有业务描述，优先使用AI根据业务描述生成表名
        if (businessContext != null && !businessContext.trim().isEmpty() && 
            !businessContext.trim().equals("数据迁移优化")) {
            logger.info("检测到业务描述，使用AI根据业务含义生成表名: {}", businessContext);
            
            if (chatClient != null) {
                try {
                    String prompt = buildTableNameOptimizationPrompt(originalTableName, businessContext);
                    String aiOptimizedName = optimizeWithAI(originalTableName, businessContext);
                    if (aiOptimizedName != null && !aiOptimizedName.trim().isEmpty()) {
                        result.setOptimizedName(aiOptimizedName);
                        result.setOptimizationType("AI_BUSINESS_OPTIMIZED");
                        result.setNote("AI根据业务描述生成英文snake_case表名");
                        result.setConfidence(0.9);
                        result.setAiPromptUsed(prompt);  // 保存使用的AI提示词
                        logger.info("AI根据业务描述优化表名: {} -> {}", originalTableName, aiOptimizedName);
                        return result;
                    }
                } catch (Exception e) {
                    logger.warn("AI表名优化失败: {}", originalTableName, e);
                }
            }
        }
        
        // 2. 检查是否已经是标准格式（仅在没有明确业务描述时）
        if (isStandardTableName(originalTableName)) {
            result.setOptimizedName(originalTableName);
            result.setOptimizationType("STANDARD");
            result.setNote("表名已符合snake_case规范");
            result.setConfidence(0.9);
            logger.debug("表名已符合标准: {}", originalTableName);
            return result;
        }
        
        // 3. 检查常见表名映射
        String commonMapping = getCommonTableMapping(originalTableName);
        if (commonMapping != null) {
            result.setOptimizedName(commonMapping);
            result.setOptimizationType("TEMPLATE");
            result.setNote("使用常见表名模板");
            result.setConfidence(0.95);
            logger.debug("使用模板映射: {} -> {}", originalTableName, commonMapping);
            return result;
        }
        
        // 4. 使用AI优化（通用优化）
        if (chatClient != null) {
            try {
                String prompt = buildTableNameOptimizationPrompt(originalTableName, businessContext);
                String aiOptimizedName = optimizeWithAI(originalTableName, businessContext);
                if (aiOptimizedName != null && !aiOptimizedName.trim().isEmpty()) {
                    result.setOptimizedName(aiOptimizedName);
                    result.setOptimizationType("AI_OPTIMIZED");
                    result.setNote("AI分析优化为英文snake_case格式");
                    result.setConfidence(0.8);
                    result.setAiPromptUsed(prompt);  // 保存使用的AI提示词
                    logger.info("AI优化表名: {} -> {}", originalTableName, aiOptimizedName);
                    return result;
                }
            } catch (Exception e) {
                logger.warn("AI表名优化失败: {}", originalTableName, e);
            }
        }
        
        // 5. 降级方案：自动转换
        String fallbackName = SnakeCaseConverter.toSnakeCase(originalTableName);

        // 确保降级方案也使用hc_前缀
        if (!fallbackName.startsWith("hc_")) {
            fallbackName = "hc_" + fallbackName;
        }

        result.setOptimizedName(fallbackName);
        result.setOptimizationType("FALLBACK");
        result.setNote("自动转换为hc_前缀的snake_case格式，建议人工审核");
        result.setConfidence(0.6);
        logger.debug("降级转换: {} -> {}", originalTableName, fallbackName);
        
        return result;
    }

    /**
     * 使用AI优化表名
     */
    private String optimizeWithAI(String tableName, String businessContext) {
        String prompt = buildTableNameOptimizationPrompt(tableName, businessContext);
        
        try {
            String response = chatClient.prompt(prompt).call().content();
            return extractTableNameFromResponse(response);
        } catch (Exception e) {
            logger.error("AI表名优化调用失败", e);
            throw e;
        }
    }

    /**
     * 构建表名优化提示词
     */
    private String buildTableNameOptimizationPrompt(String tableName, String businessContext) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据业务描述将表名转换为标准的英文snake_case格式：\n\n");
        prompt.append("原表名: ").append(tableName).append("\n");

        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("业务描述: ").append(businessContext).append("\n");
        }

        prompt.append("\n转换要求：\n");
        prompt.append("1. 必须以 'hc_' 开头（hc代表系统前缀）\n");
        prompt.append("2. 根据业务描述理解表的实际用途，生成相应的英文表名\n");
        prompt.append("3. 必须使用标准英文单词，禁止使用拼音或音译\n");
        prompt.append("4. 使用snake_case格式（单词间用下划线分隔，全部小写）\n");
        prompt.append("5. 表名要简洁明确，准确反映业务含义\n");
        prompt.append("6. 通常使用复数形式或记录形式（如records, logs, histories）\n");
        prompt.append("7. 忽略原表名的拼音结构，完全基于业务含义重新命名\n\n");

        prompt.append("转换示例：\n");
        prompt.append("- 用户信息表 -> hc_users\n");
        prompt.append("- 订单记录表 -> hc_orders\n");
        prompt.append("- 商品详情表 -> hc_product_details\n");
        prompt.append("- 支付流水表 -> hc_payment_records\n");
        prompt.append("- 健康用车消费记录表 -> hc_health_car_consumption_records\n");
        prompt.append("- 车辆违章信息表 -> hc_vehicle_violation_records\n");
        prompt.append("- 检测报告数据表 -> hc_inspection_report_data\n");
        prompt.append("- 车主认证记录表 -> hc_car_owner_auth_records\n\n");
        
        prompt.append("重要：请完全忽略原表名，仅根据业务描述生成新的英文表名。只返回新表名，不要包含任何解释。");
        
        return prompt.toString();
    }

    /**
     * 从AI响应中提取表名
     */
    private String extractTableNameFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }
        
        // 清理响应，提取表名
        String cleaned = response.trim()
                .replaceAll("```.*?```", "") // 移除代码块
                .replaceAll("[\"'`]", "") // 移除引号
                .replaceAll("表名[:：]?\\s*", "") // 移除"表名:"等前缀
                .replaceAll("优化后[:：]?\\s*", "") // 移除"优化后:"等前缀
                .replaceAll("建议[:：]?\\s*", "") // 移除"建议:"等前缀
                .split("\\s+")[0]; // 取第一个单词
        
        // 验证格式
        if (isValidTableName(cleaned)) {
            return cleaned.toLowerCase();
        }
        
        return null;
    }

    /**
     * 检查是否为标准表名
     */
    private boolean isStandardTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        return ENGLISH_PATTERN.matcher(tableName).matches() && 
               SNAKE_CASE_PATTERN.matcher(tableName).matches();
    }

    /**
     * 获取常见表名映射
     */
    private String getCommonTableMapping(String tableName) {
        if (tableName == null) {
            return null;
        }
        
        // 直接匹配
        String directMatch = COMMON_TABLE_MAPPINGS.get(tableName);
        if (directMatch != null) {
            return directMatch;
        }
        
        // 模糊匹配
        for (Map.Entry<String, String> entry : COMMON_TABLE_MAPPINGS.entrySet()) {
            if (tableName.contains(entry.getKey()) || entry.getKey().contains(tableName)) {
                return entry.getValue();
            }
        }
        
        return null;
    }

    /**
     * 验证表名格式
     */
    private boolean isValidTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否为有效的标识符格式
        return Pattern.matches("^[a-z][a-z0-9_]*$", tableName) && 
               tableName.length() <= 63; // PostgreSQL表名长度限制
    }

    /**
     * 表名优化结果
     */
    public static class TableNameOptimizationResult {
        private String originalName;
        private String optimizedName;
        private String optimizationType;
        private String note;
        private double confidence;
        private String aiPromptUsed;  // AI优化时使用的提示词
        
        // Getter and Setter methods
        public String getOriginalName() {
            return originalName;
        }
        
        public void setOriginalName(String originalName) {
            this.originalName = originalName;
        }
        
        public String getOptimizedName() {
            return optimizedName;
        }
        
        public void setOptimizedName(String optimizedName) {
            this.optimizedName = optimizedName;
        }
        
        public String getOptimizationType() {
            return optimizationType;
        }
        
        public void setOptimizationType(String optimizationType) {
            this.optimizationType = optimizationType;
        }
        
        public String getNote() {
            return note;
        }
        
        public void setNote(String note) {
            this.note = note;
        }
        
        public double getConfidence() {
            return confidence;
        }
        
        public void setConfidence(double confidence) {
            this.confidence = confidence;
        }
        
        public String getAiPromptUsed() {
            return aiPromptUsed;
        }

        public void setAiPromptUsed(String aiPromptUsed) {
            this.aiPromptUsed = aiPromptUsed;
        }

        public boolean isOptimized() {
            return !originalName.equals(optimizedName);
        }
    }
}
