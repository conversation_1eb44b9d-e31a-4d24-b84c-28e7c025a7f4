package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.TableInfo;
import com.liuyang.dto.TableStructure;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseService;
import com.liuyang.service.TableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 表服务类
 */
@Service
public class TableServiceImpl implements TableService {
    
    private static final Logger logger = LoggerFactory.getLogger(TableService.class);
    
    @Autowired
    private DatabaseService databaseService;

    @Override
    /**
     * 获取指定数据库的表列表
     */
    public List<TableInfo> getTablesByDatabase(String databaseName) {
        List<TableInfo> tables = new ArrayList<>();
        
        try (Connection conn = databaseService.getConnection(databaseName)) {
            String sql = "SELECT " +
                        "    t.table_name, " +
                        "    t.table_type, " +
                        "    t.table_schema, " +
                        "    obj_description(c.oid) as table_comment, " +
                        "    (SELECT count(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = t.table_schema) as column_count " +
                        "FROM information_schema.tables t " +
                        "LEFT JOIN pg_class c ON c.relname = t.table_name " +
                        "LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.table_schema " +
                        "WHERE t.table_schema = 'public' " +
                        "    AND t.table_type = 'BASE TABLE' " +
                        "ORDER BY t.table_name";

            logger.info("执行TableService表查询SQL: {}", sql);
            logger.info("查询数据库: {}", databaseName);
            
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    TableInfo tableInfo = new TableInfo();
                    tableInfo.setTableName(rs.getString("table_name"));
                    tableInfo.setTableType(rs.getString("table_type"));
                    tableInfo.setSchema(rs.getString("table_schema"));
                    tableInfo.setTableComment(rs.getString("table_comment"));
                    
                    // 获取行数
                    Long rowCount = getTableRowCount(conn, tableInfo.getSchema(), tableInfo.getTableName());
                    tableInfo.setRowCount(rowCount);
                    
                    tables.add(tableInfo);
                }
            }
        } catch (SQLException e) {
            logger.error("获取数据库 {} 的表列表失败", databaseName, e);
            throw new DatabaseException("获取表列表失败: " + e.getMessage(), e);
        }
        
        return tables;
    }
    
    /**
     * 获取表行数
     */
    private Long getTableRowCount(Connection conn, String schema, String tableName) {
        try {
            String sql = "SELECT count(*) FROM " + schema + "." + tableName;
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        } catch (SQLException e) {
            logger.warn("获取表 {}.{} 行数失败: {}", schema, tableName, e.getMessage());
        }
        return 0L;
    }
    
    /**
     * 获取表结构信息
     */
    public TableStructure getTableStructure(String databaseName, String tableName) {
        try (Connection conn = databaseService.getConnection(databaseName)) {
            TableStructure structure = new TableStructure();
            structure.setTableName(tableName);
            
            // 获取表基本信息
            getTableBasicInfo(conn, structure);
            
            // 获取列信息
            List<ColumnInfo> columns = getTableColumns(conn, tableName);
            structure.setColumns(columns);
            
            // 获取主键信息
            List<String> primaryKeys = getTablePrimaryKeys(conn, tableName);
            structure.setPrimaryKeys(primaryKeys);
            
            return structure;
        } catch (SQLException e) {
            logger.error("获取表 {} 结构失败", tableName, e);
            throw new DatabaseException("获取表结构失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取表基本信息
     */
    private void getTableBasicInfo(Connection conn, TableStructure structure) throws SQLException {
        String sql = "SELECT " +
                    "    t.table_schema, " +
                    "    obj_description(c.oid) as table_comment " +
                    "FROM information_schema.tables t " +
                    "LEFT JOIN pg_class c ON c.relname = t.table_name " +
                    "LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.table_schema " +
                    "WHERE t.table_name = ? AND t.table_type = 'BASE TABLE'";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, structure.getTableName());
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    structure.setSchema(rs.getString("table_schema"));
                    structure.setTableComment(rs.getString("table_comment"));
                }
            }
        }
    }

    /**
     * 获取表列信息
     */
    private List<ColumnInfo> getTableColumns(Connection conn, String tableName) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();

        String sql = "SELECT " +
                    "    c.column_name, " +
                    "    c.data_type, " +
                    "    c.character_maximum_length, " +
                    "    c.numeric_precision, " +
                    "    c.numeric_scale, " +
                    "    c.is_nullable, " +
                    "    c.column_default, " +
                    "    col_description(pgc.oid, c.ordinal_position) as column_comment " +
                    "FROM information_schema.columns c " +
                    "LEFT JOIN pg_class pgc ON pgc.relname = c.table_name " +
                    "WHERE c.table_name = ? " +
                    "ORDER BY c.ordinal_position";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ColumnInfo column = new ColumnInfo();
                    column.setColumnName(rs.getString("column_name"));
                    column.setDataType(rs.getString("data_type"));

                    // 根据数据类型设置正确的大小
                    String dataType = rs.getString("data_type");
                    if ("numeric".equals(dataType.toLowerCase())) {
                        // 对于NUMERIC类型，使用numeric_precision
                        Integer precision = rs.getObject("numeric_precision", Integer.class);
                        column.setColumnSize(precision);
                    } else {
                        // 对于字符类型，使用character_maximum_length
                        Integer maxLength = rs.getObject("character_maximum_length", Integer.class);
                        column.setColumnSize(maxLength);
                    }

                    column.setDecimalDigits(rs.getObject("numeric_scale", Integer.class));
                    column.setNullable("YES".equals(rs.getString("is_nullable")));
                    column.setDefaultValue(rs.getString("column_default"));
                    column.setComment(rs.getString("column_comment"));

                    columns.add(column);
                }
            }
        }

        return columns;
    }

    /**
     * 获取表主键信息
     */
    private List<String> getTablePrimaryKeys(Connection conn, String tableName) throws SQLException {
        List<String> primaryKeys = new ArrayList<>();

        String sql = "SELECT " +
                    "    kcu.column_name " +
                    "FROM information_schema.table_constraints tc " +
                    "JOIN information_schema.key_column_usage kcu " +
                    "    ON tc.constraint_name = kcu.constraint_name " +
                    "    AND tc.table_schema = kcu.table_schema " +
                    "WHERE tc.constraint_type = 'PRIMARY KEY' " +
                    "    AND tc.table_name = ? " +
                    "ORDER BY kcu.ordinal_position";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, tableName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    primaryKeys.add(rs.getString("column_name"));
                }
            }
        }

        return primaryKeys;
    }

    /**
     * 检查表是否存在
     */
    public boolean tableExists(String databaseName, String tableName) {
        try (Connection conn = databaseService.getConnection(databaseName)) {
            String sql = "SELECT 1 FROM information_schema.tables " +
                        "WHERE table_name = ? AND table_type = 'BASE TABLE'";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, tableName);
                try (ResultSet rs = stmt.executeQuery()) {
                    return rs.next();
                }
            }
        } catch (SQLException e) {
            logger.error("检查表是否存在失败", e);
            return false;
        }
    }
}
