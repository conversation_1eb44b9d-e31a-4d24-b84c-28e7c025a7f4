package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.EnhancedFieldMapping;
import com.liuyang.dto.FieldType;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import com.liuyang.service.FieldMappingAnalysisService;
import com.liuyang.util.SnakeCaseConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段映射分析服务
 * 提供智能字段映射分析和建议
 */
@Service
public class FieldMappingAnalysisServiceimpl implements FieldMappingAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(FieldMappingAnalysisService.class);

    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;
    
    // 常见字段映射模板
    private static final Map<String, String> COMMON_FIELD_MAPPINGS = new HashMap<>();
    
    static {
        // 基础字段映射
        COMMON_FIELD_MAPPINGS.put("id", "business_id");
        COMMON_FIELD_MAPPINGS.put("creator", "create_user");
        COMMON_FIELD_MAPPINGS.put("createtime", "create_time");
        COMMON_FIELD_MAPPINGS.put("create_time", "create_time");
        COMMON_FIELD_MAPPINGS.put("updater", "update_user");
        COMMON_FIELD_MAPPINGS.put("updatetime", "update_time");
        COMMON_FIELD_MAPPINGS.put("update_time", "update_time");
        COMMON_FIELD_MAPPINGS.put("modifier", "update_user");
        COMMON_FIELD_MAPPINGS.put("modify_time", "update_time");
        COMMON_FIELD_MAPPINGS.put("gmt_create", "create_time");
        COMMON_FIELD_MAPPINGS.put("gmt_modified", "update_time");
        
        // 业务字段映射示例
        COMMON_FIELD_MAPPINGS.put("name", "name");
        COMMON_FIELD_MAPPINGS.put("title", "title");
        COMMON_FIELD_MAPPINGS.put("description", "description");
        COMMON_FIELD_MAPPINGS.put("status", "status");
        COMMON_FIELD_MAPPINGS.put("type", "type");
        COMMON_FIELD_MAPPINGS.put("code", "code");
        COMMON_FIELD_MAPPINGS.put("value", "value");
    }
    
    /**
     * 分析源表结构并生成智能字段映射建议
     */
    public FieldMappingAnalysisResult analyzeFieldMappings(
            TableStructure sourceTable,
            List<FieldMapping> templateMappings) {
        return analyzeFieldMappings(sourceTable, templateMappings, null, null);
    }

    /**
     * 分析源表结构并生成智能字段映射建议（支持AI增强）
     */
    public FieldMappingAnalysisResult analyzeFieldMappings(
            TableStructure sourceTable,
            List<FieldMapping> templateMappings,
            String targetTableName,
            String businessContext) {
        
        logger.info("开始分析字段映射: 源表={}, 模板映射数={}", 
                   sourceTable.getTableName(), templateMappings != null ? templateMappings.size() : 0);
        
        FieldMappingAnalysisResult result = new FieldMappingAnalysisResult();
        
        // 获取源表所有字段
        Set<String> sourceFields = sourceTable.getColumns().stream()
                .map(col -> col.getColumnName().toLowerCase())
                .collect(Collectors.toSet());
        
        // 已映射的源字段
        Set<String> mappedSourceFields = new HashSet<>();
        
        // 1. 处理模板映射
        List<FieldMapping> confirmedMappings = new ArrayList<>();
        if (templateMappings != null) {
            for (FieldMapping template : templateMappings) {
                String sourceField = template.getSourceField().toLowerCase();
                if (sourceFields.contains(sourceField)) {
                    confirmedMappings.add(template);
                    mappedSourceFields.add(sourceField);
                    result.addMappedField(sourceField);
                } else {
                    result.addMissingTemplateField(template);
                }
            }
        }
        
        // 2. 对未映射的字段进行智能分析
        List<FieldMapping> suggestedMappings = new ArrayList<>();
        List<String> unmappedFields = new ArrayList<>();

        // 分析每个源字段
        for (ColumnInfo column : sourceTable.getColumns()) {
            String sourceField = column.getColumnName();
            String sourceFieldLower = sourceField.toLowerCase();

            // 跳过已经在模板中映射的字段
            if (mappedSourceFields.contains(sourceFieldLower)) {
                continue;
            }

            // 对字段进行AI或智能映射分析
            {
                // 普通字段尝试AI建议
                if (aiFieldNamingService != null && targetTableName != null) {
                    try {
                        String suggestedName = generateAiSuggestion(sourceField, column, businessContext);
                        if (suggestedName != null && !suggestedName.equals(sourceField)) {
                            EnhancedFieldMapping aiMapping = createEnhancedFieldMapping(column, suggestedName);
                            aiMapping.setComment("AI建议映射");
                            aiMapping.setFieldType(FieldType.AI_OPTIMIZED);
                            aiMapping.setConfidence(0.8); // AI字段置信度

                            suggestedMappings.add(aiMapping);
                            result.addSuggestedField(sourceFieldLower);
                            mappedSourceFields.add(sourceFieldLower);
                            logger.debug("AI建议映射: {} -> {}", sourceField, suggestedName);
                        } else {
                            unmappedFields.add(sourceField);
                            result.addUnmappedField(sourceField);
                        }
                    } catch (Exception e) {
                        logger.warn("AI字段映射建议生成失败: {}", e.getMessage());
                        unmappedFields.add(sourceField);
                        result.addUnmappedField(sourceField);
                    }
                } else {
                    // 使用传统智能映射
                    String suggestedTarget = suggestTargetField(sourceField);
                    if (suggestedTarget != null) {
                        EnhancedFieldMapping suggested = createEnhancedFieldMapping(column, suggestedTarget);
                        suggested.setComment("智能映射建议: " + column.getComment());
                        suggested.setFieldType(FieldType.MANUAL);
                        suggested.setConfidence(0.6);

                        suggestedMappings.add(suggested);
                        result.addSuggestedField(sourceFieldLower);
                    } else {
                        unmappedFields.add(sourceField);
                        result.addUnmappedField(sourceField);
                    }
                }
            }
        }
        
        result.setConfirmedMappings(confirmedMappings);
        result.setSuggestedMappings(suggestedMappings);
        result.setUnmappedFields(unmappedFields);
        
        logger.info("字段映射分析完成: 确认映射={}, 建议映射={}, 未映射={}", 
                   confirmedMappings.size(), suggestedMappings.size(), unmappedFields.size());
        
        return result;
    }
    
    /**
     * 基于字段名称建议目标字段（使用snake_case转换）
     */
    private String suggestTargetField(String sourceField) {
        String lowerField = sourceField.toLowerCase();

        // 1. 直接匹配常见字段映射
        if (COMMON_FIELD_MAPPINGS.containsKey(lowerField)) {
            return COMMON_FIELD_MAPPINGS.get(lowerField);
        }

        // 2. 使用SnakeCaseConverter进行智能转换
        String snakeCaseField = SnakeCaseConverter.toSnakeCase(sourceField);

        // 3. 如果转换后的字段与原字段不同，说明进行了有效转换
        if (!snakeCaseField.equals(sourceField.toLowerCase())) {
            logger.debug("字段 '{}' 转换为snake_case: '{}'", sourceField, snakeCaseField);
            return snakeCaseField;
        }

        // 4. 模糊匹配
        for (Map.Entry<String, String> entry : COMMON_FIELD_MAPPINGS.entrySet()) {
            if (lowerField.contains(entry.getKey()) || entry.getKey().contains(lowerField)) {
                return entry.getValue();
            }
        }

        // 5. 基于命名规则的转换
        if (lowerField.endsWith("time") || lowerField.endsWith("date")) {
            if (lowerField.contains("create") || lowerField.contains("add")) {
                return "create_time";
            } else if (lowerField.contains("update") || lowerField.contains("modify")) {
                return "update_time";
            }
        }

        if (lowerField.endsWith("user") || lowerField.endsWith("by")) {
            if (lowerField.contains("create") || lowerField.contains("add")) {
                return "create_user";
            } else if (lowerField.contains("update") || lowerField.contains("modify")) {
                return "update_user";
            }
        }
        
        // 4. 保持原字段名（转换为下划线命名）
        return convertToSnakeCase(sourceField);
    }
    
    /**
     * 将驼峰命名转换为下划线命名
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 创建增强字段映射
     */
    private EnhancedFieldMapping createEnhancedFieldMapping(ColumnInfo column, String targetField) {
        EnhancedFieldMapping mapping = new EnhancedFieldMapping();
        mapping.setSourceField(column.getColumnName());
        mapping.setTargetField(targetField);
        mapping.setSourceType(column.getDataType());
        mapping.setTargetType(mapToStandardType(column.getDataType()));
        mapping.setSourceSize(column.getColumnSize());
        mapping.setTargetSize(column.getColumnSize());
        mapping.setNullable(column.isNullable());
        mapping.setBusinessMeaning(column.getComment());

        // 检查字段特征
        mapping.setEnglishName(isEnglishFieldName(targetField));
        mapping.setSnakeCase(isSnakeCase(targetField));

        return mapping;
    }

    /**
     * 检查是否为英文字段名
     */
    private boolean isEnglishFieldName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        // 简单检查：只包含英文字母、数字和下划线
        return fieldName.matches("^[a-zA-Z][a-zA-Z0-9_]*$");
    }

    /**
     * 检查是否为snake_case格式
     */
    private boolean isSnakeCase(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        // snake_case格式：小写字母开头，只包含小写字母、数字和下划线
        return fieldName.matches("^[a-z][a-z0-9_]*$");
    }

    /**
     * 生成AI建议（简化版，实际调用AI服务）
     */
    private String generateAiSuggestion(String sourceField, ColumnInfo column, String businessContext) {
        if (aiFieldNamingService != null) {
            try {
                // 调用AI服务生成建议
                List<FieldMapping> suggestions = aiFieldNamingService.generateFieldMappingSuggestions(
                    createTableStructureForSingleField(column), "temp_table", businessContext);

                if (!suggestions.isEmpty()) {
                    return suggestions.get(0).getTargetField();
                }
            } catch (Exception e) {
                logger.warn("AI建议生成失败: {}", e.getMessage());
            }
        }

        // 降级方案：使用snake_case转换
        return SnakeCaseConverter.toSnakeCase(sourceField);
    }

    /**
     * 为单个字段创建临时表结构
     */
    private TableStructure createTableStructureForSingleField(ColumnInfo column) {
        TableStructure table = new TableStructure();
        table.setTableName("temp_table");
        table.setColumns(Arrays.asList(column));
        return table;
    }
    
    /**
     * 映射到标准数据类型
     */
    private String mapToStandardType(String sourceType) {
        if (sourceType == null) return "varchar";
        
        String lowerType = sourceType.toLowerCase();
        
        if (lowerType.contains("varchar") || lowerType.contains("text") || lowerType.contains("char")) {
            return "varchar";
        } else if (lowerType.contains("int") || lowerType.contains("serial")) {
            return "bigint";
        } else if (lowerType.contains("timestamp") || lowerType.contains("datetime")) {
            return "timestamp";
        } else if (lowerType.contains("date")) {
            return "date";
        } else if (lowerType.contains("decimal") || lowerType.contains("numeric")) {
            return "decimal";
        } else if (lowerType.contains("bool")) {
            return "boolean";
        }
        
        return sourceType;
    }
    
    /**
     * 字段映射分析结果
     */
    public static class FieldMappingAnalysisResult {
        private List<FieldMapping> confirmedMappings = new ArrayList<>();
        private List<FieldMapping> suggestedMappings = new ArrayList<>();
        private List<String> unmappedFields = new ArrayList<>();
        private List<FieldMapping> missingTemplateFields = new ArrayList<>();
        private Set<String> mappedFields = new HashSet<>();
        private Set<String> suggestedFields = new HashSet<>();
        
        // Getters and setters
        public List<FieldMapping> getConfirmedMappings() { return confirmedMappings; }
        public void setConfirmedMappings(List<FieldMapping> confirmedMappings) { this.confirmedMappings = confirmedMappings; }
        
        public List<FieldMapping> getSuggestedMappings() { return suggestedMappings; }
        public void setSuggestedMappings(List<FieldMapping> suggestedMappings) { this.suggestedMappings = suggestedMappings; }
        
        public List<String> getUnmappedFields() { return unmappedFields; }
        public void setUnmappedFields(List<String> unmappedFields) { this.unmappedFields = unmappedFields; }
        
        public List<FieldMapping> getMissingTemplateFields() { return missingTemplateFields; }
        public void setMissingTemplateFields(List<FieldMapping> missingTemplateFields) { this.missingTemplateFields = missingTemplateFields; }
        
        public void addMappedField(String field) { this.mappedFields.add(field); }
        public void addSuggestedField(String field) { this.suggestedFields.add(field); }
        public void addUnmappedField(String field) { this.unmappedFields.add(field); }
        public void addMissingTemplateField(FieldMapping field) { this.missingTemplateFields.add(field); }
        
        public int getTotalMappings() {
            return confirmedMappings.size() + suggestedMappings.size();
        }
        
        public boolean hasUnmappedFields() {
            return !unmappedFields.isEmpty();
        }
        
        public boolean hasMissingTemplateFields() {
            return !missingTemplateFields.isEmpty();
        }
    }
}
