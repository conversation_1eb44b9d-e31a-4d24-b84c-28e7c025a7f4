package com.liuyang.service.impl;

import com.liuyang.entity.OperationLog;
import com.liuyang.mapper.OperationLogMapper;
import com.liuyang.service.OperationLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务实现类
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    private static final Logger logger = LoggerFactory.getLogger(OperationLogServiceImpl.class);

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    public boolean saveLog(OperationLog operationLog) {
        try {
            if (operationLog.getCreatedTime() == null) {
                operationLog.setCreatedTime(LocalDateTime.now());
            }
            int result = operationLogMapper.insert(operationLog);
            return result > 0;
        } catch (Exception e) {
            logger.error("保存操作日志失败", e);
            return false;
        }
    }

    @Override
    @Async
    public void saveLogAsync(OperationLog operationLog) {
        saveLog(operationLog);
    }

    @Override
    public OperationLog getLogById(Long id) {
        try {
            return operationLogMapper.selectById(id);
        } catch (Exception e) {
            logger.error("查询操作日志失败: id={}", id, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getLogsByPage(Long userId, String username, String operationType,
                                           LocalDateTime startTime, LocalDateTime endTime,
                                           int page, int pageSize) {
        Map<String, Object> result = new HashMap<>();
        try {
            logger.info("分页查询参数: userId={}, username={}, operationType={}, startTime={}, endTime={}, page={}, pageSize={}",
                       userId, username, operationType, startTime, endTime, page, pageSize);

            // 计算偏移量
            int offset = (page - 1) * pageSize;

            // 查询数据
            List<OperationLog> logs = operationLogMapper.selectByPage(
                userId, username, operationType, startTime, endTime, offset, pageSize);

            // 查询总数
            long total = operationLogMapper.countByCondition(
                userId, username, operationType, startTime, endTime);

            logger.info("查询结果: 总数={}, 当前页数据量={}", total, logs.size());
            
            // 计算总页数
            int totalPages = (int) Math.ceil((double) total / pageSize);
            
            result.put("success", true);
            result.put("data", logs);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            
        } catch (Exception e) {
            logger.error("分页查询操作日志失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> getOperationTypeStats(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return operationLogMapper.selectOperationTypeStats(startTime, endTime);
        } catch (Exception e) {
            logger.error("查询操作类型统计失败", e);
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> getUserOperationStats(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return operationLogMapper.selectUserOperationStats(startTime, endTime);
        } catch (Exception e) {
            logger.error("查询用户操作统计失败", e);
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> getDailyOperationStats(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return operationLogMapper.selectDailyOperationStats(startTime, endTime);
        } catch (Exception e) {
            logger.error("查询每日操作统计失败", e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getErrorStats(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return operationLogMapper.selectErrorStats(startTime, endTime);
        } catch (Exception e) {
            logger.error("查询错误统计失败", e);
            return null;
        }
    }

    @Override
    public List<OperationLog> getRecentLogs(int limit) {
        try {
            return operationLogMapper.selectRecent(limit);
        } catch (Exception e) {
            logger.error("查询最近日志失败", e);
            return null;
        }
    }

    @Override
    public int cleanExpiredLogs(LocalDateTime beforeTime) {
        try {
            int deletedCount = operationLogMapper.deleteBeforeTime(beforeTime);
            logger.info("清理过期日志完成，删除 {} 条记录", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            logger.error("清理过期日志失败", e);
            return 0;
        }
    }

    @Override
    public void logUserLogin(String username, String clientIp, String userAgent, boolean success, String errorMessage) {
        OperationLog log = new OperationLog();
        log.setUsername(username);
        log.setOperationType("USER_LOGIN");
        log.setOperationDesc(success ? "用户登录成功" : "用户登录失败");
        log.setTargetType("USER");
        log.setTargetId(username);
        log.setRequestMethod("POST");
        log.setRequestUrl("/api/satoken/login");
        log.setResponseStatus(success ? 200 : 401);
        log.setErrorMessage(errorMessage);
        log.setClientIp(clientIp);
        log.setUserAgent(userAgent);
        log.setCreatedTime(LocalDateTime.now());
        
        saveLogAsync(log);
        logger.info("记录用户登录日志: {} - {}", username, success ? "成功" : "失败");
    }

    @Override
    public void logUserLogout(String username, String clientIp, String userAgent) {
        OperationLog log = new OperationLog();
        log.setUsername(username);
        log.setOperationType("USER_LOGOUT");
        log.setOperationDesc("用户登出");
        log.setTargetType("USER");
        log.setTargetId(username);
        log.setRequestMethod("POST");
        log.setRequestUrl("/api/satoken/logout");
        log.setResponseStatus(200);
        log.setClientIp(clientIp);
        log.setUserAgent(userAgent);
        log.setCreatedTime(LocalDateTime.now());
        
        saveLogAsync(log);
        logger.info("记录用户登出日志: {}", username);
    }

    @Override
    public void logDatabaseMigration(String username, String sourceDb, String targetDb, String tableName,
                                   boolean success, long executionTime, String errorMessage) {
        OperationLog log = new OperationLog();
        log.setUsername(username);
        log.setOperationType("DATABASE_MIGRATION");
        log.setOperationDesc(String.format("数据库迁移: %s.%s -> %s.%s", sourceDb, tableName, targetDb, tableName));
        log.setTargetType("TABLE");
        log.setTargetId(sourceDb + "." + tableName);
        log.setRequestMethod("POST");
        log.setRequestUrl("/api/copy/table");
        log.setResponseStatus(success ? 200 : 500);
        log.setErrorMessage(errorMessage);
        log.setExecutionTime(executionTime);
        log.setCreatedTime(LocalDateTime.now());

        saveLogAsync(log);
        logger.info("记录数据库迁移日志: {} - {} -> {} - {}", username, sourceDb, targetDb,
                   success ? "成功" : "失败");
    }

    @Override
    public void logConfigManagement(String username, String operation, String configName, String configId,
                                  boolean success, String errorMessage) {
        OperationLog log = new OperationLog();
        log.setUsername(username);
        log.setOperationType("CONFIG_MANAGEMENT");
        log.setOperationDesc(String.format("配置管理: %s - %s", operation, configName));
        log.setTargetType("CONFIG");
        log.setTargetId(configId);
        log.setRequestMethod(getHttpMethodByOperation(operation));
        log.setRequestUrl("/api/database-config");
        log.setResponseStatus(success ? 200 : 500);
        log.setErrorMessage(errorMessage);
        log.setCreatedTime(LocalDateTime.now());

        saveLogAsync(log);
        logger.info("记录配置管理日志: {} - {} {} - {}", username, operation, configName,
                   success ? "成功" : "失败");
    }

    /**
     * 根据操作类型获取HTTP方法
     */
    private String getHttpMethodByOperation(String operation) {
        switch (operation.toUpperCase()) {
            case "CREATE":
                return "POST";
            case "UPDATE":
                return "PUT";
            case "DELETE":
                return "DELETE";
            default:
                return "GET";
        }
    }
}
