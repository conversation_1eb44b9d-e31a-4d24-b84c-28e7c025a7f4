package com.liuyang.service.impl;

import com.liuyang.entity.User;
import com.liuyang.mapper.UserMapper;
import com.liuyang.service.UserService;
import com.liuyang.controller.LogWebSocketController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    // 传统Session登录方法已删除，改用Sa-Token JWT认证


    
    @Override
    public User getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }
    
    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }
    
    @Override
    public List<User> getAllUsers() {
        return userMapper.selectAll();
    }
    
    @Override
    @Transactional
    public User createUser(User user, String operator) {
        try {
            // 参数验证
            if (!StringUtils.hasText(user.getUsername())) {
                throw new IllegalArgumentException("用户名不能为空");
            }
            
            if (isUsernameExists(user.getUsername())) {
                throw new IllegalArgumentException("用户名已存在");
            }
            
            // 设置默认值
            if (user.getStatus() == null) {
                user.setStatus(User.Status.ENABLED.getCode());
            }
            if (!StringUtils.hasText(user.getRole())) {
                user.setRole(User.Role.USER.getCode());
            }
            
            // 加密密码
            if (StringUtils.hasText(user.getPassword())) {
                user.setPassword(passwordEncoder.encode(user.getPassword()));
            } else {
                // 默认密码
                user.setPassword(passwordEncoder.encode("123456"));
            }
            
            user.setCreatedBy(operator);
            userMapper.insert(user);
            
            String createMsg = String.format("👤 创建用户: %s (%s) - 操作人: %s", 
                                           user.getUsername(), user.getDisplayName(), operator);
            logger.info(createMsg);
            LogWebSocketController.addLog("INFO", "UserService", createMsg);
            
            return user;
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 创建用户失败: %s - %s", user.getUsername(), e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("创建用户失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public User updateUser(User user, String operator) {
        try {
            User existingUser = userMapper.selectById(user.getId());
            if (existingUser == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            // 检查用户名是否重复
            if (!existingUser.getUsername().equals(user.getUsername()) && 
                isUsernameExists(user.getUsername(), user.getId())) {
                throw new IllegalArgumentException("用户名已存在");
            }
            
            user.setUpdatedBy(operator);
            userMapper.update(user);
            
            String updateMsg = String.format("👤 更新用户: %s (%s) - 操作人: %s", 
                                           user.getUsername(), user.getDisplayName(), operator);
            logger.info(updateMsg);
            LogWebSocketController.addLog("INFO", "UserService", updateMsg);
            
            return userMapper.selectById(user.getId());
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 更新用户失败: %s - %s", user.getUsername(), e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("更新用户失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public void deleteUser(Long id, String operator) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            // Session相关代码已删除，改用Sa-Token JWT认证
            
            // 删除用户
            userMapper.deleteById(id);
            
            String deleteMsg = String.format("🗑️ 删除用户: %s (%s) - 操作人: %s", 
                                           user.getUsername(), user.getDisplayName(), operator);
            logger.info(deleteMsg);
            LogWebSocketController.addLog("INFO", "UserService", deleteMsg);
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 删除用户失败: ID=%d - %s", id, e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("删除用户失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public void changeUserStatus(Long id, Integer status, String operator) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            userMapper.updateStatus(id, status, operator);
            
            String statusText = User.Status.ENABLED.getCode().equals(status) ? "启用" : "禁用";
            String statusMsg = String.format("👤 %s用户: %s (%s) - 操作人: %s", 
                                           statusText, user.getUsername(), user.getDisplayName(), operator);
            logger.info(statusMsg);
            LogWebSocketController.addLog("INFO", "UserService", statusMsg);
            
            // Session相关代码已删除，改用Sa-Token JWT认证
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 更改用户状态失败: ID=%d - %s", id, e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("更改用户状态失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public void resetPassword(Long id, String newPassword, String operator) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            String encodedPassword = passwordEncoder.encode(newPassword);
            userMapper.updatePassword(id, encodedPassword, operator);
            
            String resetMsg = String.format("🔑 重置密码: %s (%s) - 操作人: %s", 
                                          user.getUsername(), user.getDisplayName(), operator);
            logger.info(resetMsg);
            LogWebSocketController.addLog("INFO", "UserService", resetMsg);
            
            // Session相关代码已删除，改用Sa-Token JWT认证
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 重置密码失败: ID=%d - %s", id, e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("重置密码失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public void changePassword(Long id, String oldPassword, String newPassword) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            
            // 验证旧密码
            if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
                throw new IllegalArgumentException("原密码错误");
            }
            
            String encodedPassword = passwordEncoder.encode(newPassword);
            userMapper.updatePassword(id, encodedPassword, user.getUsername());
            
            String changeMsg = String.format("🔑 修改密码: %s (%s)", user.getUsername(), user.getDisplayName());
            logger.info(changeMsg);
            LogWebSocketController.addLog("INFO", "UserService", changeMsg);
            
        } catch (Exception e) {
            String errorMsg = String.format("❌ 修改密码失败: ID=%d - %s", id, e.getMessage());
            logger.error(errorMsg, e);
            LogWebSocketController.addLog("ERROR", "UserService", errorMsg);
            throw new RuntimeException("修改密码失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isUsernameExists(String username) {
        return userMapper.countByUsername(username) > 0;
    }
    
    @Override
    public boolean isUsernameExists(String username, Long excludeUserId) {
        return userMapper.countByUsernameExcludeId(username, excludeUserId) > 0;
    }
    
    @Override
    public List<User> searchUsers(String username, String realName, String role, Integer status) {
        return userMapper.searchUsers(username, realName, role, status);
    }
    
    // Session相关方法已删除，改用Sa-Token JWT认证

    @Override
    @Transactional
    public void updateLastLogin(Long userId, String loginIp) {
        try {
            userMapper.updateLastLogin(userId, LocalDateTime.now(), loginIp);
        } catch (Exception e) {
            logger.error("更新用户最后登录信息失败: userId={}, loginIp={}", userId, loginIp, e);
        }
    }

}
