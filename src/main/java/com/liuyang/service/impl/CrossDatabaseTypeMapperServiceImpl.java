package com.liuyang.service.impl;

import com.liuyang.dto.DatabaseType;
import com.liuyang.service.CrossDatabaseTypeMapperService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 跨数据库类型映射服务
 * 负责处理不同数据库之间的数据类型转换
 */
@Service
public class CrossDatabaseTypeMapperServiceImpl implements CrossDatabaseTypeMapperService {

    private static final Logger logger = LoggerFactory.getLogger(CrossDatabaseTypeMapperServiceImpl.class);
    
    // PostgreSQL 到 MySQL 的类型映射
    private static final Map<String, String> PG_TO_MYSQL_TYPE_MAP = new HashMap<>();
    
    static {
        // 字符串类型
        PG_TO_MYSQL_TYPE_MAP.put("character varying", "VARCHAR");
        PG_TO_MYSQL_TYPE_MAP.put("varchar", "VARCHAR");
        PG_TO_MYSQL_TYPE_MAP.put("character", "CHAR");
        PG_TO_MYSQL_TYPE_MAP.put("char", "CHAR");
        PG_TO_MYSQL_TYPE_MAP.put("text", "TEXT");
        
        // 数字类型
        PG_TO_MYSQL_TYPE_MAP.put("integer", "INT");
        PG_TO_MYSQL_TYPE_MAP.put("int4", "INT");
        PG_TO_MYSQL_TYPE_MAP.put("bigint", "BIGINT");
        PG_TO_MYSQL_TYPE_MAP.put("int8", "BIGINT");
        PG_TO_MYSQL_TYPE_MAP.put("smallint", "SMALLINT");
        PG_TO_MYSQL_TYPE_MAP.put("int2", "SMALLINT");
        PG_TO_MYSQL_TYPE_MAP.put("numeric", "DECIMAL");
        PG_TO_MYSQL_TYPE_MAP.put("decimal", "DECIMAL");
        PG_TO_MYSQL_TYPE_MAP.put("real", "FLOAT");
        PG_TO_MYSQL_TYPE_MAP.put("float4", "FLOAT");
        PG_TO_MYSQL_TYPE_MAP.put("double precision", "DOUBLE");
        PG_TO_MYSQL_TYPE_MAP.put("float8", "DOUBLE");
        
        // 布尔类型
        PG_TO_MYSQL_TYPE_MAP.put("boolean", "BOOLEAN");
        PG_TO_MYSQL_TYPE_MAP.put("bool", "BOOLEAN");
        
        // 日期时间类型
        PG_TO_MYSQL_TYPE_MAP.put("timestamp without time zone", "DATETIME");
        PG_TO_MYSQL_TYPE_MAP.put("timestamp", "DATETIME");
        PG_TO_MYSQL_TYPE_MAP.put("timestamp with time zone", "TIMESTAMP");
        PG_TO_MYSQL_TYPE_MAP.put("timestamptz", "TIMESTAMP");
        PG_TO_MYSQL_TYPE_MAP.put("date", "DATE");
        PG_TO_MYSQL_TYPE_MAP.put("time without time zone", "TIME");
        PG_TO_MYSQL_TYPE_MAP.put("time", "TIME");
        PG_TO_MYSQL_TYPE_MAP.put("time with time zone", "TIME");
        PG_TO_MYSQL_TYPE_MAP.put("timetz", "TIME");
        
        // 二进制类型
        PG_TO_MYSQL_TYPE_MAP.put("bytea", "LONGBLOB");
        
        // JSON类型
        PG_TO_MYSQL_TYPE_MAP.put("json", "JSON");
        PG_TO_MYSQL_TYPE_MAP.put("jsonb", "JSON");
        
        // UUID类型
        PG_TO_MYSQL_TYPE_MAP.put("uuid", "VARCHAR(36)");
        
        // 数组类型（PostgreSQL特有，转换为TEXT）
        PG_TO_MYSQL_TYPE_MAP.put("array", "TEXT");
    }
    
    /**
     * 映射数据类型
     * @param sourceType 源数据库类型
     * @param targetType 目标数据库类型
     * @param dataType 数据类型
     * @param columnSize 列大小
     * @param decimalDigits 小数位数
     * @return 映射后的数据类型
     */
    public String mapDataType(DatabaseType sourceType, DatabaseType targetType, 
                             String dataType, Integer columnSize, Integer decimalDigits) {
        
        if (sourceType == DatabaseType.POSTGRESQL && targetType == DatabaseType.MYSQL) {
            return mapPostgresToMySQL(dataType, columnSize, decimalDigits);
        }
        
        // 如果源类型和目标类型相同，使用原有的映射逻辑
        if (sourceType == targetType) {
            return mapSameDatabase(sourceType, dataType, columnSize, decimalDigits);
        }
        
        // 默认返回原类型
        return dataType.toUpperCase();
    }
    
    /**
     * PostgreSQL 到 MySQL 的类型映射
     */
    private String mapPostgresToMySQL(String dataType, Integer columnSize, Integer decimalDigits) {
        String lowerType = dataType.toLowerCase();
        
        // 处理数组类型
        if (lowerType.endsWith("[]") || lowerType.contains("array")) {
            return "TEXT";
        }
        
        // 处理特殊的变长类型
        if (lowerType.equals("character varying") || lowerType.equals("varchar")) {
            if (columnSize != null && columnSize > 0) {
                // 为了避免MySQL行大小限制问题，将超过500长度的VARCHAR转换为TEXT
                // MySQL行大小限制为65535字节，多个大VARCHAR字段容易超限
                if (columnSize > 500) {
                    logger.info("将大VARCHAR字段转换为TEXT: {}({}) -> TEXT (原因: 长度{}超过500，避免MySQL行大小限制)",
                               dataType, columnSize, columnSize);
                    return "TEXT";
                } else {
                    logger.debug("保持VARCHAR类型: {}({}) -> VARCHAR({})", dataType, columnSize, columnSize);
                    return "VARCHAR(" + columnSize + ")";
                }
            }
            logger.info("无长度信息的VARCHAR转换为TEXT: {} -> TEXT", dataType);
            return "TEXT";
        }
        
        if (lowerType.equals("character") || lowerType.equals("char")) {
            if (columnSize != null && columnSize > 0) {
                // MySQL CHAR最大长度是255，超过255转为TEXT
                if (columnSize > 255) {
                    return "TEXT";
                } else {
                    return "CHAR(" + columnSize + ")";
                }
            }
            return "CHAR(1)";
        }
        
        // 处理数字类型
        if (lowerType.equals("numeric") || lowerType.equals("decimal")) {
            if (columnSize != null && columnSize > 0 && decimalDigits != null && decimalDigits >= 0) {
                return "DECIMAL(" + columnSize + "," + decimalDigits + ")";
            } else if (columnSize != null && columnSize > 0) {
                return "DECIMAL(" + columnSize + ")";
            }
            return "DECIMAL";
        }
        
        // 查找映射表
        String mappedType = PG_TO_MYSQL_TYPE_MAP.get(lowerType);
        if (mappedType != null) {
            return mappedType;
        }
        
        // 未找到映射，返回TEXT作为兜底
        return "TEXT";
    }
    
    /**
     * 相同数据库的类型映射（使用原有逻辑）
     */
    private String mapSameDatabase(DatabaseType dbType, String dataType, Integer columnSize, Integer decimalDigits) {
        if (dbType == DatabaseType.POSTGRESQL) {
            return mapPostgreSQLType(dataType, columnSize, decimalDigits);
        } else if (dbType == DatabaseType.MYSQL) {
            return mapMySQLType(dataType, columnSize, decimalDigits);
        }
        return dataType.toUpperCase();
    }
    
    /**
     * PostgreSQL类型映射（原有逻辑）
     */
    private String mapPostgreSQLType(String dataType, Integer columnSize, Integer decimalDigits) {
        switch (dataType.toLowerCase()) {
            case "character varying":
            case "varchar":
                return columnSize != null ? "VARCHAR(" + columnSize + ")" : "VARCHAR";
            case "character":
            case "char":
                return columnSize != null ? "CHAR(" + columnSize + ")" : "CHAR";
            case "numeric":
                if (columnSize != null && columnSize > 0 && decimalDigits != null && decimalDigits >= 0) {
                    return "NUMERIC(" + columnSize + "," + decimalDigits + ")";
                } else if (columnSize != null && columnSize > 0) {
                    return "NUMERIC(" + columnSize + ")";
                }
                return "NUMERIC";
            case "integer":
                return "INTEGER";
            case "bigint":
                return "BIGINT";
            case "smallint":
                return "SMALLINT";
            case "boolean":
                return "BOOLEAN";
            case "timestamp without time zone":
                return "TIMESTAMP";
            case "timestamp with time zone":
                return "TIMESTAMPTZ";
            case "date":
                return "DATE";
            case "time without time zone":
                return "TIME";
            case "text":
                return "TEXT";
            default:
                return dataType.toUpperCase();
        }
    }
    
    /**
     * MySQL类型映射
     */
    private String mapMySQLType(String dataType, Integer columnSize, Integer decimalDigits) {
        switch (dataType.toLowerCase()) {
            case "varchar":
                return columnSize != null ? "VARCHAR(" + columnSize + ")" : "VARCHAR(255)";
            case "char":
                return columnSize != null ? "CHAR(" + columnSize + ")" : "CHAR(1)";
            case "decimal":
            case "numeric":
                if (columnSize != null && columnSize > 0 && decimalDigits != null && decimalDigits >= 0) {
                    return "DECIMAL(" + columnSize + "," + decimalDigits + ")";
                } else if (columnSize != null && columnSize > 0) {
                    return "DECIMAL(" + columnSize + ")";
                }
                return "DECIMAL";
            case "int":
            case "integer":
                return "INT";
            case "bigint":
                return "BIGINT";
            case "smallint":
                return "SMALLINT";
            case "boolean":
            case "bool":
                return "BOOLEAN";
            case "datetime":
                return "DATETIME";
            case "timestamp":
                return "TIMESTAMP";
            case "date":
                return "DATE";
            case "time":
                return "TIME";
            case "text":
                return "TEXT";
            default:
                return dataType.toUpperCase();
        }
    }
} 