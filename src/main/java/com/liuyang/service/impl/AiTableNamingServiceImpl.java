package com.liuyang.service.impl;

import com.liuyang.service.AiTableNamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * AI表名生成服务
 * 使用AI分析原表名，生成符合规范的新表名
 */
@Service
@ConditionalOnProperty(name = "app.ai.field-naming.enabled", havingValue = "true", matchIfMissing = false)
public class AiTableNamingServiceImpl implements AiTableNamingService {

    private static final Logger logger = LoggerFactory.getLogger(AiTableNamingService.class);

    @Autowired(required = false)
    @Qualifier("tableNamingChatClient")
    private ChatClient tableNamingChatClient;

    /**
     * 生成AI优化的表名
     * 
     * @param originalTableName 原表名
     * @param tableComment 表注释/说明
     * @param businessContext 业务上下文
     * @return 优化后的表名，以hc_开头，使用snake_case格式
     */
    public String generateOptimizedTableName(String originalTableName, String tableComment, String businessContext) {
        logger.info("开始AI表名生成: 原表名={}, 注释={}", originalTableName, tableComment);
        
        if (tableNamingChatClient == null) {
            logger.warn("TableNamingChatClient未初始化，使用降级方案");
            return generateFallbackTableName(originalTableName);
        }
        
        try {
            String prompt = buildTableNamingPrompt(originalTableName, tableComment, businessContext);
            logger.debug("AI表名生成提示词: {}", prompt);
            
            String aiResponse = tableNamingChatClient.prompt(prompt).call().content();
            logger.debug("AI原始响应: {}", aiResponse);
            
            String optimizedTableName = extractTableNameFromResponse(aiResponse);
            
            // 验证和规范化表名
            String finalTableName = validateAndNormalizeTableName(optimizedTableName, originalTableName);
            
            logger.info("AI表名生成完成: {} -> {}", originalTableName, finalTableName);
            return finalTableName;
            
        } catch (Exception e) {
            logger.error("AI表名生成失败，使用降级方案", e);
            return generateFallbackTableName(originalTableName);
        }
    }
    
    /**
     * 构建表名生成提示词
     */
    private String buildTableNamingPrompt(String originalTableName, String tableComment, String businessContext) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请为数据库表生成一个标准的英文表名。\n\n");

        prompt.append("要求：\n");
        prompt.append("1. 必须以 'hc_' 开头（hc代表系统前缀）\n");
        prompt.append("2. 使用 snake_case 格式（单词间用下划线分隔，全部小写）\n");
        prompt.append("3. 表名要准确反映表的业务用途\n");
        prompt.append("4. 使用标准英文单词，避免拼音\n");
        prompt.append("5. 保持简洁但要表意清晰\n");
        prompt.append("6. 通常使用复数形式（如users, orders, records）\n\n");

        prompt.append("原表名: ").append(originalTableName).append("\n");

        if (tableComment != null && !tableComment.trim().isEmpty()) {
            prompt.append("表说明: ").append(tableComment).append("\n");
        }

        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("业务上下文: ").append(businessContext).append("\n");
        }
        
        prompt.append("\n示例转换：\n");
        prompt.append("- 用户表 → hc_users\n");
        prompt.append("- 订单信息 → hc_orders\n");
        prompt.append("- 商品详情 → hc_product_details\n");
        prompt.append("- 系统配置 → hc_system_config\n");
        prompt.append("- 用户权限 → hc_user_permissions\n");
        prompt.append("- 操作日志 → hc_operation_logs\n\n");
        
        prompt.append("请只返回表名，不要包含其他说明文字。");
        
        return prompt.toString();
    }
    
    /**
     * 从AI响应中提取表名
     */
    private String extractTableNameFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }
        
        // 清理响应，提取表名
        String cleaned = response.trim()
                .replaceAll("```.*?```", "") // 移除代码块
                .replaceAll("[\"'`]", "") // 移除引号
                .replaceAll("表名[：:]?", "") // 移除"表名:"等前缀
                .replaceAll("建议[：:]?", "") // 移除"建议:"等前缀
                .replaceAll("\\s+", " ") // 规范化空格
                .trim();
        
        // 提取第一个看起来像表名的部分
        String[] lines = cleaned.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("hc_") && line.matches("^hc_[a-z][a-z0-9_]*$")) {
                return line;
            }
        }
        
        // 如果没有找到标准格式，尝试提取第一个单词
        String[] words = cleaned.split("\\s+");
        for (String word : words) {
            if (word.startsWith("hc_") && word.length() > 3) {
                return word.toLowerCase().replaceAll("[^a-z0-9_]", "");
            }
        }
        
        return null;
    }
    
    /**
     * 验证和规范化表名
     */
    private String validateAndNormalizeTableName(String tableName, String originalTableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return generateFallbackTableName(originalTableName);
        }
        
        // 规范化表名
        String normalized = tableName.toLowerCase().trim();
        
        // 确保以hc_开头
        if (!normalized.startsWith("hc_")) {
            if (normalized.startsWith("_")) {
                normalized = "hc" + normalized;
            } else {
                normalized = "hc_" + normalized;
            }
        }
        
        // 移除非法字符，只保留字母、数字和下划线
        normalized = normalized.replaceAll("[^a-z0-9_]", "");
        
        // 确保不以下划线结尾
        normalized = normalized.replaceAll("_+$", "");
        
        // 确保不包含连续的下划线
        normalized = normalized.replaceAll("_+", "_");
        
        // 验证最终格式
        if (!normalized.matches("^hc_[a-z][a-z0-9_]*$")) {
            logger.warn("AI生成的表名格式不正确: {}, 使用降级方案", normalized);
            return generateFallbackTableName(originalTableName);
        }
        
        return normalized;
    }
    
    /**
     * 生成降级方案表名
     */
    private String generateFallbackTableName(String originalTableName) {
        if (originalTableName == null || originalTableName.trim().isEmpty()) {
            return "hc_table";
        }
        
        // 简单的降级方案：转换为snake_case并添加hc_前缀
        String fallback = originalTableName.toLowerCase()
                .replaceAll("[^a-z0-9]", "_") // 非字母数字替换为下划线
                .replaceAll("_+", "_") // 合并连续下划线
                .replaceAll("^_+|_+$", ""); // 移除首尾下划线
        
        if (fallback.isEmpty()) {
            fallback = "table";
        }
        
        // 确保以字母开头
        if (!fallback.matches("^[a-z].*")) {
            fallback = "t_" + fallback;
        }
        
        String result = "hc_" + fallback;
        logger.info("使用降级方案生成表名: {} -> {}", originalTableName, result);
        
        return result;
    }
    
    /**
     * 检查AI服务是否可用
     */
    public boolean isAiServiceAvailable() {
        return tableNamingChatClient != null;
    }
}
