package com.liuyang.service.impl;

import com.liuyang.dto.DatabaseInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseConfigManagementService;
import com.liuyang.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库服务类
 * 现在从数据库配置管理服务中获取配置信息
 */
@Service
public class DatabaseServiceImpl implements DatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);
    
    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;
    
    /**
     * 获取PostgreSQL数据库连接（源数据库）
     */
    public Connection getConnection(String databaseName) throws SQLException {
        return getSourceConnection(databaseName);
    }
    
    /**
     * 获取源数据库连接
     */
    public Connection getSourceConnection(String databaseName) throws SQLException {
        logger.debug("获取源数据库连接: {}", databaseName);
        
        try {
            DatabaseConfigEntity config = databaseConfigManagementService.getSourceDatabaseConfig();
            String url = config.getJdbcUrl(databaseName);
            
            logger.debug("连接源数据库: {}", url);
            return DriverManager.getConnection(url, config.getUsername(), config.getPassword());
            
        } catch (Exception e) {
            logger.error("获取源数据库连接失败: {}", e.getMessage());
            throw new SQLException("获取源数据库连接失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取目标数据库连接
     */
    public Connection getTargetConnection(String databaseName) throws SQLException {
        logger.debug("获取目标数据库连接: {}", databaseName);
        
        try {
            DatabaseConfigEntity config = databaseConfigManagementService.getTargetDatabaseConfig();
            String url = config.getJdbcUrl(databaseName);
            
            logger.debug("连接目标数据库: {}", url);
            return DriverManager.getConnection(url, config.getUsername(), config.getPassword());
            
        } catch (Exception e) {
            logger.error("获取目标数据库连接失败: {}", e.getMessage());
            throw new SQLException("获取目标数据库连接失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取所有数据库列表（源数据库）
     */
    public List<DatabaseInfo> getAllDatabases() throws DatabaseException {
        logger.debug("获取所有源数据库列表");
        
        try {
            DatabaseConfigEntity config = databaseConfigManagementService.getSourceDatabaseConfig();
            return getDatabasesFromConfig(config);
        } catch (Exception e) {
            logger.error("获取数据库列表失败", e);
            throw new DatabaseException("获取数据库列表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取指定配置的数据库列表
     */
    private List<DatabaseInfo> getDatabasesFromConfig(DatabaseConfigEntity config) throws SQLException {
        List<DatabaseInfo> databases = new ArrayList<>();
        
        // 使用系统数据库进行连接
        String systemDb = getSystemDatabaseName(config.getDatabaseType());
        String url = config.getJdbcUrl(systemDb);
        
        try (Connection conn = DriverManager.getConnection(url, config.getUsername(), config.getPassword())) {
            String sql = getDatabaseListSql(config.getDatabaseType());
            
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                while (rs.next()) {
                    String dbName = rs.getString(1);
                    // 过滤系统数据库
                    if (!isSystemDatabase(dbName, config.getDatabaseType())) {
                        DatabaseInfo info = new DatabaseInfo();
                        info.setName(dbName);
                        info.setDatabaseType(convertToDatabaseType(config.getDatabaseType()));
                        databases.add(info);
                }
            }
            }
        }
        
        logger.debug("获取到 {} 个数据库", databases.size());
        return databases;
    }
    
    /**
     * 获取系统数据库名称
     */
    private String getSystemDatabaseName(String databaseType) {
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return "postgres";
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            return "information_schema";
        }
        return "";
    }
    
    /**
     * 获取数据库列表SQL
     */
    private String getDatabaseListSql(String databaseType) {
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return "SELECT datname FROM pg_database WHERE datistemplate = false";
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            return "SELECT SCHEMA_NAME FROM information_schema.SCHEMATA";
        }
        throw new IllegalArgumentException("不支持的数据库类型: " + databaseType);
    }
    
    /**
     * 检查是否为系统数据库
     */
    private boolean isSystemDatabase(String dbName, String databaseType) {
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return dbName.equals("template0") || dbName.equals("template1") || 
                   dbName.equals("postgres") || dbName.startsWith("rdsadmin");
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            return dbName.equals("information_schema") || dbName.equals("performance_schema") || 
                   dbName.equals("mysql") || dbName.equals("sys");
        }
        return false;
    }
    
    /**
     * 检查数据库是否存在
     */
    public boolean databaseExists(String databaseName) {
        logger.debug("检查数据库是否存在: {}", databaseName);
        
        try {
            DatabaseConfigEntity config = databaseConfigManagementService.getSourceDatabaseConfig();
            return databaseExistsInConfig(config, databaseName);
        } catch (Exception e) {
            logger.warn("检查数据库存在性失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 在指定配置中检查数据库是否存在
     */
    private boolean databaseExistsInConfig(DatabaseConfigEntity config, String databaseName) {
        try {
            String systemDb = getSystemDatabaseName(config.getDatabaseType());
            String url = config.getJdbcUrl(systemDb);
            
            try (Connection conn = DriverManager.getConnection(url, config.getUsername(), config.getPassword())) {
                String sql = getCheckDatabaseSql(config.getDatabaseType());
                
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, databaseName);
                try (ResultSet rs = stmt.executeQuery()) {
                        return rs.next() && rs.getInt(1) > 0;
                    }
                }
            }
        } catch (SQLException e) {
            logger.warn("检查数据库存在性失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取检查数据库存在性的SQL
     */
    private String getCheckDatabaseSql(String databaseType) {
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return "SELECT COUNT(*) FROM pg_database WHERE datname = ?";
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            return "SELECT COUNT(*) FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = ?";
        }
        throw new IllegalArgumentException("不支持的数据库类型: " + databaseType);
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection(String databaseName) {
        logger.debug("测试数据库连接: {}", databaseName);
        
        try {
            DatabaseConfigEntity config = databaseConfigManagementService.getSourceDatabaseConfig();
            return databaseConfigManagementService.testConnection(config, databaseName);
        } catch (Exception e) {
            logger.warn("测试数据库连接失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前使用的源数据库配置信息
     */
    public DatabaseConfigEntity getCurrentSourceConfig() {
        return databaseConfigManagementService.getSourceDatabaseConfig();
    }
    
    /**
     * 获取当前使用的目标数据库配置信息
     */
    public DatabaseConfigEntity getCurrentTargetConfig() {
        return databaseConfigManagementService.getTargetDatabaseConfig();
    }
    
    /**
     * 转换字符串为DatabaseType枚举
     */
    private DatabaseType convertToDatabaseType(String databaseType) {
        if ("POSTGRESQL".equalsIgnoreCase(databaseType)) {
            return DatabaseType.POSTGRESQL;
        } else if ("MYSQL".equalsIgnoreCase(databaseType)) {
            return DatabaseType.MYSQL;
        }
        // 默认返回PostgreSQL
        return DatabaseType.POSTGRESQL;
    }
}
