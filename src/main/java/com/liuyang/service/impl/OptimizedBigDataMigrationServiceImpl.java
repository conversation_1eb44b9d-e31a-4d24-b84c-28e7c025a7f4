package com.liuyang.service.impl;

import com.liuyang.config.DatabaseConfigForMybatis;
import com.liuyang.config.BigDataMigrationConfig;
import com.liuyang.dto.CopyTableRequest;
import com.liuyang.dto.TableStructure;
import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseConfigManagementService;
import com.liuyang.service.BigDataMigrationService;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.util.SnowflakeIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import com.liuyang.controller.LogWebSocketController;

import java.sql.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 大数据量迁移服务实现
 * 优化版本：解决日志混乱和进度显示问题，提升迁移性能
 */
@Service
@Primary
public class OptimizedBigDataMigrationServiceImpl implements BigDataMigrationService {
    
    private static final Logger logger = LoggerFactory.getLogger(OptimizedBigDataMigrationServiceImpl.class);
    
    @Autowired
    private DatabaseConfigForMybatis databaseConfig;
    
    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;

    @Autowired
    private BigDataMigrationConfig bigDataConfig;
    
    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;
    
    // 进度监控配置
    private static final int PROGRESS_REPORT_INTERVAL_SECONDS = 30; // 30秒报告一次进度
    private static final DecimalFormat PERCENT_FORMAT = new DecimalFormat("#0.00");
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    /**
     * 迁移进度跟踪类
     */
    public static class MigrationProgress {
        private final AtomicLong processedRows = new AtomicLong(0);
        private final AtomicLong totalRows = new AtomicLong(0);
        private final long startTime = System.currentTimeMillis();
        private volatile int currentBatch = 0;
        private volatile int totalBatches = 0;
        
        public void addProcessed(long count) {
            processedRows.addAndGet(count);
        }
        
        public long getProcessedRows() {
            return processedRows.get();
        }
        
        public void setTotalRows(long total) {
            totalRows.set(total);
        }
        
        public long getTotalRows() {
            return totalRows.get();
        }
        
        public void setCurrentBatch(int batch) {
            this.currentBatch = batch;
        }
        
        public void setTotalBatches(int total) {
            this.totalBatches = total;
        }
        
        public double getProgressPercentage() {
            long total = totalRows.get();
            if (total == 0) return 0.0;
            return (double) processedRows.get() / total * 100.0;
        }
        
        public long getElapsedTimeMs() {
            return System.currentTimeMillis() - startTime;
        }
        
        public double getSpeed() {
            long elapsed = getElapsedTimeMs();
            if (elapsed == 0) return 0.0;
            return (double) processedRows.get() * 1000.0 / elapsed;
        }
        
        public long getEstimatedRemainingMs() {
            long processed = processedRows.get();
            long total = totalRows.get();
            if (processed == 0) return 0;
            
            long elapsed = getElapsedTimeMs();
            long remaining = total - processed;
            return (long) ((double) remaining * elapsed / processed);
        }
        
        public String getProgressReport(String migrationId) {
            return String.format(
                "[%s] 进度: %s/%s (%.2f%%) | 批次: %d/%d | 速度: %.0f 条/秒 | 已用时: %s | 预计剩余: %s",
                migrationId,
                processedRows.get(),
                totalRows.get(),
                getProgressPercentage(),
                currentBatch,
                totalBatches,
                getSpeed(),
                formatDuration(getElapsedTimeMs()),
                formatDuration(getEstimatedRemainingMs())
            );
        }
        
        private String formatDuration(long ms) {
            long seconds = ms / 1000;
            long minutes = seconds / 60;
            long hours = minutes / 60;
            
            if (hours > 0) {
                return String.format("%d小时%d分钟", hours, minutes % 60);
            } else if (minutes > 0) {
                return String.format("%d分钟%d秒", minutes, seconds % 60);
            } else {
                return String.format("%d秒", seconds);
            }
        }
    }
    
    /**
     * 优化的大数据量迁移主方法
     */
    @Override
    public void migrateLargeDataset(CopyTableRequest request, TableStructure sourceStructure, 
                                   TableStructure targetStructure) {
        
        String migrationId = generateMigrationId();
        String startMsg = String.format("🚀 [%s] 开始大数据量迁移: %s -> %s",
                                       migrationId, request.getTableName(), request.getTargetTableName());
        logger.info(startMsg);
        LogWebSocketController.addLog("INFO", "OptimizedBigDataMigration", startMsg);
        
        MigrationProgress progress = new MigrationProgress();
        ScheduledExecutorService progressMonitor = null;
        
        try {
            // 1. 获取数据总量
            long totalRows = getTotalRows(request);
            progress.setTotalRows(totalRows);
            
            String totalMsg = String.format("📊 [%s] 数据总量: %,d 条", migrationId, totalRows);
            logger.info(totalMsg);
            LogWebSocketController.addLog("INFO", "OptimizedBigDataMigration", totalMsg);

            if (totalRows == 0) {
                String skipMsg = String.format("✅ [%s] 源表无数据，跳过迁移", migrationId);
                logger.info(skipMsg);
                LogWebSocketController.addLog("INFO", "OptimizedBigDataMigration", skipMsg);
                return;
            }

            // 2. 计算优化的迁移策略（使用更保守的策略）
            int batchSize = calculateOptimalBatchSize(totalRows);
            int totalBatches = (int) Math.ceil((double) totalRows / batchSize);
            progress.setTotalBatches(totalBatches);

            String strategyMsg = String.format("📋 [%s] 迁移策略: 批次大小=%d, 总批次数=%d (串行执行，避免并发复杂性)",
                                             migrationId, batchSize, totalBatches);
            logger.info(strategyMsg);
            LogWebSocketController.addLog("INFO", "OptimizedBigDataMigration", strategyMsg);
            
            // 3. 启动进度监控线程
            progressMonitor = startProgressMonitor(migrationId, progress);
            
            // 4. 执行串行批次迁移
            executeSerialMigration(migrationId, request, sourceStructure, targetStructure, 
                                 batchSize, totalBatches, progress);
            
            // 5. 最终统计
            long finalProcessed = progress.getProcessedRows();
            long duration = progress.getElapsedTimeMs();
            
            logger.info("✅ [{}] 大数据量迁移完成: 处理 {}/{} 条记录，耗时 {}，平均速度 {:.0f} 条/秒", 
                       migrationId, finalProcessed, totalRows, 
                       progress.formatDuration(duration), progress.getSpeed());
            
        } catch (Exception e) {
            logger.error("❌ [{}] 大数据量迁移失败", migrationId, e);
            throw new DatabaseException("大数据量迁移失败: " + e.getMessage(), e);
        } finally {
            if (progressMonitor != null) {
                progressMonitor.shutdown();
            }
        }
    }
    
    /**
     * 生成迁移ID
     */
    private String generateMigrationId() {
        return "MIG-" + System.currentTimeMillis() % 100000;
    }
    
    /**
     * 计算最优批次大小
     */
    private int calculateOptimalBatchSize(long totalRows) {
        // 使用更保守的批次大小，确保稳定性
        if (totalRows < 50000) {
            return 1000;  // 小数据量：1000条/批次
        } else if (totalRows < 500000) {
            return 2000;  // 中等数据量：2000条/批次
        } else if (totalRows < 2000000) {
            return 3000;  // 大数据量：3000条/批次
        } else {
            return 5000;  // 超大数据量：5000条/批次
        }
    }
    
    /**
     * 启动进度监控线程
     */
    private ScheduledExecutorService startProgressMonitor(String migrationId, MigrationProgress progress) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ProgressMonitor-" + migrationId);
            t.setDaemon(true);
            return t;
        });
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                logger.info("📈 {}", progress.getProgressReport(migrationId));
            } catch (Exception e) {
                logger.warn("进度报告失败: {}", e.getMessage());
            }
        }, PROGRESS_REPORT_INTERVAL_SECONDS, PROGRESS_REPORT_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        return scheduler;
    }
    
    /**
     * 执行串行迁移（避免并发复杂性）
     */
    private void executeSerialMigration(String migrationId, CopyTableRequest request,
                                      TableStructure sourceStructure, TableStructure targetStructure,
                                      int batchSize, int totalBatches, MigrationProgress progress) throws SQLException {

        logger.info("🔄 [{}] 开始串行批次迁移，共 {} 个批次", migrationId, totalBatches);

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            progress.setCurrentBatch(batchIndex + 1);

            long offset = (long) batchIndex * batchSize;
            long processed = processSingleBatch(migrationId, request, sourceStructure, targetStructure,
                                              offset, batchSize, batchIndex + 1, totalBatches);

            progress.addProcessed(processed);

            // 每10个批次输出一次详细进度
            if ((batchIndex + 1) % 10 == 0 || batchIndex == totalBatches - 1) {
                logger.info("📊 [{}] 批次进度: {}/{} 完成，累计处理 {} 条记录",
                           migrationId, batchIndex + 1, totalBatches, progress.getProcessedRows());
            }
        }
    }

    /**
     * 处理单个批次
     */
    private long processSingleBatch(String migrationId, CopyTableRequest request,
                                  TableStructure sourceStructure, TableStructure targetStructure,
                                  long offset, int batchSize, int currentBatch, int totalBatches) throws SQLException {

        logger.debug("🔧 [{}] 处理批次 {}/{}: offset={}, size={}",
                    migrationId, currentBatch, totalBatches, offset, batchSize);

        try (Connection sourceConn = getConnection(request.getSourceDbType(), request.getSourceDatabase());
             Connection targetConn = getConnection(request.getTargetDbType(), request.getTargetDatabase())) {

            // 优化连接设置
            optimizeConnection(sourceConn, targetConn);

            // 生成SQL
            String selectSql = generateSelectSql(request.getTableName(), sourceStructure, offset, batchSize);
            String insertSql = generateInsertSql(request.getTargetTableName(), targetStructure);

            logger.debug("🔍 [{}] 批次 {} SQL: {}", migrationId, currentBatch, selectSql);

            long processed = 0;

            try (PreparedStatement selectStmt = sourceConn.prepareStatement(selectSql);
                 PreparedStatement insertStmt = targetConn.prepareStatement(insertSql)) {

                // 设置查询超时
                selectStmt.setQueryTimeout(bigDataConfig.getQueryTimeoutSeconds());
                insertStmt.setQueryTimeout(bigDataConfig.getBatchTimeoutSeconds());

                // 执行查询
                try (ResultSet rs = selectStmt.executeQuery()) {

                    while (rs.next()) {
                        // 设置插入参数
                        setInsertParameters(rs, insertStmt, sourceStructure, targetStructure, request);
                        insertStmt.addBatch();
                        processed++;
                    }

                    // 执行批量插入
                    if (processed > 0) {
                        int[] results = insertStmt.executeBatch();
                        targetConn.commit();

                        logger.debug("✅ [{}] 批次 {} 完成: 处理 {} 条记录",
                                   migrationId, currentBatch, processed);
                    }
                }

            } catch (SQLException e) {
                targetConn.rollback();
                logger.error("❌ [{}] 批次 {} 执行失败: {}", migrationId, currentBatch, e.getMessage());
                throw new SQLException(String.format("批次 %d 执行失败: %s", currentBatch, e.getMessage()), e);
            }

            return processed;

        } catch (Exception e) {
            logger.error("❌ [{}] 批次 {} 处理失败", migrationId, currentBatch, e);
            throw new SQLException(String.format("批次 %d 处理失败: %s", currentBatch, e.getMessage()), e);
        }
    }

    /**
     * 优化数据库连接
     */
    private void optimizeConnection(Connection sourceConn, Connection targetConn) throws SQLException {
        // 源连接优化
        sourceConn.setAutoCommit(false);
        sourceConn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);

        // 目标连接优化
        targetConn.setAutoCommit(false);
        targetConn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);

        // MySQL特定优化
        if (targetConn.getMetaData().getDatabaseProductName().toLowerCase().contains("mysql")) {
            try (Statement stmt = targetConn.createStatement()) {
                stmt.execute("SET SESSION sql_mode = 'NO_AUTO_VALUE_ON_ZERO'");
                stmt.execute("SET SESSION foreign_key_checks = 0");
                stmt.execute("SET SESSION unique_checks = 0");
                stmt.execute("SET SESSION autocommit = 0");
            }
        }
    }

    /**
     * 生成查询SQL
     */
    private String generateSelectSql(String tableName, TableStructure structure, long offset, int batchSize) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");

        // 添加所有字段
        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(structure.getColumns().get(i).getColumnName());
        }

        sql.append(" FROM ").append(tableName);
        sql.append(" ORDER BY ").append(structure.getColumns().get(0).getColumnName()); // 使用第一个字段排序
        sql.append(" LIMIT ").append(batchSize);
        sql.append(" OFFSET ").append(offset);

        return sql.toString();
    }

    /**
     * 生成插入SQL
     */
    private String generateInsertSql(String tableName, TableStructure structure) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        // 添加字段名
        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(structure.getColumns().get(i).getColumnName());
        }

        sql.append(") VALUES (");

        // 添加参数占位符
        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append("?");
        }

        sql.append(")");
        return sql.toString();
    }

    /**
     * 设置插入参数
     */
    private void setInsertParameters(ResultSet rs, PreparedStatement insertStmt,
                                   TableStructure sourceStructure, TableStructure targetStructure,
                                   CopyTableRequest request) throws SQLException {
        int paramIndex = 1;

        // 如果启用双主键设计，先设置新主键
        if (request.isUseDualPrimaryKey()) {
            long newId = snowflakeIdGenerator.nextId();
            insertStmt.setLong(paramIndex++, newId);
        }

        // 设置源表字段对应的参数
        int sourceFieldIndex = 1;
        for (int i = (request.isUseDualPrimaryKey() ? 1 : 0); i < targetStructure.getColumns().size(); i++) {
            ColumnInfo targetColumn = targetStructure.getColumns().get(i);

            try {
                Object value = rs.getObject(sourceFieldIndex++);

                // 处理数据类型转换
                if (value != null) {
                    value = convertDataType(value, targetColumn);
                }

                insertStmt.setObject(paramIndex++, value);

            } catch (SQLException e) {
                logger.error("设置参数失败 - 参数索引: {}, 目标字段: {}, 错误: {}",
                           paramIndex - 1, targetColumn.getColumnName(), e.getMessage());
                throw e;
            }
        }
    }

    /**
     * 数据类型转换
     */
    private Object convertDataType(Object value, ColumnInfo targetColumn) {
        if (value == null) return null;

        String targetType = targetColumn.getDataType().toLowerCase();

        // PostgreSQL到MySQL的常见类型转换
        switch (targetType) {
            case "tinyint":
            case "smallint":
            case "mediumint":
            case "int":
            case "integer":
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                }
                break;
            case "bigint":
                if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
                break;
            case "varchar":
            case "text":
            case "longtext":
                return value.toString();
            case "decimal":
            case "numeric":
                if (value instanceof Number) {
                    return value;
                }
                break;
            case "datetime":
            case "timestamp":
                if (value instanceof Timestamp) {
                    return value;
                }
                break;
        }

        return value;
    }

    /**
     * 获取数据总量
     */
    private long getTotalRows(CopyTableRequest request) throws SQLException {
        try (Connection conn = getConnection(request.getSourceDbType(), request.getSourceDatabase())) {
            String sql = "SELECT COUNT(*) FROM " + request.getTableName();
            try (PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        }
        return 0;
    }

    /**
     * 获取数据库连接 - 修复版本
     */
    private Connection getConnection(DatabaseType dbType, String database) throws SQLException {
        try {
            if (dbType == DatabaseType.MYSQL) {
                // 获取目标数据库配置（MySQL）
                DatabaseConfigEntity targetConfig = databaseConfigManagementService.getTargetDatabaseConfig("dev");
                String url = String.format("****************************************************************************************************************************",
                                         targetConfig.getHost(), targetConfig.getPort(), database);
                return DriverManager.getConnection(url, targetConfig.getUsername(), targetConfig.getPassword());
            } else {
                // 获取源数据库配置（PostgreSQL）
                DatabaseConfigEntity sourceConfig = databaseConfigManagementService.getSourceDatabaseConfig("dev");
                String url = String.format("***************************************************************",
                                         sourceConfig.getHost(), sourceConfig.getPort(), database);
                return DriverManager.getConnection(url, sourceConfig.getUsername(), sourceConfig.getPassword());
            }
        } catch (Exception e) {
            logger.error("❌ 获取数据库连接失败 - 类型: {}, 数据库: {}, 错误: {}", dbType, database, e.getMessage());
            throw new SQLException("获取数据库连接失败: " + e.getMessage(), e);
        }
    }
}
