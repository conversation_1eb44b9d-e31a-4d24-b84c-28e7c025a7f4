package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.EnhancedFieldMapping;
import com.liuyang.dto.FieldType;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import com.liuyang.service.EnhancedFieldMappingService;
import com.liuyang.util.SnakeCaseConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 增强的字段映射分析服务
 * 支持基础字段模板和AI优化
 */
@Service
public class EnhancedFieldMappingServiceImpl implements EnhancedFieldMappingService {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedFieldMappingService.class);

    @Autowired(required = false)
    private AiFieldNamingService aiFieldNamingService;

    // 英文字段名检测模式
    private static final Pattern ENGLISH_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");
    
    // Snake case检测模式
    private static final Pattern SNAKE_CASE_PATTERN = Pattern.compile("^[a-z][a-z0-9_]*$");

    /**
     * 分析并生成增强的字段映射
     */
    public List<EnhancedFieldMapping> analyzeFieldMappings(TableStructure sourceTable, String businessContext) {
        List<EnhancedFieldMapping> enhancedMappings = new ArrayList<>();
        
        logger.info("开始分析表 {} 的字段映射，共 {} 个字段", 
                   sourceTable.getTableName(), sourceTable.getColumns().size());

        for (ColumnInfo column : sourceTable.getColumns()) {
            EnhancedFieldMapping mapping = analyzeField(column, businessContext);
            enhancedMappings.add(mapping);
        }

        logger.info("字段映射分析完成，生成 {} 个映射", enhancedMappings.size());
        return enhancedMappings;
    }

    /**
     * 分析单个字段
     */
    private EnhancedFieldMapping analyzeField(ColumnInfo column, String businessContext) {
        String sourceField = column.getColumnName();
        EnhancedFieldMapping mapping = new EnhancedFieldMapping();
        
        // 设置基本信息
        mapping.setSourceField(sourceField);
        mapping.setSourceType(column.getDataType());
        mapping.setTargetType(column.getDataType());
        mapping.setSourceSize(column.getColumnSize());
        mapping.setTargetSize(column.getColumnSize());
        mapping.setNullable(column.isNullable());
        mapping.setBusinessMeaning(column.getComment());

        // 检查字段特征
        mapping.setEnglishName(isEnglishFieldName(sourceField));
        mapping.setSnakeCase(isSnakeCase(sourceField));

        // 使用AI优化所有字段（确保规范的命名）
        handleAiOptimizationField(mapping, sourceField, businessContext);

        return mapping;
    }


    /**
     * 处理需要AI优化的字段
     */
    private void handleAiOptimizationField(EnhancedFieldMapping mapping, String sourceField, String businessContext) {
        try {
            // 特殊处理：主键id字段直接优化为business_id
            if ("id".equalsIgnoreCase(sourceField) && isPrimaryKeyField(mapping)) {
                mapping.setTargetField("business_id");
                mapping.setFieldType(FieldType.BUSINESS_KEY);
                mapping.setOptimizationNote("主键ID字段优化为业务主键");
                mapping.setConfidence(1.0);
                mapping.setImportanceLevel("HIGH");
                mapping.setComment("业务主键 - 源表主键映射");
                mapping.setEnglishName(true);
                mapping.setSnakeCase(true);
                logger.info("主键字段特殊优化: {} -> business_id", sourceField);
                return;
            }

            if (aiFieldNamingService != null) {
                // 使用AI优化字段名
                String optimizedName = aiFieldNamingService.optimizeFieldName(
                    sourceField,
                    mapping.getSourceType(),
                    mapping.getBusinessMeaning(),
                    businessContext
                );
                
                if (optimizedName != null && !optimizedName.trim().isEmpty()) {
                    mapping.setTargetField(optimizedName);
                    mapping.setFieldType(FieldType.AI_OPTIMIZED);
                    
                    if (optimizedName.equals(sourceField)) {
                        mapping.setOptimizationNote("AI分析确认: 字段名已符合规范");
                        mapping.setComment("AI确认字段 - " + (mapping.getBusinessMeaning() != null ? mapping.getBusinessMeaning() : "字段名规范"));
                        logger.debug("AI确认字段已规范: {}", sourceField);
                    } else {
                        mapping.setOptimizationNote("AI分析优化: " + sourceField + " -> " + optimizedName);
                        mapping.setComment("AI优化字段 - " + (mapping.getBusinessMeaning() != null ? mapping.getBusinessMeaning() : "业务字段"));
                        logger.info("AI优化字段: {} -> {}", sourceField, optimizedName);
                    }
                    
                    mapping.setConfidence(0.8);
                    mapping.setImportanceLevel("HIGH");
                    mapping.setEnglishName(isEnglishFieldName(optimizedName));
                    mapping.setSnakeCase(isSnakeCase(optimizedName));
                } else {
                    // AI优化失败，使用降级方案
                    handleFallbackOptimization(mapping, sourceField);
                }
            } else {
                // AI服务不可用，使用降级方案
                handleFallbackOptimization(mapping, sourceField);
            }
        } catch (Exception e) {
            logger.warn("AI字段优化失败: {}, 使用降级方案", sourceField, e);
            handleFallbackOptimization(mapping, sourceField);
        }
    }

    /**
     * 处理降级优化方案
     */
    private void handleFallbackOptimization(EnhancedFieldMapping mapping, String sourceField) {
        String fallbackName = SnakeCaseConverter.toSnakeCase(sourceField);
        
        mapping.setTargetField(fallbackName);
        mapping.setFieldType(FieldType.MANUAL);
        mapping.setOptimizationNote("降级方案: 自动转换为snake_case格式");
        mapping.setConfidence(0.6);
        mapping.setImportanceLevel("MEDIUM");
        mapping.setComment("自动转换字段 - 建议人工审核");
        mapping.setEnglishName(isEnglishFieldName(fallbackName));
        mapping.setSnakeCase(isSnakeCase(fallbackName));
        
        logger.debug("降级优化: {} -> {}", sourceField, fallbackName);
    }

    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKeyField(EnhancedFieldMapping mapping) {
        // 通过字段类型或业务含义判断是否为主键
        String businessMeaning = mapping.getBusinessMeaning();
        if (businessMeaning != null) {
            String meaning = businessMeaning.toLowerCase();
            return meaning.contains("主键") || meaning.contains("primary") ||
                   meaning.contains("id") || meaning.contains("标识");
        }
        return false;
    }

    /**
     * 检查是否为英文字段名
     */
    private boolean isEnglishFieldName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        return ENGLISH_PATTERN.matcher(fieldName).matches();
    }

    /**
     * 检查是否为snake_case格式
     */
    private boolean isSnakeCase(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        return SNAKE_CASE_PATTERN.matcher(fieldName).matches();
    }

    /**
     * 获取字段映射统计信息
     */
    @Override
    public FieldMappingStatistics getStatistics(List<EnhancedFieldMapping> mappings) {
        FieldMappingStatistics stats = new FieldMappingStatistics();
        
        for (EnhancedFieldMapping mapping : mappings) {
            stats.totalFields++;
            
            switch (mapping.getFieldType()) {
                case AI_OPTIMIZED:
                    stats.aiOptimizedFields++;
                    break;
                case MANUAL:
                    stats.manualFields++;
                    break;
                default:
                    stats.unknownFields++;
                    break;
            }
            
            if (mapping.isEnglishName()) {
                stats.englishFields++;
            }
            
            if (mapping.isSnakeCase()) {
                stats.snakeCaseFields++;
            }
            
            if (mapping.getConfidence() >= 0.8) {
                stats.highConfidenceFields++;
            }
        }
        
        return stats;
    }

    /**
     * 字段映射统计信息
     */
    public static class FieldMappingStatistics {
        public int totalFields = 0;
        public int aiOptimizedFields = 0;
        public int manualFields = 0;
        public int unknownFields = 0;
        public int englishFields = 0;
        public int snakeCaseFields = 0;
        public int highConfidenceFields = 0;
        
        public double getOptimizationRate() {
            return totalFields > 0 ? (double) aiOptimizedFields / totalFields : 0.0;
        }
        
        public double getStandardComplianceRate() {
            return totalFields > 0 ? (double) snakeCaseFields / totalFields : 0.0;
        }
    }
}
