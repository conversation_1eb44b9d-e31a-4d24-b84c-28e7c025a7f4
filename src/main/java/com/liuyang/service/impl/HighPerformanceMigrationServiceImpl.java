package com.liuyang.service.impl;

import com.liuyang.config.DatabaseConfigForMybatis;
import com.liuyang.config.BigDataMigrationConfig;
import com.liuyang.config.HighPerformanceMigrationConfig;
import com.liuyang.dto.CopyTableRequest;
import com.liuyang.dto.TableStructure;
import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.exception.DatabaseException;
import com.liuyang.service.DatabaseConfigManagementService;
import com.liuyang.service.BigDataMigrationService;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.util.SnowflakeIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.liuyang.controller.LogWebSocketController;

import java.sql.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List;
import java.util.ArrayList;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

/**
 * 企业级高性能数据迁移服务
 * 支持百万级数据的高速迁移
 */
@Service("highPerformanceMigrationService")
public class HighPerformanceMigrationServiceImpl implements BigDataMigrationService {
    
    private static final Logger logger = LoggerFactory.getLogger(HighPerformanceMigrationServiceImpl.class);
    
    @Autowired
    private DatabaseConfigManagementService databaseConfigManagementService;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private HighPerformanceMigrationConfig highPerfConfig;
    
    // 高性能配置常量（将从配置文件读取）
    
    /**
     * 分片信息
     */
    public static class DataShard {
        private final long startId;
        private final long endId;
        private final long estimatedRows;
        private final int shardIndex;
        
        public DataShard(long startId, long endId, long estimatedRows, int shardIndex) {
            this.startId = startId;
            this.endId = endId;
            this.estimatedRows = estimatedRows;
            this.shardIndex = shardIndex;
        }
        
        // Getters
        public long getStartId() { return startId; }
        public long getEndId() { return endId; }
        public long getEstimatedRows() { return estimatedRows; }
        public int getShardIndex() { return shardIndex; }
    }
    
    /**
     * 迁移统计信息
     */
    public static class MigrationStats {
        private final AtomicLong totalProcessed = new AtomicLong(0);
        private final AtomicLong totalRows = new AtomicLong(0);
        private final AtomicInteger completedShards = new AtomicInteger(0);
        private final AtomicInteger totalShards = new AtomicInteger(0);
        private final long startTime = System.currentTimeMillis();
        private volatile long lastReportTime = startTime;
        private volatile long lastReportCount = 0;
        
        public void addProcessed(long count) {
            totalProcessed.addAndGet(count);
        }
        
        public void incrementCompletedShards() {
            completedShards.incrementAndGet();
        }
        
        public long getProcessedRows() { return totalProcessed.get(); }
        public long getTotalRows() { return totalRows.get(); }
        public void setTotalRows(long total) { totalRows.set(total); }
        public void setTotalShards(int total) { totalShards.set(total); }
        
        public double getProgressPercentage() {
            long total = totalRows.get();
            return total == 0 ? 0.0 : (double) totalProcessed.get() / total * 100.0;
        }
        
        public double getCurrentSpeed() {
            long currentTime = System.currentTimeMillis();
            long currentCount = totalProcessed.get();
            
            if (currentTime == lastReportTime) return 0.0;
            
            double speed = (double) (currentCount - lastReportCount) * 1000.0 / (currentTime - lastReportTime);
            
            lastReportTime = currentTime;
            lastReportCount = currentCount;
            
            return speed;
        }
        
        public long getElapsedTimeMs() {
            return System.currentTimeMillis() - startTime;
        }
        
        public String getProgressReport(String migrationId) {
            return String.format(
                "[%s] 🚀 进度: %,d/%,d (%.2f%%) | 分片: %d/%d | 当前速度: %,.0f 条/秒 | 已用时: %s",
                migrationId,
                getProcessedRows(),
                getTotalRows(),
                getProgressPercentage(),
                completedShards.get(),
                totalShards.get(),
                getCurrentSpeed(),
                formatDuration(getElapsedTimeMs())
            );
        }
        
        private String formatDuration(long ms) {
            long seconds = ms / 1000;
            long minutes = seconds / 60;
            long hours = minutes / 60;
            
            if (hours > 0) {
                return String.format("%d小时%d分钟", hours, minutes % 60);
            } else if (minutes > 0) {
                return String.format("%d分钟%d秒", minutes, seconds % 60);
            } else {
                return String.format("%d秒", seconds);
            }
        }
    }
    
    /**
     * 高性能数据迁移主方法
     */
    @Override
    public void migrateLargeDataset(CopyTableRequest request, TableStructure sourceStructure, 
                                   TableStructure targetStructure) {
        
        String migrationId = generateMigrationId();
        String startMsg = String.format("🚀 [%s] 启动企业级高性能数据迁移: %s -> %s",
                                       migrationId, request.getTableName(), request.getTargetTableName());
        logger.info(startMsg);
        LogWebSocketController.addLog("INFO", "HighPerformanceMigration", startMsg);
        
        MigrationStats stats = new MigrationStats();
        HikariDataSource sourcePool = null;
        HikariDataSource targetPool = null;
        ScheduledExecutorService progressMonitor = null;
        ExecutorService migrationExecutor = null;
        
        try {
            // 1. 创建高性能连接池
            sourcePool = createHighPerformanceDataSource(request.getSourceDbType(), request.getSourceDatabase());
            targetPool = createHighPerformanceDataSource(request.getTargetDbType(), request.getTargetDatabase());
            
            // 2. 分析数据分布，创建分片策略
            List<DataShard> shards = createDataShards(sourcePool, request, migrationId);
            stats.setTotalShards(shards.size());

            long totalRows = shards.stream().mapToLong(DataShard::getEstimatedRows).sum();
            stats.setTotalRows(totalRows);

            String analysisMsg = String.format("📊 [%s] 数据分析完成: 总计 %,d 条记录，分为 %d 个分片",
                                              migrationId, totalRows, shards.size());
            logger.info(analysisMsg);
            LogWebSocketController.addLog("INFO", "HighPerformanceMigration", analysisMsg);

            // 3. 启动进度监控
            progressMonitor = startProgressMonitor(migrationId, stats);

            // 4. 创建优化的线程池
            int threadCount = calculateOptimalThreadCount(totalRows, shards.size());
            migrationExecutor = createOptimizedThreadPool(threadCount, migrationId);
            
            logger.info("🔧 [{}] 启动 {} 个并行线程进行分片迁移", migrationId, threadCount);
            
            // 5. 并行执行分片迁移
            executeParallelShardMigration(migrationId, request, sourceStructure, targetStructure,
                                        shards, sourcePool, targetPool, migrationExecutor, stats);
            
            // 6. 等待所有分片完成
            migrationExecutor.shutdown();
            if (!migrationExecutor.awaitTermination(3600, TimeUnit.SECONDS)) {
                logger.warn("⚠️ [{}] 迁移超时，强制终止", migrationId);
                migrationExecutor.shutdownNow();
            }
            
            // 7. 最终统计
            long finalProcessed = stats.getProcessedRows();
            long duration = stats.getElapsedTimeMs();
            double avgSpeed = finalProcessed * 1000.0 / duration;
            
            String completeMsg = String.format("✅ [%s] 高性能迁移完成: 处理 %,d/%,d 条记录，耗时 %s，平均速度 %.0f 条/秒",
                                              migrationId, finalProcessed, totalRows,
                                              stats.formatDuration(duration), avgSpeed);
            logger.info(completeMsg);
            LogWebSocketController.addLog("INFO", "HighPerformanceMigration", completeMsg);
            
        } catch (Exception e) {
            logger.error("❌ [{}] 高性能迁移失败", migrationId, e);
            throw new DatabaseException("高性能迁移失败: " + e.getMessage(), e);
        } finally {
            // 清理资源
            if (progressMonitor != null) progressMonitor.shutdown();
            if (migrationExecutor != null && !migrationExecutor.isShutdown()) migrationExecutor.shutdownNow();
            if (sourcePool != null) sourcePool.close();
            if (targetPool != null) targetPool.close();
        }
    }
    
    /**
     * 创建高性能数据源
     */
    private HikariDataSource createHighPerformanceDataSource(DatabaseType dbType, String database) throws SQLException {
        DatabaseConfigEntity config;
        String jdbcUrl;
        
        if (dbType == DatabaseType.MYSQL) {
            config = databaseConfigManagementService.getTargetDatabaseConfig("dev");
            jdbcUrl = String.format(
                "jdbc:mysql://%s:%d/%s?" +
                "useSSL=false&" +
                "serverTimezone=UTC&" +
                "allowPublicKeyRetrieval=true&" +
                "rewriteBatchedStatements=true&" +           // 批量重写优化
                "useServerPrepStmts=true&" +                 // 服务器端预编译
                "cachePrepStmts=true&" +                     // 缓存预编译语句
                "prepStmtCacheSize=500&" +                   // 预编译缓存大小
                "prepStmtCacheSqlLimit=2048&" +              // SQL缓存限制
                "useLocalSessionState=true&" +               // 本地会话状态
                "useLocalTransactionState=true&" +           // 本地事务状态
                "connectTimeout=60000&" +                    // 连接超时
                "socketTimeout=300000",                      // Socket超时
                config.getHost(), config.getPort(), database
            );
        } else {
            config = databaseConfigManagementService.getSourceDatabaseConfig("dev");
            jdbcUrl = String.format(
                "jdbc:postgresql://%s:%d/%s?" +
                "connectTimeout=60&" +
                "socketTimeout=300&" +
                "loginTimeout=60&" +
                "prepareThreshold=5&" +                      // 预编译阈值
                "defaultRowFetchSize=10000",                 // 默认获取行数
                config.getHost(), config.getPort(), database
            );
        }
        
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(jdbcUrl);
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        
        // 高性能连接池配置
        hikariConfig.setMaximumPoolSize(highPerfConfig.getConnectionPoolSize());
        hikariConfig.setMinimumIdle(highPerfConfig.getConnectionPoolSize() / 2);
        hikariConfig.setConnectionTimeout(highPerfConfig.getConnectionTimeout());
        hikariConfig.setIdleTimeout(highPerfConfig.getIdleTimeout());
        hikariConfig.setMaxLifetime(highPerfConfig.getMaxLifetime());
        hikariConfig.setLeakDetectionThreshold(60000);
        hikariConfig.setPoolName("HighPerf-" + dbType + "-Pool");
        
        // 数据库特定优化
        if (dbType == DatabaseType.MYSQL) {
            hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
            hikariConfig.addDataSourceProperty("prepStmtCacheSize", "500");
            hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        }
        
        return new HikariDataSource(hikariConfig);
    }
    
    /**
     * 生成迁移ID
     */
    private String generateMigrationId() {
        return "HPM-" + System.currentTimeMillis() % 100000;
    }
    
    /**
     * 启动进度监控
     */
    private ScheduledExecutorService startProgressMonitor(String migrationId, MigrationStats stats) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ProgressMonitor-" + migrationId);
            t.setDaemon(true);
            return t;
        });
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                logger.info("📈 {}", stats.getProgressReport(migrationId));
            } catch (Exception e) {
                logger.warn("进度报告失败: {}", e.getMessage());
            }
        }, highPerfConfig.getProgressReportInterval(), highPerfConfig.getProgressReportInterval(), TimeUnit.SECONDS);
        
        return scheduler;
    }
    
    /**
     * 计算最优线程数
     */
    private int calculateOptimalThreadCount(long totalRows, int shardCount) {
        // 基于数据量和分片数计算最优线程数
        int threadCount = Math.min(shardCount, highPerfConfig.getMaxParallelThreads());

        if (totalRows < 500000) {
            threadCount = Math.min(threadCount, 4);
        } else if (totalRows < 2000000) {
            threadCount = Math.min(threadCount, 8);
        } else {
            threadCount = Math.min(threadCount, highPerfConfig.getMaxParallelThreads());
        }

        return Math.max(highPerfConfig.getMinParallelThreads(), threadCount);
    }
    
    /**
     * 创建优化的线程池
     */
    private ExecutorService createOptimizedThreadPool(int threadCount, String migrationId) {
        return new ThreadPoolExecutor(
            threadCount,
            threadCount,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            r -> {
                Thread t = new Thread(r, "HighPerfMigration-" + migrationId + "-" +
                                     Thread.currentThread().getId());
                t.setDaemon(false);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 创建数据分片策略
     */
    private List<DataShard> createDataShards(HikariDataSource sourcePool, CopyTableRequest request,
                                            String migrationId) throws SQLException {
        List<DataShard> shards = new ArrayList<>();

        try (Connection conn = sourcePool.getConnection()) {
            // 获取主键字段名
            String primaryKeyColumn = getPrimaryKeyColumn(conn, request.getTableName());

            // 获取数据范围
            String rangeSQL = String.format(
                "SELECT MIN(%s) as min_id, MAX(%s) as max_id, COUNT(*) as total_count FROM %s",
                primaryKeyColumn, primaryKeyColumn, request.getTableName()
            );

            try (PreparedStatement stmt = conn.prepareStatement(rangeSQL);
                 ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    long minId = rs.getLong("min_id");
                    long maxId = rs.getLong("max_id");
                    long totalCount = rs.getLong("total_count");

                    logger.info("📊 [{}] 数据范围分析: ID范围 {} - {}, 总计 {} 条记录",
                               migrationId, minId, maxId, totalCount);

                    // 计算分片
                    long range = maxId - minId + 1;
                    int shardCount = (int) Math.ceil((double) totalCount / highPerfConfig.getShardSize());
                    long shardRange = range / shardCount;

                    for (int i = 0; i < shardCount; i++) {
                        long startId = minId + i * shardRange;
                        long endId = (i == shardCount - 1) ? maxId : startId + shardRange - 1;
                        long estimatedRows = Math.min(highPerfConfig.getShardSize(), totalCount - i * highPerfConfig.getShardSize());

                        shards.add(new DataShard(startId, endId, estimatedRows, i));

                        logger.debug("🔧 [{}] 分片 {}: ID范围 {} - {}, 预估 {} 条记录",
                                   migrationId, i, startId, endId, estimatedRows);
                    }
                }
            }
        }

        return shards;
    }

    /**
     * 获取主键字段名
     */
    private String getPrimaryKeyColumn(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        try (ResultSet rs = metaData.getPrimaryKeys(null, null, tableName)) {
            if (rs.next()) {
                return rs.getString("COLUMN_NAME");
            }
        }

        // 如果没有主键，使用第一个字段
        try (ResultSet rs = metaData.getColumns(null, null, tableName, null)) {
            if (rs.next()) {
                return rs.getString("COLUMN_NAME");
            }
        }

        throw new SQLException("无法确定表 " + tableName + " 的主键字段");
    }

    /**
     * 并行执行分片迁移
     */
    private void executeParallelShardMigration(String migrationId, CopyTableRequest request,
                                             TableStructure sourceStructure, TableStructure targetStructure,
                                             List<DataShard> shards, HikariDataSource sourcePool,
                                             HikariDataSource targetPool, ExecutorService executor,
                                             MigrationStats stats) {

        CountDownLatch latch = new CountDownLatch(shards.size());

        for (DataShard shard : shards) {
            executor.submit(() -> {
                try {
                    processDataShard(migrationId, request, sourceStructure, targetStructure,
                                   shard, sourcePool, targetPool, stats);
                    stats.incrementCompletedShards();

                    logger.debug("✅ [{}] 分片 {} 完成: 处理 {} 条记录",
                               migrationId, shard.getShardIndex(), shard.getEstimatedRows());

                } catch (Exception e) {
                    logger.error("❌ [{}] 分片 {} 处理失败", migrationId, shard.getShardIndex(), e);
                    throw new RuntimeException("分片处理失败", e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("迁移被中断", e);
        }
    }

    /**
     * 处理单个数据分片
     */
    private void processDataShard(String migrationId, CopyTableRequest request,
                                TableStructure sourceStructure, TableStructure targetStructure,
                                DataShard shard, HikariDataSource sourcePool,
                                HikariDataSource targetPool, MigrationStats stats) throws SQLException {

        String primaryKeyColumn = sourceStructure.getColumns().get(0).getColumnName();

        // 构建分片查询SQL
        String selectSQL = buildShardSelectSQL(request.getTableName(), sourceStructure,
                                             primaryKeyColumn, shard);
        String insertSQL = buildBatchInsertSQL(request.getTargetTableName(), targetStructure);

        try (Connection sourceConn = sourcePool.getConnection();
             Connection targetConn = targetPool.getConnection()) {

            // 优化连接设置
            optimizeConnection(sourceConn, targetConn);

            try (PreparedStatement selectStmt = sourceConn.prepareStatement(selectSQL);
                 PreparedStatement insertStmt = targetConn.prepareStatement(insertSQL)) {

                // 设置查询参数
                selectStmt.setLong(1, shard.getStartId());
                selectStmt.setLong(2, shard.getEndId());
                selectStmt.setFetchSize(10000); // 大批量获取

                try (ResultSet rs = selectStmt.executeQuery()) {

                    int batchCount = 0;
                    long processedInShard = 0;

                    while (rs.next()) {
                        // 设置插入参数
                        setHighPerformanceInsertParameters(rs, insertStmt, sourceStructure,
                                                         targetStructure, request);
                        insertStmt.addBatch();
                        batchCount++;
                        processedInShard++;

                        // 执行大批次
                        if (batchCount >= highPerfConfig.getMaxBatchSize()) {
                            insertStmt.executeBatch();
                            targetConn.commit();
                            stats.addProcessed(batchCount);
                            batchCount = 0;
                        }
                    }

                    // 处理剩余批次
                    if (batchCount > 0) {
                        insertStmt.executeBatch();
                        targetConn.commit();
                        stats.addProcessed(batchCount);
                    }

                    logger.debug("🔧 [{}] 分片 {} 处理完成: {} 条记录",
                               migrationId, shard.getShardIndex(), processedInShard);
                }
            }
        }
    }

    /**
     * 构建分片查询SQL
     */
    private String buildShardSelectSQL(String tableName, TableStructure structure,
                                     String primaryKeyColumn, DataShard shard) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");

        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(structure.getColumns().get(i).getColumnName());
        }

        sql.append(" FROM ").append(tableName);
        sql.append(" WHERE ").append(primaryKeyColumn).append(" >= ? AND ");
        sql.append(primaryKeyColumn).append(" <= ?");
        sql.append(" ORDER BY ").append(primaryKeyColumn);

        return sql.toString();
    }

    /**
     * 构建批量插入SQL
     */
    private String buildBatchInsertSQL(String tableName, TableStructure structure) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(structure.getColumns().get(i).getColumnName());
        }

        sql.append(") VALUES (");

        for (int i = 0; i < structure.getColumns().size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append("?");
        }

        sql.append(")");
        return sql.toString();
    }

    /**
     * 优化数据库连接
     */
    private void optimizeConnection(Connection sourceConn, Connection targetConn) throws SQLException {
        // 源连接优化
        sourceConn.setAutoCommit(false);
        sourceConn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);

        // 目标连接优化
        targetConn.setAutoCommit(false);
        targetConn.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);

        // MySQL特定优化
        if (targetConn.getMetaData().getDatabaseProductName().toLowerCase().contains("mysql")) {
            try (Statement stmt = targetConn.createStatement()) {
                stmt.execute("SET SESSION sql_mode = 'NO_AUTO_VALUE_ON_ZERO'");
                stmt.execute("SET SESSION foreign_key_checks = 0");
                stmt.execute("SET SESSION unique_checks = 0");
                stmt.execute("SET SESSION autocommit = 0");
                stmt.execute("SET SESSION innodb_flush_log_at_trx_commit = 2");
                stmt.execute("SET SESSION sync_binlog = 0");
            }
        }

        // PostgreSQL特定优化
        if (sourceConn.getMetaData().getDatabaseProductName().toLowerCase().contains("postgresql")) {
            try (Statement stmt = sourceConn.createStatement()) {
                stmt.execute("SET synchronous_commit = off");
                stmt.execute("SET checkpoint_segments = 32");
            }
        }
    }

    /**
     * 高性能参数设置
     */
    private void setHighPerformanceInsertParameters(ResultSet rs, PreparedStatement insertStmt,
                                                  TableStructure sourceStructure, TableStructure targetStructure,
                                                  CopyTableRequest request) throws SQLException {
        int paramIndex = 1;

        // 如果启用双主键设计，先设置新主键
        if (request.isUseDualPrimaryKey()) {
            long newId = snowflakeIdGenerator.nextId();
            insertStmt.setLong(paramIndex++, newId);
        }

        // 设置源表字段对应的参数
        int sourceFieldIndex = 1;
        for (int i = (request.isUseDualPrimaryKey() ? 1 : 0); i < targetStructure.getColumns().size(); i++) {
            ColumnInfo targetColumn = targetStructure.getColumns().get(i);

            try {
                Object value = rs.getObject(sourceFieldIndex++);

                // 高性能数据类型转换
                if (value != null) {
                    value = fastDataTypeConversion(value, targetColumn);
                }

                insertStmt.setObject(paramIndex++, value);

            } catch (SQLException e) {
                throw new SQLException(String.format(
                    "参数设置失败 - 索引: %d, 字段: %s",
                    paramIndex - 1, targetColumn.getColumnName()
                ), e);
            }
        }
    }

    /**
     * 快速数据类型转换
     */
    private Object fastDataTypeConversion(Object value, ColumnInfo targetColumn) {
        if (value == null) return null;

        String targetType = targetColumn.getDataType().toLowerCase();

        // 使用switch优化性能
        switch (targetType) {
            case "tinyint":
            case "smallint":
            case "mediumint":
            case "int":
            case "integer":
                return value instanceof Number ? ((Number) value).intValue() : value;
            case "bigint":
                return value instanceof Number ? ((Number) value).longValue() : value;
            case "varchar":
            case "text":
            case "longtext":
                return value.toString();
            case "decimal":
            case "numeric":
            case "datetime":
            case "timestamp":
                return value;
            default:
                return value;
        }
    }
}
