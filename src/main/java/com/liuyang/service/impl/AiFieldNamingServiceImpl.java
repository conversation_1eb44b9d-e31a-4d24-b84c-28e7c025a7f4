package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * AI字段命名服务实现
 * 使用OpenAI模型进行智能字段命名建议
 */
@Service
@ConditionalOnProperty(name = "app.ai.field-naming.enabled", havingValue = "true", matchIfMissing = false)
public class AiFieldNamingServiceImpl implements AiFieldNamingService {

    private static final Logger logger = LoggerFactory.getLogger(AiFieldNamingService.class);

    @Autowired(required = false)
    @Qualifier("fieldNamingChatClient")
    private ChatClient chatClient;

    @Value("${spring.ai.dashscope.api-key:}")
    private String dashscopeApiKey;
    
    @Value("${app.ai.field-naming.model:gpt-3.5-turbo}")
    private String model;
    
    // snake_case命名规则配置
    private final List<String> namingRules = Arrays.asList(
        "严格使用下划线命名法（snake_case），单词之间必须用下划线分隔",
        "所有字母必须小写，不允许大写字母",
        "字段名要有明确的业务含义，避免缩写",
        "时间字段：create_time, update_time, delete_time",
        "用户字段：create_user, update_user, delete_user",
        "主键字段：id，业务主键：business_id",
        "布尔字段：is_active, is_deleted, is_enabled",
        "状态字段：user_status, order_status, payment_status",
        "金额字段：total_amount, unit_price, discount_amount",
        "数量字段：item_count, total_num, max_limit",
        "常见转换示例：userName→user_name, createDate→create_time, isActive→is_active"
    );
    
    /**
     * 使用AI生成字段映射建议
     */
    @Override
    public List<FieldMapping> generateFieldMappingSuggestions(
            TableStructure sourceTable, 
            String targetTableName,
            String businessContext) {
        
        logger.info("使用AI生成字段映射建议: 源表={}, 目标表={}", sourceTable.getTableName(), targetTableName);
        
        try {
            // 构建提示词
            String prompt = buildFieldMappingPrompt(sourceTable, targetTableName, businessContext);
            
            // 调用AI模型
            String aiResponse = callAiModel(prompt);
            
            logger.debug("AI响应: {}", aiResponse);
            
            // 解析AI响应
            List<FieldMapping> suggestions = parseAiResponse(aiResponse, sourceTable);
            
            logger.info("AI生成了 {} 个字段映射建议", suggestions.size());
            return suggestions;
            
        } catch (Exception e) {
            logger.error("AI字段命名建议生成失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 优化单个字段名
     */
    @Override
    public String optimizeFieldName(String originalFieldName, String fieldType, String comment, String businessContext) {
        logger.info("使用AI优化字段名: {}", originalFieldName);

        // 特殊处理：主键id字段直接优化为business_id
        if ("id".equalsIgnoreCase(originalFieldName) && isPrimaryKeyField(comment)) {
            logger.info("主键字段特殊优化: {} -> business_id", originalFieldName);
            return "business_id";
        }

        try {
            String prompt = buildSingleFieldPrompt(originalFieldName, fieldType, comment, businessContext);
            String aiResponse = callAiModel(prompt);

            // 提取建议的字段名
            String optimizedName = extractFieldNameFromResponse(aiResponse);

            logger.info("字段名优化: {} -> {}", originalFieldName, optimizedName);
            return optimizedName;

        } catch (Exception e) {
            logger.error("AI字段名优化失败", e);
            return originalFieldName;
        }
    }
    
    /**
     * 批量优化字段映射
     */
    @Override
    public List<FieldMapping> optimizeFieldMappings(List<FieldMapping> originalMappings, String businessContext) {
        logger.info("使用AI批量优化字段映射: {} 个字段", originalMappings.size());
        
        try {
            String prompt = buildBatchOptimizationPrompt(originalMappings, businessContext);
            String aiResponse = callAiModel(prompt);
            
            // 解析优化后的映射
            List<FieldMapping> optimizedMappings = parseOptimizedMappings(aiResponse, originalMappings);
            
            logger.info("AI优化完成: {} 个字段映射", optimizedMappings.size());
            return optimizedMappings;
            
        } catch (Exception e) {
            logger.error("AI批量字段映射优化失败", e);
            return originalMappings;
        }
    }
    
    /**
     * 构建字段映射提示词
     */
    private String buildFieldMappingPrompt(TableStructure sourceTable, String targetTableName, String businessContext) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个数据库字段命名专家。请根据以下信息为数据库表字段生成标准化的命名建议：\n\n");
        
        // 命名规范
        prompt.append("命名规范：\n");
        for (String rule : namingRules) {
            prompt.append("- ").append(rule).append("\n");
        }
        prompt.append("\n");
        
        // 业务上下文
        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("业务上下文：").append(businessContext).append("\n\n");
        }
        
        // 源表信息
        prompt.append("源表信息：\n");
        prompt.append("表名：").append(sourceTable.getTableName()).append("\n");
        prompt.append("目标表名：").append(targetTableName).append("\n\n");
        
        prompt.append("源表字段列表：\n");
        for (ColumnInfo column : sourceTable.getColumns()) {
            prompt.append("- 字段名：").append(column.getColumnName())
                  .append("，类型：").append(column.getDataType());
            if (column.getComment() != null && !column.getComment().trim().isEmpty()) {
                prompt.append("，注释：").append(column.getComment());
            }
            prompt.append("\n");
        }
        
        prompt.append("\n请为每个字段提供标准化的目标字段名建议，格式如下：\n");
        prompt.append("源字段名 -> 目标字段名 (说明)\n\n");
        prompt.append("请确保：\n");
        prompt.append("1. 遵循上述命名规范\n");
        prompt.append("2. 保持字段的业务含义\n");
        prompt.append("3. 考虑字段类型和用途\n");
        prompt.append("4. 提供简短的说明\n");
        
        return prompt.toString();
    }
    
    /**
     * 构建单个字段优化提示词
     */
    private String buildSingleFieldPrompt(String fieldName, String fieldType, String comment, String businessContext) {
        StringBuilder prompt = new StringBuilder();
        
        // 如果有字段描述，强调基于描述进行命名
        if (comment != null && !comment.trim().isEmpty()) {
            prompt.append("请根据字段描述和业务含义，为数据库字段生成标准的英文命名：\n\n");
            
            prompt.append("字段信息：\n");
            prompt.append("- 原字段名：").append(fieldName).append("\n");
            prompt.append("- 字段类型：").append(fieldType).append("\n");
            prompt.append("- 字段描述：").append(comment).append("\n");
            if (businessContext != null && !businessContext.trim().isEmpty()) {
                prompt.append("- 业务上下文：").append(businessContext).append("\n");
            }
            
            prompt.append("\n🔥 重要：请主要根据「字段描述」的业务含义来生成字段名，而不是简单转换原字段名。\n");
            prompt.append("目标是生成能准确反映字段业务用途的标准英文字段名。\n\n");
            
        } else {
        prompt.append("请为以下数据库字段提供标准化的命名建议：\n\n");
        
        prompt.append("字段信息：\n");
        prompt.append("- 原字段名：").append(fieldName).append("\n");
        prompt.append("- 字段类型：").append(fieldType).append("\n");
        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("- 业务上下文：").append(businessContext).append("\n");
            }
        }
        
        prompt.append("命名规范要求：\n");
        for (String rule : namingRules) {
            prompt.append("- ").append(rule).append("\n");
        }
        
        // 根据是否有描述，提供不同的示例
        if (comment != null && !comment.trim().isEmpty()) {
            prompt.append("\n示例：\n");
            prompt.append("- 字段描述「用户注册时间」→ registration_time 或 register_time\n");
            prompt.append("- 字段描述「商品库存数量」→ stock_quantity 或 inventory_count\n");
            prompt.append("- 字段描述「订单支付状态」→ payment_status 或 pay_status\n");
            prompt.append("- 字段描述「用户真实姓名」→ real_name 或 full_name\n");
        }
        
        prompt.append("\n请直接返回建议的字段名，不需要其他说明。");
        
        return prompt.toString();
    }
    
    /**
     * 构建批量优化提示词
     */
    private String buildBatchOptimizationPrompt(List<FieldMapping> mappings, String businessContext) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请优化以下字段映射，使其符合标准命名规范：\n\n");
        
        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("业务上下文：").append(businessContext).append("\n\n");
        }
        
        prompt.append("当前字段映射：\n");
        for (FieldMapping mapping : mappings) {
            prompt.append("- ").append(mapping.getSourceField())
                  .append(" -> ").append(mapping.getTargetField())
                  .append(" (").append(mapping.getSourceType()).append(")");
            if (mapping.getComment() != null && !mapping.getComment().trim().isEmpty()) {
                prompt.append(" // ").append(mapping.getComment());
            }
            prompt.append("\n");
        }
        
        prompt.append("\n命名规范：\n");
        for (String rule : namingRules) {
            prompt.append("- ").append(rule).append("\n");
        }
        
        prompt.append("\n请返回优化后的映射，格式：源字段名 -> 优化后字段名\n");
        
        return prompt.toString();
    }
    
    /**
     * 调用AI模型
     */
    private String callAiModel(String promptText) {
        // 检查ChatClient是否可用
        if (chatClient == null) {
            logger.error("ChatClient未初始化");
            logger.error("可能的原因：");
            logger.error("1. Spring AI Alibaba自动配置失败");
            logger.error("2. API密钥未正确配置: {}", dashscopeApiKey != null ? "已配置" : "未配置");
            logger.error("3. 依赖版本不兼容");
            logger.error("请检查配置并重启应用");
            throw new RuntimeException("ChatClient未初始化，请检查Spring AI Alibaba配置和API密钥");
        }

        int maxRetries = 3;
        long baseDelay = 1000; // 1秒基础延迟
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug("调用AI模型，第{}次尝试，提示词长度: {}", attempt, promptText.length());
                
                String response = chatClient.prompt(promptText).call().content();
                
                logger.debug("AI响应成功，第{}次尝试，响应长度: {}", attempt, response != null ? response.length() : 0);
                return response;
                
            } catch (org.springframework.web.client.ResourceAccessException e) {
                // 处理超时和网络异常
                if (e.getMessage().contains("timeout") || e.getMessage().contains("timed out")) {
                    logger.warn("AI调用超时，第{}次尝试失败: {}", attempt, e.getMessage());
                    
                    if (attempt < maxRetries) {
                        long delay = baseDelay * attempt; // 递增延迟
                        logger.info("等待{}毫秒后进行第{}次重试...", delay, attempt + 1);
                        try {
                            Thread.sleep(delay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        logger.error("AI调用超时，已达最大重试次数{}次", maxRetries);
                        throw new RuntimeException("AI服务调用超时，请稍后重试。如果问题持续，请检查网络连接或联系管理员。", e);
                    }
                } else {
                    logger.error("AI调用网络异常: {}", e.getMessage());
                    throw new RuntimeException("AI服务网络异常: " + e.getMessage(), e);
                }
            } catch (Exception e) {
                logger.error("AI模型调用失败，第{}次尝试: {}", attempt, e.getMessage(), e);
                
                if (attempt < maxRetries && isRetryableException(e)) {
                    long delay = baseDelay * attempt;
                    logger.info("等待{}毫秒后进行第{}次重试...", delay, attempt + 1);
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    logger.error("AI模型调用失败，不再重试");
                    throw new RuntimeException("AI模型调用失败: " + e.getMessage(), e);
                }
            }
        }
        
        throw new RuntimeException("AI服务调用失败，已重试" + maxRetries + "次");
    }
    
    /**
     * 判断异常是否可重试
     */
    private boolean isRetryableException(Exception e) {
        String message = e.getMessage();
        if (message == null) {
            return false;
        }
        
        // 可重试的异常类型
        return message.contains("timeout") ||
               message.contains("timed out") ||
               message.contains("connection") ||
               message.contains("network") ||
               message.contains("temporary") ||
               e instanceof java.net.SocketTimeoutException ||
               e instanceof java.net.ConnectException ||
               e instanceof org.springframework.web.client.ResourceAccessException;
    }
    
    /**
     * 解析AI响应
     */
    private List<FieldMapping> parseAiResponse(String aiResponse, TableStructure sourceTable) {
        List<FieldMapping> mappings = new ArrayList<>();
        
        // 使用正则表达式解析 "源字段名 -> 目标字段名" 格式
        Pattern pattern = Pattern.compile("([\\w_]+)\\s*->\\s*([\\w_]+)\\s*(?:\\(([^)]+)\\))?");
        Matcher matcher = pattern.matcher(aiResponse);
        
        while (matcher.find()) {
            String sourceField = matcher.group(1).trim();
            String targetField = matcher.group(2).trim();
            String explanation = matcher.group(3) != null ? matcher.group(3).trim() : "";
            
            // 查找源字段信息
            ColumnInfo sourceColumn = sourceTable.getColumns().stream()
                .filter(col -> col.getColumnName().equalsIgnoreCase(sourceField))
                .findFirst()
                .orElse(null);
            
            if (sourceColumn != null) {
                FieldMapping mapping = new FieldMapping();
                mapping.setSourceField(sourceColumn.getColumnName());
                mapping.setTargetField(targetField);
                mapping.setSourceType(sourceColumn.getDataType());
                mapping.setTargetType(mapToStandardType(sourceColumn.getDataType()));
                mapping.setSourceSize(sourceColumn.getColumnSize());
                mapping.setTargetSize(sourceColumn.getColumnSize());
                mapping.setNullable(sourceColumn.isNullable());
                mapping.setComment("AI建议: " + explanation);
                
                mappings.add(mapping);
            }
        }
        
        return mappings;
    }
    
    /**
     * 从AI响应中提取字段名
     */
    private String extractFieldNameFromResponse(String response) {
        // 简单提取：取第一个看起来像字段名的词
        Pattern pattern = Pattern.compile("\\b([a-z][a-z0-9_]*[a-z0-9])\\b");
        Matcher matcher = pattern.matcher(response.toLowerCase());
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 如果没有找到，返回清理后的响应
        return response.trim().toLowerCase().replaceAll("[^a-z0-9_]", "_");
    }
    
    /**
     * 解析优化后的映射
     */
    private List<FieldMapping> parseOptimizedMappings(String aiResponse, List<FieldMapping> originalMappings) {
        List<FieldMapping> optimizedMappings = new ArrayList<>();
        
        Pattern pattern = Pattern.compile("([\\w_]+)\\s*->\\s*([\\w_]+)");
        Matcher matcher = pattern.matcher(aiResponse);
        
        int index = 0;
        while (matcher.find() && index < originalMappings.size()) {
            String sourceField = matcher.group(1).trim();
            String optimizedTargetField = matcher.group(2).trim();
            
            FieldMapping original = originalMappings.get(index);
            FieldMapping optimized = new FieldMapping();
            optimized.setSourceField(original.getSourceField());
            optimized.setTargetField(optimizedTargetField);
            optimized.setSourceType(original.getSourceType());
            optimized.setTargetType(original.getTargetType());
            optimized.setSourceSize(original.getSourceSize());
            optimized.setTargetSize(original.getTargetSize());
            optimized.setNullable(original.isNullable());
            optimized.setComment("AI优化: " + original.getComment());
            
            optimizedMappings.add(optimized);
            index++;
        }
        
        // 如果解析的数量不够，补充原始映射
        while (optimizedMappings.size() < originalMappings.size()) {
            optimizedMappings.add(originalMappings.get(optimizedMappings.size()));
        }
        
        return optimizedMappings;
    }
    
    /**
     * 验证字段名是否符合命名规范
     */
    @Override
    public FieldNameValidationResult validateFieldName(String fieldName, String fieldType) {
        logger.info("验证字段名: {}", fieldName);

        try {
            String prompt = buildValidationPrompt(fieldName, fieldType);
            String aiResponse = callAiModel(prompt);

            return parseValidationResponse(aiResponse, fieldName);

        } catch (Exception e) {
            logger.error("AI字段名验证失败", e);
            // 返回基础验证结果
            return performBasicValidation(fieldName, fieldType);
        }
    }

    /**
     * 生成表结构创建建议
     */
    @Override
    public TableStructureSuggestion generateTableStructureSuggestion(
            String tableName, String businessDescription, Integer fieldCount) {
        
        logger.info("使用AI生成表结构建议: 表名={}, 描述={}, 字段数={}", tableName, businessDescription, fieldCount);

        try {
            String prompt = buildTableStructurePrompt(tableName, businessDescription, fieldCount);
            String aiResponse = callAiModel(prompt);

            TableStructureSuggestion suggestion = parseTableStructureResponse(aiResponse, tableName);
            logger.info("AI生成表结构建议完成: {} 个字段", suggestion.getFields().size());
            
            return suggestion;

        } catch (Exception e) {
            logger.error("AI表结构建议生成失败", e);
            return createBasicTableStructure(tableName, businessDescription);
        }
    }

    /**
     * 构建验证提示词
     */
    private String buildValidationPrompt(String fieldName, String fieldType) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请验证以下数据库字段名是否符合标准命名规范：\n\n");
        prompt.append("字段名：").append(fieldName).append("\n");
        prompt.append("字段类型：").append(fieldType).append("\n\n");

        prompt.append("命名规范：\n");
        for (String rule : namingRules) {
            prompt.append("- ").append(rule).append("\n");
        }

        prompt.append("\n请返回验证结果，格式如下：\n");
        prompt.append("VALID: true/false\n");
        prompt.append("REASON: 验证原因\n");
        prompt.append("SUGGESTION: 改进建议\n");
        prompt.append("IMPROVED_NAME: 改进后的字段名（如果需要）\n");

        return prompt.toString();
    }

    /**
     * 构建表结构提示词
     */
    private String buildTableStructurePrompt(String tableName, String businessDescription, Integer fieldCount) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("请为以下业务场景设计标准的数据库表结构：\n\n");
        prompt.append("表名：").append(tableName).append("\n");
        prompt.append("业务描述：").append(businessDescription).append("\n");
        if (fieldCount != null) {
            prompt.append("预期字段数量：").append(fieldCount).append("\n");
        }

        prompt.append("\n设计要求：\n");
        prompt.append("1. 包含标准的审计字段（id, business_id, create_time, create_user, update_time, update_user）\n");
        prompt.append("2. 遵循PostgreSQL最佳实践\n");
        prompt.append("3. 字段命名符合snake_case规范\n");
        prompt.append("4. 合理的数据类型选择\n");

        prompt.append("\n请返回表结构，格式如下：\n");
        prompt.append("FIELD: 字段名 | 数据类型 | 是否可空 | 注释 | 是否主键\n");

        return prompt.toString();
    }

    /**
     * 映射到标准数据类型
     */
    private String mapToStandardType(String sourceType) {
        if (sourceType == null) {
            return "varchar";
        }

        String lowerType = sourceType.toLowerCase();

        if (lowerType.contains("varchar") || lowerType.contains("text") || lowerType.contains("char")) {
            return "varchar";
        } else if (lowerType.contains("int") || lowerType.contains("serial")) {
            return "bigint";
        } else if (lowerType.contains("timestamp") || lowerType.contains("datetime")) {
            return "timestamp";
        } else if (lowerType.contains("date")) {
            return "date";
        } else if (lowerType.contains("decimal") || lowerType.contains("numeric")) {
            return "decimal";
        } else if (lowerType.contains("bool")) {
            return "boolean";
        }

        return sourceType;
    }

    /**
     * 解析验证响应
     */
    private FieldNameValidationResult parseValidationResponse(String aiResponse, String originalFieldName) {
        try {
            boolean valid = extractBooleanValue(aiResponse, "VALID");
            String reason = extractStringValue(aiResponse, "REASON");
            String suggestion = extractStringValue(aiResponse, "SUGGESTION");
            String improvedName = extractStringValue(aiResponse, "IMPROVED_NAME");

            if (improvedName == null || improvedName.trim().isEmpty()) {
                improvedName = originalFieldName;
            }

            return new FieldNameValidationResult(valid, suggestion, reason, improvedName);

        } catch (Exception e) {
            logger.warn("解析AI验证响应失败，使用基础验证: {}", e.getMessage());
            return performBasicValidation(originalFieldName, null);
        }
    }

    /**
     * 解析表结构响应
     */
    private TableStructureSuggestion parseTableStructureResponse(String aiResponse, String tableName) {
        try {
            List<FieldSuggestion> fields = new ArrayList<>();

            // 解析FIELD格式的行
            Pattern pattern = Pattern.compile("FIELD:\\s*([^|]+)\\s*\\|\\s*([^|]+)\\s*\\|\\s*([^|]+)\\s*\\|\\s*([^|]+)\\s*\\|\\s*([^|\\n]+)");
            Matcher matcher = pattern.matcher(aiResponse);

            while (matcher.find()) {
                String fieldName = matcher.group(1).trim();
                String fieldType = matcher.group(2).trim();
                String nullable = matcher.group(3).trim();
                String comment = matcher.group(4).trim();
                String primaryKey = matcher.group(5).trim();

                FieldSuggestion field = new FieldSuggestion();
                field.setFieldName(fieldName);
                field.setFieldType(fieldType);
                field.setComment(comment);
                field.setNullable(!"false".equalsIgnoreCase(nullable) && !"no".equalsIgnoreCase(nullable));
                field.setPrimaryKey("true".equalsIgnoreCase(primaryKey) || "yes".equalsIgnoreCase(primaryKey));

                fields.add(field);
            }

            return new TableStructureSuggestion(tableName, "AI生成的表结构建议", fields);

        } catch (Exception e) {
            logger.warn("解析AI表结构响应失败，使用基础结构: {}", e.getMessage());
            return createBasicTableStructure(tableName, "基础表结构");
        }
    }

    /**
     * 执行基础验证
     */
    private FieldNameValidationResult performBasicValidation(String fieldName, String fieldType) {
        boolean valid = true;
        StringBuilder reason = new StringBuilder();
        String improvedName = fieldName;

        // 检查命名规范
        if (!fieldName.matches("^[a-z][a-z0-9_]*[a-z0-9]$")) {
            valid = false;
            reason.append("字段名不符合snake_case规范; ");
            improvedName = convertToSnakeCase(fieldName);
        }

        // 检查保留字
        if (isReservedWord(fieldName)) {
            valid = false;
            reason.append("字段名是保留字; ");
            improvedName = fieldName + "_value";
        }

        String suggestion = valid ? "字段名符合规范" : "建议使用: " + improvedName;

        return new FieldNameValidationResult(valid, suggestion, reason.toString(), improvedName);
    }

    /**
     * 创建基础表结构
     */
    private TableStructureSuggestion createBasicTableStructure(String tableName, String description) {
        List<FieldSuggestion> fields = new ArrayList<>();

        // 添加标准字段
        fields.add(new FieldSuggestion("id", "bigint", "主键ID", false));
        fields.add(new FieldSuggestion("business_id", "varchar(64)", "业务主键", false));
        fields.add(new FieldSuggestion("create_time", "timestamp", "创建时间", false));
        fields.add(new FieldSuggestion("create_user", "varchar(64)", "创建用户", false));
        fields.add(new FieldSuggestion("update_time", "timestamp", "更新时间", false));
        fields.add(new FieldSuggestion("update_user", "varchar(64)", "更新用户", false));

        // 设置主键
        fields.get(0).setPrimaryKey(true);

        return new TableStructureSuggestion(tableName, description, fields);
    }

    /**
     * 从AI响应中提取布尔值
     */
    private boolean extractBooleanValue(String response, String key) {
        Pattern pattern = Pattern.compile(key + ":\\s*(true|false)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            return "true".equalsIgnoreCase(matcher.group(1));
        }
        return false;
    }

    /**
     * 从AI响应中提取字符串值
     */
    private String extractStringValue(String response, String key) {
        Pattern pattern = Pattern.compile(key + ":\\s*([^\\n]+)");
        Matcher matcher = pattern.matcher(response);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return null;
    }

    /**
     * 检查是否为保留字
     */
    private boolean isReservedWord(String fieldName) {
        String[] reservedWords = {
            "select", "from", "where", "insert", "update", "delete", "create", "drop",
            "alter", "table", "index", "view", "user", "order", "group", "having"
        };

        for (String reserved : reservedWords) {
            if (reserved.equalsIgnoreCase(fieldName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将驼峰命名转换为下划线命名（重复方法，保持一致性）
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 生成字段注释
     */
    @Override
    public String generateFieldComment(String fieldName, String fieldType, String originalComment, String businessContext) {
        logger.info("使用AI生成字段注释: {}", fieldName);
        
        try {
            // 构建字段注释生成提示词
            String prompt = buildFieldCommentPrompt(fieldName, fieldType, originalComment, businessContext);
            
            // 调用AI模型
            String aiResponse = callAiModel(prompt);
            
            if (aiResponse != null && !aiResponse.trim().isEmpty()) {
                // 清理和格式化AI生成的注释
                String cleanedComment = cleanFieldComment(aiResponse);
                logger.info("AI生成字段注释: {} -> \"{}\"", fieldName, cleanedComment);
                return cleanedComment;
            }
            
        } catch (Exception e) {
            logger.warn("AI生成字段注释失败: {} - {}", fieldName, e.getMessage());
        }
        
        // 降级方案：返回原注释或根据字段名生成基本注释
        return generateBasicComment(fieldName, fieldType, originalComment);
    }

    /**
     * 构建字段注释生成提示词
     */
    private String buildFieldCommentPrompt(String fieldName, String fieldType, String originalComment, String businessContext) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个数据库字段注释专家。请为以下数据库字段生成简洁明确的中文注释：\n\n");
        
        prompt.append("字段信息：\n");
        prompt.append("- 字段名：").append(fieldName).append("\n");
        prompt.append("- 字段类型：").append(fieldType).append("\n");
        
        if (originalComment != null && !originalComment.trim().isEmpty()) {
            prompt.append("- 原注释：").append(originalComment).append("\n");
        }
        
        if (businessContext != null && !businessContext.trim().isEmpty()) {
            prompt.append("- 业务上下文：").append(businessContext).append("\n");
        }
        
        prompt.append("\n注释要求：\n");
        prompt.append("1. 使用简洁的中文描述，不超过50个字符\n");
        prompt.append("2. 描述字段的业务含义和用途\n");
        prompt.append("3. 如果是时间字段，说明具体用途（如创建时间、更新时间等）\n");
        prompt.append("4. 如果是状态字段，说明状态含义\n");
        prompt.append("5. 如果是ID字段，说明ID类型（如主键、外键、业务ID等）\n");
        prompt.append("6. 如果是金额字段，说明金额单位\n");
        prompt.append("7. 避免重复字段名信息\n");
        prompt.append("8. 直接返回注释内容，不要包含引号或其他格式\n\n");
        
        prompt.append("参考示例：\n");
        prompt.append("- user_id: 用户唯一标识\n");
        prompt.append("- create_time: 记录创建时间\n");
        prompt.append("- user_status: 用户状态（0-禁用，1-启用）\n");
        prompt.append("- total_amount: 订单总金额，单位：元\n");
        prompt.append("- car_number: 车牌号码\n");
        prompt.append("- phone_number: 手机号码\n\n");
        
        prompt.append("请为字段 \"").append(fieldName).append("\" 生成注释：");
        
        return prompt.toString();
    }
    
    /**
     * 清理字段注释
     */
    private String cleanFieldComment(String aiComment) {
        if (aiComment == null) {
            return null;
        }
        
        // 移除多余的引号和格式符号
        String cleaned = aiComment.trim()
            .replaceAll("^[\"'`]+", "")
            .replaceAll("[\"'`]+$", "")
            .replaceAll("\\n", " ")
            .replaceAll("\\s+", " ")
            .trim();
        
        // 移除可能的前缀文本
        cleaned = cleaned.replaceAll("^(注释：|字段注释：|说明：|描述：)", "");
        
        // 确保注释长度不超过100个字符（MySQL comment限制）
        if (cleaned.length() > 100) {
            cleaned = cleaned.substring(0, 97) + "...";
        }
        
        return cleaned;
    }
    
    /**
     * 生成基本注释（降级方案）
     */
    private String generateBasicComment(String fieldName, String fieldType, String originalComment) {
        // 如果有原注释，优先使用
        if (originalComment != null && !originalComment.trim().isEmpty()) {
            return originalComment;
        }
        
        // 根据字段名生成基本注释
        String lowerFieldName = fieldName.toLowerCase();
        
        if (lowerFieldName.equals("id")) {
            return "主键ID";
        } else if (lowerFieldName.endsWith("_id")) {
            String prefix = lowerFieldName.substring(0, lowerFieldName.length() - 3);
            return prefix.replace("_", "") + "标识";
        } else if (lowerFieldName.equals("create_time") || lowerFieldName.equals("createtime")) {
            return "创建时间";
        } else if (lowerFieldName.equals("update_time") || lowerFieldName.equals("updatetime")) {
            return "更新时间";
        } else if (lowerFieldName.equals("create_user") || lowerFieldName.equals("creator")) {
            return "创建用户";
        } else if (lowerFieldName.equals("update_user") || lowerFieldName.equals("updater")) {
            return "更新用户";
        } else if (lowerFieldName.contains("status")) {
            return "状态";
        } else if (lowerFieldName.contains("amount")) {
            return "金额";
        } else if (lowerFieldName.contains("count")) {
            return "数量";
        } else if (lowerFieldName.contains("time")) {
            return "时间";
        } else if (lowerFieldName.contains("date")) {
            return "日期";
        } else if (lowerFieldName.contains("name")) {
            return "名称";
        } else if (lowerFieldName.contains("code")) {
            return "编码";
        } else if (lowerFieldName.contains("number")) {
            return "号码";
        } else {
            return fieldName.replace("_", "");
        }
    }

    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKeyField(String comment) {
        if (comment == null || comment.trim().isEmpty()) {
            return false;
        }

        String lowerComment = comment.toLowerCase();
        return lowerComment.contains("主键") ||
               lowerComment.contains("primary") ||
               lowerComment.contains("id") ||
               lowerComment.contains("标识") ||
               lowerComment.contains("唯一标识") ||
               lowerComment.contains("主键id");
    }
}
