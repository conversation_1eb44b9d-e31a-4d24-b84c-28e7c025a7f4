package com.liuyang.service.impl;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;
import com.liuyang.service.AiFieldNamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 降级字段命名服务
 * 当AI服务不可用时提供基础的字段命名功能
 */
@Service
@ConditionalOnProperty(name = "app.ai.field-naming.enabled", havingValue = "false", matchIfMissing = true)
public class FallbackFieldNamingServiceImpl implements AiFieldNamingService {

    private static final Logger logger = LoggerFactory.getLogger(FallbackFieldNamingServiceImpl.class);

    // 常见字段映射规则
    private static final Map<String, String> FIELD_MAPPING_RULES = new HashMap<>();
    
    // 数据类型映射
    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();
    
    static {
        // 初始化字段映射规则
        FIELD_MAPPING_RULES.put("id", "business_id");
        FIELD_MAPPING_RULES.put("creator", "create_user");
        FIELD_MAPPING_RULES.put("createtime", "create_time");
        FIELD_MAPPING_RULES.put("create_time", "create_time");
        FIELD_MAPPING_RULES.put("updater", "update_user");
        FIELD_MAPPING_RULES.put("updatetime", "update_time");
        FIELD_MAPPING_RULES.put("update_time", "update_time");
        FIELD_MAPPING_RULES.put("modifier", "update_user");
        FIELD_MAPPING_RULES.put("modify_time", "update_time");
        FIELD_MAPPING_RULES.put("gmt_create", "create_time");
        FIELD_MAPPING_RULES.put("gmt_modified", "update_time");
        
        // 初始化类型映射
        TYPE_MAPPING.put("varchar", "varchar");
        TYPE_MAPPING.put("text", "varchar");
        TYPE_MAPPING.put("char", "varchar");
        TYPE_MAPPING.put("int", "bigint");
        TYPE_MAPPING.put("integer", "bigint");
        TYPE_MAPPING.put("bigint", "bigint");
        TYPE_MAPPING.put("timestamp", "timestamp");
        TYPE_MAPPING.put("datetime", "timestamp");
        TYPE_MAPPING.put("date", "date");
        TYPE_MAPPING.put("decimal", "decimal");
        TYPE_MAPPING.put("numeric", "decimal");
        TYPE_MAPPING.put("boolean", "boolean");
        TYPE_MAPPING.put("bool", "boolean");
    }

    @Override
    public List<FieldMapping> generateFieldMappingSuggestions(
            TableStructure sourceTable, 
            String targetTableName,
            String businessContext) {
        
        logger.info("使用降级服务生成字段映射建议: 源表={}", sourceTable.getTableName());
        
        List<FieldMapping> suggestions = new ArrayList<>();
        
        for (ColumnInfo column : sourceTable.getColumns()) {
            String sourceField = column.getColumnName();
            String suggestedTarget = suggestTargetFieldName(sourceField);
            
            FieldMapping mapping = new FieldMapping();
            mapping.setSourceField(sourceField);
            mapping.setTargetField(suggestedTarget);
            mapping.setSourceType(column.getDataType());
            mapping.setTargetType(mapToStandardType(column.getDataType()));
            mapping.setSourceSize(column.getColumnSize());
            mapping.setTargetSize(column.getColumnSize());
            mapping.setNullable(column.isNullable());
            mapping.setComment("规则映射建议: " + column.getComment());
            
            suggestions.add(mapping);
        }
        
        logger.info("降级服务生成了 {} 个字段映射建议", suggestions.size());
        return suggestions;
    }

    @Override
    public String optimizeFieldName(
            String originalFieldName,
            String fieldType,
            String comment,
            String businessContext) {

        logger.info("使用降级服务优化字段名: {}", originalFieldName);

        // 特殊处理：主键id字段直接优化为business_id
        if ("id".equalsIgnoreCase(originalFieldName) && isPrimaryKeyField(comment)) {
            logger.info("主键字段特殊优化: {} -> business_id", originalFieldName);
            return "business_id";
        }

        String optimized = suggestTargetFieldName(originalFieldName);
        logger.info("字段名优化: {} -> {}", originalFieldName, optimized);

        return optimized;
    }

    @Override
    public List<FieldMapping> optimizeFieldMappings(
            List<FieldMapping> originalMappings, 
            String businessContext) {
        
        logger.info("使用降级服务批量优化字段映射: {} 个字段", originalMappings.size());
        
        List<FieldMapping> optimizedMappings = new ArrayList<>();
        
        for (FieldMapping original : originalMappings) {
            FieldMapping optimized = new FieldMapping();
            optimized.setSourceField(original.getSourceField());
            optimized.setTargetField(suggestTargetFieldName(original.getTargetField()));
            optimized.setSourceType(original.getSourceType());
            optimized.setTargetType(mapToStandardType(original.getSourceType()));
            optimized.setSourceSize(original.getSourceSize());
            optimized.setTargetSize(original.getTargetSize());
            optimized.setNullable(original.isNullable());
            optimized.setComment("规则优化: " + original.getComment());
            
            optimizedMappings.add(optimized);
        }
        
        return optimizedMappings;
    }

    @Override
    public FieldNameValidationResult validateFieldName(String fieldName, String fieldType) {
        logger.info("使用降级服务验证字段名: {}", fieldName);
        
        boolean valid = true;
        StringBuilder reason = new StringBuilder();
        String improvedName = fieldName;
        
        // 检查命名规范
        if (!isValidSnakeCase(fieldName)) {
            valid = false;
            reason.append("字段名不符合snake_case规范; ");
            improvedName = convertToSnakeCase(fieldName);
        }
        
        // 检查保留字
        if (isReservedWord(fieldName)) {
            valid = false;
            reason.append("字段名是数据库保留字; ");
            improvedName = fieldName + "_value";
        }
        
        // 检查长度
        if (fieldName.length() > 63) {
            valid = false;
            reason.append("字段名过长（超过63字符）; ");
            improvedName = fieldName.substring(0, 60) + "_id";
        }
        
        String suggestion = valid ? "字段名符合规范" : "建议使用: " + improvedName;
        
        return new FieldNameValidationResult(valid, suggestion, reason.toString(), improvedName);
    }

    @Override
    public TableStructureSuggestion generateTableStructureSuggestion(String tableName, String businessDescription, Integer fieldCount) {
        logger.info("使用降级方案生成表结构建议");
        
        List<AiFieldNamingService.FieldSuggestion> fields = new ArrayList<>();
        
        // 创建基本字段建议
        fields.add(new AiFieldNamingService.FieldSuggestion("id", "BIGINT", "主键ID", false));
        fields.add(new AiFieldNamingService.FieldSuggestion("create_time", "DATETIME", "创建时间", true));
        fields.add(new AiFieldNamingService.FieldSuggestion("update_time", "DATETIME", "更新时间", true));
        fields.add(new AiFieldNamingService.FieldSuggestion("create_user", "VARCHAR(100)", "创建用户", true));
        fields.add(new AiFieldNamingService.FieldSuggestion("update_user", "VARCHAR(100)", "更新用户", true));
        
        return new AiFieldNamingService.TableStructureSuggestion(tableName, businessDescription, fields);
    }
    
    @Override
    public String generateFieldComment(String fieldName, String fieldType, String originalComment, String businessContext) {
        logger.info("使用降级方案生成字段注释: {}", fieldName);
        
        // 如果有原注释，优先使用
        if (originalComment != null && !originalComment.trim().isEmpty()) {
            return originalComment;
        }
        
        // 根据字段名生成基本注释
        String lowerFieldName = fieldName.toLowerCase();
        
        if (lowerFieldName.equals("id")) {
            return "主键ID";
        } else if (lowerFieldName.endsWith("_id")) {
            String prefix = lowerFieldName.substring(0, lowerFieldName.length() - 3);
            return prefix.replace("_", "") + "标识";
        } else if (lowerFieldName.equals("create_time") || lowerFieldName.equals("createtime")) {
            return "创建时间";
        } else if (lowerFieldName.equals("update_time") || lowerFieldName.equals("updatetime")) {
            return "更新时间";
        } else if (lowerFieldName.equals("create_user") || lowerFieldName.equals("creator")) {
            return "创建用户";
        } else if (lowerFieldName.equals("update_user") || lowerFieldName.equals("updater")) {
            return "更新用户";
        } else if (lowerFieldName.equals("business_id")) {
            return "业务主键";
        } else if (lowerFieldName.contains("status")) {
            return "状态";
        } else if (lowerFieldName.contains("amount")) {
            return "金额";
        } else if (lowerFieldName.contains("count")) {
            return "数量";
        } else if (lowerFieldName.contains("time")) {
            return "时间";
        } else if (lowerFieldName.contains("date")) {
            return "日期";
        } else if (lowerFieldName.contains("name")) {
            return "名称";
        } else if (lowerFieldName.contains("code")) {
            return "编码";
        } else if (lowerFieldName.contains("number")) {
            return "号码";
        } else if (lowerFieldName.contains("url")) {
            return "链接地址";
        } else if (lowerFieldName.contains("type")) {
            return "类型";
        } else if (lowerFieldName.contains("state")) {
            return "状态";
        } else if (lowerFieldName.contains("order")) {
            return "订单相关";
        } else if (lowerFieldName.contains("user")) {
            return "用户相关";
        } else if (lowerFieldName.contains("phone")) {
            return "电话号码";
        } else if (lowerFieldName.contains("email")) {
            return "邮箱地址";
        } else if (lowerFieldName.contains("address")) {
            return "地址";
        } else if (lowerFieldName.contains("price")) {
            return "价格";
        } else if (lowerFieldName.contains("pay")) {
            return "支付相关";
        } else if (lowerFieldName.contains("query")) {
            return "查询相关";
        } else if (lowerFieldName.contains("auth")) {
            return "认证相关";
        } else if (lowerFieldName.contains("car")) {
            return "车辆相关";
        } else if (lowerFieldName.contains("brand")) {
            return "品牌";
        } else if (lowerFieldName.contains("channel")) {
            return "渠道";
        } else if (lowerFieldName.contains("operator")) {
            return "操作员";
        } else if (lowerFieldName.contains("recall")) {
            return "召回相关";
        } else if (lowerFieldName.contains("vin")) {
            return "车架号";
        } else if (lowerFieldName.contains("license") || lowerFieldName.contains("lic")) {
            return "车牌相关";
        } else {
            // 默认处理：移除下划线，首字母大写
            return fieldName.replace("_", "").toLowerCase();
        }
    }
    
    /**
     * 建议目标字段名
     */
    private String suggestTargetFieldName(String sourceField) {
        String lowerField = sourceField.toLowerCase();
        
        // 1. 直接映射
        if (FIELD_MAPPING_RULES.containsKey(lowerField)) {
            return FIELD_MAPPING_RULES.get(lowerField);
        }
        
        // 2. 模糊匹配
        for (Map.Entry<String, String> entry : FIELD_MAPPING_RULES.entrySet()) {
            if (lowerField.contains(entry.getKey()) || entry.getKey().contains(lowerField)) {
                return entry.getValue();
            }
        }
        
        // 3. 基于规则的转换
        String converted = applyNamingRules(sourceField);
        
        return converted;
    }
    
    /**
     * 应用命名规则
     */
    private String applyNamingRules(String fieldName) {
        String result = fieldName;
        
        // 转换为snake_case
        result = convertToSnakeCase(result);
        
        // 处理特殊情况
        if (result.matches(".*time.*") || result.matches(".*date.*")) {
            if (result.contains("create") || result.contains("add")) {
                return "create_time";
            } else if (result.contains("update") || result.contains("modify")) {
                return "update_time";
            }
        }
        
        if (result.matches(".*user.*") || result.matches(".*by.*")) {
            if (result.contains("create") || result.contains("add")) {
                return "create_user";
            } else if (result.contains("update") || result.contains("modify")) {
                return "update_user";
            }
        }
        
        return result;
    }
    
    /**
     * 映射到标准数据类型
     */
    private String mapToStandardType(String sourceType) {
        if (sourceType == null) {
            return "varchar";
        }
        
        String lowerType = sourceType.toLowerCase();
        
        for (Map.Entry<String, String> entry : TYPE_MAPPING.entrySet()) {
            if (lowerType.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        
        return sourceType;
    }
    
    /**
     * 检查是否为有效的snake_case命名
     */
    private boolean isValidSnakeCase(String fieldName) {
        return Pattern.matches("^[a-z][a-z0-9_]*[a-z0-9]$", fieldName);
    }
    
    /**
     * 将驼峰命名转换为下划线命名
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 检查是否为保留字
     */
    private boolean isReservedWord(String fieldName) {
        String[] reservedWords = {
            "select", "from", "where", "insert", "update", "delete", "create", "drop", 
            "alter", "table", "index", "view", "user", "order", "group", "having",
            "and", "or", "not", "null", "true", "false", "case", "when", "then", "else"
        };
        
        for (String reserved : reservedWords) {
            if (reserved.equalsIgnoreCase(fieldName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKeyField(String comment) {
        if (comment == null || comment.trim().isEmpty()) {
            return false;
        }

        String lowerComment = comment.toLowerCase();
        return lowerComment.contains("主键") ||
               lowerComment.contains("primary") ||
               lowerComment.contains("id") ||
               lowerComment.contains("标识") ||
               lowerComment.contains("唯一标识") ||
               lowerComment.contains("主键id");
    }
}
