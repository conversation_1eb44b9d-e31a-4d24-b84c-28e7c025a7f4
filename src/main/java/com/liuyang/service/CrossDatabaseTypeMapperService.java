package com.liuyang.service;

import com.liuyang.dto.DatabaseType;

public interface CrossDatabaseTypeMapperService {
    /**
     * 映射数据类型
     * @param sourceType 源数据库类型
     * @param targetType 目标数据库类型
     * @param dataType 数据类型
     * @param columnSize 列大小
     * @param decimalDigits 小数位数
     * @return 映射后的数据类型
     */
    public String mapDataType(DatabaseType sourceType, DatabaseType targetType,
                              String dataType, Integer columnSize, Integer decimalDigits);
}
