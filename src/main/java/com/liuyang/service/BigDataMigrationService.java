package com.liuyang.service;

import com.liuyang.dto.CopyTableRequest;
import com.liuyang.dto.TableStructure;

/**
 * 大数据量迁移服务接口
 * 专门处理百万级以上数据的高效迁移
 */
public interface BigDataMigrationService {
    
    /**
     * 大数据量迁移主方法
     * 
     * @param request 迁移请求
     * @param sourceStructure 源表结构
     * @param targetStructure 目标表结构
     */
    void migrateLargeDataset(CopyTableRequest request, TableStructure sourceStructure, 
                           TableStructure targetStructure);
}
