package com.liuyang.service;

import com.liuyang.entity.InvitationCode;

import java.util.List;

/**
 * 邀请码服务接口
 */
public interface InvitationCodeService {
    
    /**
     * 验证邀请码是否有效
     * @param code 原始邀请码
     * @return 验证结果
     */
    boolean validateInvitationCode(String code);
    
    /**
     * 使用邀请码（验证并增加使用次数）
     * @param code 原始邀请码
     * @param username 使用的用户名
     * @return 使用结果
     */
    boolean useInvitationCode(String code, String username);
    
    /**
     * 创建新的邀请码
     * @param code 原始邀请码
     * @param description 描述
     * @param maxUsage 最大使用次数（-1表示无限制）
     * @param createdBy 创建人
     * @return 创建的邀请码对象
     */
    InvitationCode createInvitationCode(String code, String description, Integer maxUsage, String createdBy);
    
    /**
     * 根据ID查询邀请码
     */
    InvitationCode getById(Long id);
    
    /**
     * 查询所有激活的邀请码
     */
    List<InvitationCode> getAllActive();
    
    /**
     * 查询所有邀请码
     */
    List<InvitationCode> getAll();
    
    /**
     * 查询所有可用的邀请码
     */
    List<InvitationCode> getAllAvailable();
    
    /**
     * 更新邀请码信息
     */
    boolean updateInvitationCode(InvitationCode invitationCode);
    
    /**
     * 启用/禁用邀请码
     */
    boolean toggleInvitationCode(Long id, boolean active, String updatedBy);
    
    /**
     * 删除邀请码
     */
    boolean deleteInvitationCode(Long id);
    
    /**
     * 检查邀请码是否存在
     */
    boolean existsByCode(String code);
    
    /**
     * 初始化默认邀请码
     */
    void initializeDefaultInvitationCodes();
}
