package com.liuyang.service;

import com.liuyang.entity.DatabaseConfigEntity;

import java.util.List;
import java.util.Map;

public interface DatabaseConfigManagementService {
    public DatabaseConfigEntity getSourceDatabaseConfig() ;

    /**
     * 获取指定环境的源数据库配置
     */
    public DatabaseConfigEntity getSourceDatabaseConfig(String environment) ;
    /**
     * 获取当前环境的目标数据库配置
     */
    public DatabaseConfigEntity getTargetDatabaseConfig() ;

    /**
     * 获取指定环境的目标数据库配置
     */
    public DatabaseConfigEntity getTargetDatabaseConfig(String environment);

    /**
     * 测试数据库连接
     */
    public boolean testConnection(DatabaseConfigEntity config);

    /**
     * 测试数据库连接（指定数据库名）
     */
    public boolean testConnection(DatabaseConfigEntity config, String databaseName);

    /**
     * 创建或更新配置
     */
    public DatabaseConfigEntity saveOrUpdateConfig(DatabaseConfigEntity config) ;

    /**
     * 根据ID查询配置
     */
    public DatabaseConfigEntity getConfigById(Long id);

    /**
     * 查询所有配置
     */
    public List<DatabaseConfigEntity> getAllConfigs();

    /**
     * 删除配置
     */
    public void deleteConfig(Long configId) ;

    /**
     * 获取环境列表
     */
    public List<String> getAllEnvironments();

    /**
     * 获取指定环境的所有启用配置
     */
    public List<DatabaseConfigEntity> getActiveConfigsByEnvironment(String environment) ;

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() ;

    /**
     * 批量测试数据库连接
     */
    public List<Map<String, Object>> batchTestConnection(List<Long> configIds) ;
}
