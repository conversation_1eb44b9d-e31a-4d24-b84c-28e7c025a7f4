package com.liuyang.service;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.entity.MigrationRecord;
import com.liuyang.entity.TableMappingRecord;
import com.liuyang.entity.FieldMappingRecord;
import com.liuyang.mapper.MigrationRecordMapper.MigrationStatistics;
import com.liuyang.mapper.TableMappingRecordMapper.TableMappingStatistics;
import com.liuyang.mapper.FieldMappingRecordMapper.FieldMappingStatistics;

import java.util.List;
import java.util.Map;

/**
 * 迁移映射记录服务接口
 */
public interface MigrationMappingService {
    
    /**
     * 开始迁移记录
     * @param sourceConfigId 源数据库配置ID
     * @param targetConfigId 目标数据库配置ID
     * @param sourceDatabaseName 源数据库名
     * @param targetDatabaseName 目标数据库名
     * @param migrationNotes 迁移备注
     * @return 迁移记录
     */
    MigrationRecord startMigration(Long sourceConfigId, Long targetConfigId, 
                                 String sourceDatabaseName, String targetDatabaseName, 
                                 String migrationNotes);
    
    /**
     * 完成迁移记录
     * @param migrationBatchId 迁移批次ID
     * @param success 是否成功
     * @param errorMessage 错误信息
     */
    void completeMigration(Long migrationBatchId, boolean success, String errorMessage);
    
    /**
     * 添加表迁移记录
     * @param migrationBatchId 迁移批次ID
     * @param sourceTableName 源表名
     * @param sourceTableSchema 源表模式
     * @param sourceTableComment 源表注释
     * @param targetTableName 目标表名
     * @param targetTableComment 目标表注释
     * @param targetDdlStatement 目标表DDL语句
     * @param sourceColumns 源表字段信息
     * @param targetColumns 目标表字段信息
     * @param sourceRecordCount 源表记录数
     * @param targetRecordCount 目标表记录数
     * @return 表映射记录
     */
    TableMappingRecord addTableMapping(Long migrationBatchId,
                                     String sourceTableName, String sourceTableSchema, String sourceTableComment,
                                     String targetTableName, String targetTableComment, String targetDdlStatement,
                                     List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                     long sourceRecordCount, long targetRecordCount);

    /**
     * 添加表迁移记录（包含AI提示词描述）
     * @param migrationBatchId 迁移批次ID
     * @param sourceTableName 源表名
     * @param sourceTableSchema 源表模式
     * @param sourceTableComment 源表注释
     * @param targetTableName 目标表名
     * @param targetTableComment 目标表注释
     * @param aiPromptDescription AI优化表名时使用的提示词描述
     * @param targetDdlStatement 目标表DDL语句
     * @param sourceColumns 源表字段信息
     * @param targetColumns 目标表字段信息
     * @param sourceRecordCount 源表记录数
     * @param targetRecordCount 目标表记录数
     * @return 表映射记录
     */
    TableMappingRecord addTableMappingWithAiPrompt(Long migrationBatchId,
                                                   String sourceTableName, String sourceTableSchema, String sourceTableComment,
                                                   String targetTableName, String targetTableComment, String aiPromptDescription,
                                                   String targetDdlStatement,
                                                   List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                                   long sourceRecordCount, long targetRecordCount);

    /**
     * 标记表迁移完成
     * @param tableMappingId 表映射记录ID
     * @param success 是否成功
     * @param errorMessage 错误信息
     */
    void completeTableMapping(Long tableMappingId, boolean success, String errorMessage);
    
    /**
     * 批量添加字段映射记录
     * @param tableMappingId 表映射记录ID
     * @param sourceColumns 源字段信息
     * @param targetColumns 目标字段信息
     * @param fieldMappings 字段映射关系
     */
    void addFieldMappings(Long tableMappingId, List<ColumnInfo> sourceColumns, 
                         List<ColumnInfo> targetColumns, Map<String, String> fieldMappings);
    
    /**
     * 根据批次ID查询迁移记录
     * @param migrationBatchId 迁移批次ID
     * @return 迁移记录（包含表映射和字段映射）
     */
    MigrationRecord getMigrationRecordWithDetails(Long migrationBatchId);
    
    /**
     * 查询迁移记录列表（分页）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 迁移记录列表
     */
    List<MigrationRecord> getMigrationRecords(int pageNum, int pageSize);
    
    /**
     * 查询迁移记录总数
     * @return 总数
     */
    int getMigrationRecordCount();
    
    /**
     * 根据配置ID查询相关的迁移记录
     * @param configId 数据库配置ID
     * @return 迁移记录列表
     */
    List<MigrationRecord> getMigrationRecordsByConfigId(Long configId);
    
    /**
     * 查询最近的迁移记录
     * @param limit 数量限制
     * @return 迁移记录列表
     */
    List<MigrationRecord> getRecentMigrationRecords(int limit);
    
    /**
     * 获取迁移统计信息
     * @return 迁移统计信息
     */
    MigrationStatistics getMigrationStatistics();
    
    /**
     * 获取表映射统计信息
     * @param migrationRecordId 迁移记录ID
     * @return 表映射统计信息
     */
    TableMappingStatistics getTableMappingStatistics(Long migrationRecordId);
    
    /**
     * 获取字段映射统计信息
     * @param tableMappingRecordId 表映射记录ID
     * @return 字段映射统计信息
     */
    FieldMappingStatistics getFieldMappingStatistics(Long tableMappingRecordId);
    
    /**
     * 删除迁移记录及其关联数据
     * @param migrationRecordId 迁移记录ID
     */
    void deleteMigrationRecord(Long migrationRecordId);

    /**
     * 删除单个迁移记录及其所有关联数据（级联删除）
     * @param migrationRecordId 迁移记录ID
     * @return 删除结果信息
     */
    String deleteMigrationRecordWithCascade(Long migrationRecordId);
    
    /**
     * 查询指定表的迁移历史
     * @param sourceTableName 源表名
     * @param targetTableName 目标表名
     * @return 表映射记录列表
     */
    List<TableMappingRecord> getTableMigrationHistory(String sourceTableName, String targetTableName);
    
    /**
     * 查询指定字段的映射历史
     * @param sourceFieldName 源字段名
     * @param targetFieldName 目标字段名
     * @return 字段映射记录列表
     */
    List<FieldMappingRecord> getFieldMappingHistory(String sourceFieldName, String targetFieldName);
    
    /**
     * 快速创建表迁移记录（用于现有迁移功能集成）
     * @param sourceConfigId 源数据库配置ID
     * @param targetConfigId 目标数据库配置ID
     * @param sourceDatabaseName 源数据库名
     * @param targetDatabaseName 目标数据库名
     * @param sourceTableName 源表名
     * @param targetTableName 目标表名
     * @param targetDdlStatement 目标表DDL语句
     * @param sourceColumns 源表字段信息
     * @param targetColumns 目标表字段信息
     * @param sourceRecordCount 源表记录数
     * @param targetRecordCount 目标表记录数
     * @param migrationSuccess 迁移是否成功
     * @param errorMessage 错误信息
     * @return 迁移批次ID
     */
    Long recordTableMigration(Long sourceConfigId, Long targetConfigId,
                              String sourceDatabaseName, String targetDatabaseName,
                              String sourceTableName, String targetTableName,
                              String targetDdlStatement,
                              List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                              long sourceRecordCount, long targetRecordCount,
                              boolean migrationSuccess, String errorMessage);

    /**
     * 快速创建表迁移记录（包含AI提示词描述）
     * @param sourceConfigId 源数据库配置ID
     * @param targetConfigId 目标数据库配置ID
     * @param sourceDatabaseName 源数据库名
     * @param targetDatabaseName 目标数据库名
     * @param sourceTableName 源表名
     * @param targetTableName 目标表名
     * @param targetDdlStatement 目标表DDL语句
     * @param aiPromptDescription AI优化表名时使用的提示词描述
     * @param sourceColumns 源表字段信息
     * @param targetColumns 目标表字段信息
     * @param sourceRecordCount 源表记录数
     * @param targetRecordCount 目标表记录数
     * @param migrationSuccess 迁移是否成功
     * @param errorMessage 错误信息
     * @return 迁移批次ID
     */
    Long recordTableMigrationWithAiPrompt(Long sourceConfigId, Long targetConfigId,
                                          String sourceDatabaseName, String targetDatabaseName,
                                          String sourceTableName, String targetTableName,
                                          String targetDdlStatement, String aiPromptDescription,
                                          List<ColumnInfo> sourceColumns, List<ColumnInfo> targetColumns,
                                          long sourceRecordCount, long targetRecordCount,
                                          boolean migrationSuccess, String errorMessage);

    /**
     * 根据源表名查询迁移记录
     */
    List<MigrationRecord> getMigrationRecordsBySourceTable(String sourceTableName);
    
    /**
     * 根据目标表名查询迁移记录
     */
    List<MigrationRecord> getMigrationRecordsByTargetTable(String targetTableName);
    
    /**
     * 根据表映射关系查询迁移记录
     */
    List<MigrationRecord> getMigrationRecordsByTableMapping(String sourceTableName, String targetTableName);
    
    /**
     * 检查表迁移是否已经存在
     */
    boolean isTableMigrationExists(String sourceTableName, String targetTableName);
    
    /**
     * 删除源表的所有历史迁移记录（物理删除）
     * @param sourceTableName 源表名
     * @return 删除的记录数
     */
    int deleteAllHistoryBySourceTable(String sourceTableName);
    
    /**
     * 记录单个表的迁移过程
     */
} 