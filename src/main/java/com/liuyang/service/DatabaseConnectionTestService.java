package com.liuyang.service;

import com.liuyang.dto.DatabaseType;
import java.util.Map;

/**
 * 数据库连接测试服务接口
 */
public interface DatabaseConnectionTestService {
    
    /**
     * 测试单个数据库连接
     * 
     * @param dbType 数据库类型
     * @param databaseName 数据库名称
     * @return 测试结果
     */
    Map<String, Object> testConnection(DatabaseType dbType, String databaseName);
    
    /**
     * 测试所有数据库连接
     * 
     * @return 测试结果
     */
    Map<String, Object> testAllConnections();
}
