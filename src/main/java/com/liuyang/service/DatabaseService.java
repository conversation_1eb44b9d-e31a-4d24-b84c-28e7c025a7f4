package com.liuyang.service;

import com.liuyang.dto.DatabaseInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.entity.DatabaseConfigEntity;
import com.liuyang.exception.DatabaseException;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public interface DatabaseService {

    /**
     * 获取PostgreSQL数据库连接（源数据库）
     */
    public Connection getConnection(String databaseName) throws SQLException ;

    /**
     * 获取源数据库连接
     */
    public Connection getSourceConnection(String databaseName) throws SQLException ;

    /**
     * 获取目标数据库连接
     */
    public Connection getTargetConnection(String databaseName) throws SQLException ;

    /**
     * 获取所有数据库列表（源数据库）
     */
    public List<DatabaseInfo> getAllDatabases() throws DatabaseException ;


    /**
     * 检查数据库是否存在
     */
    public boolean databaseExists(String databaseName);


    /**
     * 测试数据库连接
     */
    public boolean testConnection(String databaseName) ;

    /**
     * 获取当前使用的源数据库配置信息
     */
    public DatabaseConfigEntity getCurrentSourceConfig();

    /**
     * 获取当前使用的目标数据库配置信息
     */
    public DatabaseConfigEntity getCurrentTargetConfig();

}
