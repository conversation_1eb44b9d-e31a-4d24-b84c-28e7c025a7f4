package com.liuyang.service;

import com.liuyang.dto.FieldMapping;
import com.liuyang.dto.TableStructure;

import java.util.List;

/**
 * AI字段命名服务接口
 * 定义AI字段命名相关的服务方法
 */
public interface AiFieldNamingService {

    /**
     * 使用AI生成字段映射建议
     * 
     * @param sourceTable 源表结构
     * @param targetTableName 目标表名
     * @param businessContext 业务上下文
     * @return 字段映射建议列表
     */
    List<FieldMapping> generateFieldMappingSuggestions(
            TableStructure sourceTable, 
            String targetTableName,
            String businessContext);

    /**
     * 优化单个字段名
     * 
     * @param originalFieldName 原字段名
     * @param fieldType 字段类型
     * @param comment 字段注释
     * @param businessContext 业务上下文
     * @return 优化后的字段名
     */
    String optimizeFieldName(
            String originalFieldName, 
            String fieldType, 
            String comment, 
            String businessContext);

    /**
     * 批量优化字段映射
     * 
     * @param originalMappings 原始字段映射列表
     * @param businessContext 业务上下文
     * @return 优化后的字段映射列表
     */
    List<FieldMapping> optimizeFieldMappings(
            List<FieldMapping> originalMappings, 
            String businessContext);

    /**
     * 验证字段名是否符合命名规范
     * 
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @return 验证结果和建议
     */
    FieldNameValidationResult validateFieldName(String fieldName, String fieldType);

    /**
     * 生成表结构创建建议
     * 
     * @param tableName 表名
     * @param businessDescription 业务描述
     * @param fieldCount 预期字段数量
     * @return 表结构建议
     */
    TableStructureSuggestion generateTableStructureSuggestion(
            String tableName, 
            String businessDescription, 
            Integer fieldCount);

    /**
     * 生成字段注释
     * 
     * @param fieldName 字段名
     * @param fieldType 字段类型
     * @param originalComment 原始注释
     * @param businessContext 业务上下文
     * @return 生成的字段注释
     */
    String generateFieldComment(
            String fieldName, 
            String fieldType, 
            String originalComment, 
            String businessContext);

    /**
     * 字段名验证结果
     */
    class FieldNameValidationResult {
        private boolean valid;
        private String suggestion;
        private String reason;
        private String improvedName;

        // 构造函数
        public FieldNameValidationResult(boolean valid, String suggestion, String reason, String improvedName) {
            this.valid = valid;
            this.suggestion = suggestion;
            this.reason = reason;
            this.improvedName = improvedName;
        }

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }

        public String getSuggestion() { return suggestion; }
        public void setSuggestion(String suggestion) { this.suggestion = suggestion; }

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }

        public String getImprovedName() { return improvedName; }
        public void setImprovedName(String improvedName) { this.improvedName = improvedName; }
    }

    /**
     * 表结构建议
     */
    class TableStructureSuggestion {
        private String tableName;
        private String description;
        private List<FieldSuggestion> fields;

        // 构造函数
        public TableStructureSuggestion() {}

        public TableStructureSuggestion(String tableName, String description, List<FieldSuggestion> fields) {
            this.tableName = tableName;
            this.description = description;
            this.fields = fields;
        }

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public List<FieldSuggestion> getFields() { return fields; }
        public void setFields(List<FieldSuggestion> fields) { this.fields = fields; }
    }

    /**
     * 字段建议
     */
    class FieldSuggestion {
        private String fieldName;
        private String fieldType;
        private String comment;
        private boolean nullable;
        private String defaultValue;
        private boolean primaryKey;

        // 构造函数
        public FieldSuggestion() {}

        public FieldSuggestion(String fieldName, String fieldType, String comment, boolean nullable) {
            this.fieldName = fieldName;
            this.fieldType = fieldType;
            this.comment = comment;
            this.nullable = nullable;
        }

        // Getters and Setters
        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public boolean isNullable() { return nullable; }
        public void setNullable(boolean nullable) { this.nullable = nullable; }

        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }

        public boolean isPrimaryKey() { return primaryKey; }
        public void setPrimaryKey(boolean primaryKey) { this.primaryKey = primaryKey; }
    }
}
