package com.liuyang.service;

import com.liuyang.dto.DatabaseConnectionConfig;
import com.liuyang.dto.DatabaseInfo;
import com.liuyang.dto.DatabaseType;
import com.liuyang.exception.DatabaseException;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public interface DynamicDatabaseService {

    /**
     * 测试数据库连接
     */
    public boolean testConnection(DatabaseConnectionConfig config);

    /**
     * 获取数据库列表
     */
    public List<DatabaseInfo> getDatabaseList(DatabaseConnectionConfig config);


    /**
     * 获取数据库连接
     */
    public Connection getConnection(DatabaseConnectionConfig config, String databaseName) throws SQLException ;

    /**
     * 获取表列表（使用动态连接）
     */
    public List<String> getTableList(DatabaseConnectionConfig config, String databaseName) ;
}
