package com.liuyang.service;

import com.liuyang.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    // 传统Session登录方法已删除，改用Sa-Token JWT认证
    
    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);
    
    /**
     * 根据ID查询用户
     */
    User getUserById(Long id);
    
    /**
     * 查询所有用户
     */
    List<User> getAllUsers();
    
    /**
     * 创建用户
     */
    User createUser(User user, String operator);
    
    /**
     * 更新用户
     */
    User updateUser(User user, String operator);
    
    /**
     * 删除用户
     */
    void deleteUser(Long id, String operator);
    
    /**
     * 更改用户状态
     */
    void changeUserStatus(Long id, Integer status, String operator);
    
    /**
     * 重置用户密码
     */
    void resetPassword(Long id, String newPassword, String operator);
    
    /**
     * 修改密码
     */
    void changePassword(Long id, String oldPassword, String newPassword);
    
    /**
     * 检查用户名是否存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 检查用户名是否存在（排除指定用户）
     */
    boolean isUsernameExists(String username, Long excludeUserId);
    
    /**
     * 搜索用户
     */
    List<User> searchUsers(String username, String realName, String role, Integer status);
    
    // Session相关方法已删除，改用Sa-Token JWT认证

    /**
     * 更新用户最后登录信息
     */
    void updateLastLogin(Long userId, String loginIp);
    

}
