package com.liuyang.service;

import com.liuyang.dto.ColumnInfo;
import com.liuyang.dto.TableInfo;
import com.liuyang.dto.TableStructure;
import com.liuyang.exception.DatabaseException;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TableService {

    /**
     * 获取指定数据库的表列表
     */
    public List<TableInfo> getTablesByDatabase(String databaseName) ;

    /**
     * 获取表结构信息
     */
    public TableStructure getTableStructure(String databaseName, String tableName);

    /**
     * 检查表是否存在
     */
    public boolean tableExists(String databaseName, String tableName) ;

}
