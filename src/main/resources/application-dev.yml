# 开发环境配置
spring:
  application:
    name: DB_CopyData-Dev

  # 数据源配置 - 开发环境 (本地MySQL)
  datasource:
    url: ********************************************************************************************************************************************************************************************************************************************************
    username: root
    password: liuyang
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
      validation-timeout: 5000
      initialization-fail-timeout: 1

# 服务器配置 - 开发环境
server:
  port: 8888

# 应用自定义配置 - 开发环境
app:
  # 配置管理数据库连接配置 (本地MySQL)
  config:
    database:
      host: 127.0.0.1
      port: 3306
      username: root
      password: liuyang

  # 表复制配置 - 开发环境
  copy:
    batch-size: 500           # 开发环境使用较小的批次
    max-connections: 5        # 开发环境使用较少的连接
    timeout: 30

  # 数据迁移配置 - 开发环境
  migration:
    big-data:
      large-data-threshold: 100000
      small-batch-size: 2000
      medium-batch-size: 5000
      large-batch-size: 8000
      xlarge-batch-size: 10000
      max-threads: 8
      query-timeout-seconds: 300
      batch-timeout-seconds: 600
    high-performance:
      # 分片配置
      shard-size: 100000
      max-parallel-threads: 16
      min-parallel-threads: 1
      # 批次配置
      max-batch-size: 50000
      min-batch-size: 5000
      optimal-batch-size: 20000
      # 连接池配置
      connection-pool-size: 20
      connection-timeout: 60000
      socket-timeout: 300000
      idle-timeout: 300000
      max-lifetime: 1800000
      # 监控配置
      progress-report-interval: 10
      enable-detailed-logging: false
      enable-performance-metrics: true
      # 优化配置
      enable-batch-rewrite: true
      enable-prepared-statement-cache: true
      enable-connection-optimization: true
      enable-database-specific-optimization: true
      # 内存配置
      fetch-size: 10000
      result-set-cache-size: 1000
      # 错误处理配置
      max-retry-attempts: 3
      retry-delay-ms: 1000
      fail-fast-on-error: false
      # 性能阈值配置
      large-dataset-threshold: 1000000
      huge-dataset-threshold: 10000000
      min-acceptable-speed: 1000.0

# 日志配置 - 开发环境
logging:
  level:
    com.liuyang: DEBUG
    org.springframework.jdbc: DEBUG
    org.springframework.web: DEBUG
    org.springframework.transaction: DEBUG
    root: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/db-copy-data-dev.log
    max-size: 5MB
    max-history: 10

# 管理端点配置 - 开发环境（开放更多端点）
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# Sa-Token配置 - 开发环境
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: DB_COPY_TOKEN
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 86400
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # JWT秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz1234567890
