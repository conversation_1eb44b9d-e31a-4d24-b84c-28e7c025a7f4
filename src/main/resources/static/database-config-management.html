<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库配置管理 - DB_CopyData</title>
    <link rel="stylesheet" href="libs/element-plus.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        

        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px;
            text-align: center;
            box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 500;
        }
        
        .container {
            max-width: 1400px;
            margin: 10px auto;
            padding: 0 15px;
            width: calc(100% - 30px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
            margin-bottom: 10px;
        }
        
        .stat-card {
            background: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 22px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #606266;
            font-size: 12px;
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
        }
        
        /* 移除原有的表格容器样式，使用下方优化后的样式 */
        
        .status-active {
            color: #67c23a;
        }
        
        .status-inactive {
            color: #f56c6c;
        }
        
        .config-type-source {
            color: #409eff;
            background: #ecf5ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .config-type-target {
            color: #67c23a;
            background: #f0f9ff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .database-type-postgresql {
            color: #336791;
            background: #e8f4f8;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .database-type-mysql {
            color: #e97627;
            background: #fdf6ec;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        /* 表格容器样式 */
        .table-container {
            width: 100%;
            overflow-x: auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            max-width: 100%;
        }
        
        /* 表格样式优化 */
        .el-table {
            font-size: 12px;
            border-radius: 8px;
            overflow: hidden;
            width: 100% !important;
            table-layout: auto;
        }
        
        /* 确保表格占满容器宽度 */
        .el-table__body-wrapper {
            width: 100% !important;
        }
        
        .el-table__header-wrapper {
            width: 100% !important;
        }
        
        .el-table th {
            background-color: #f8f9fa !important;
            color: #495057 !important;
            font-weight: 600;
            padding: 8px 0;
            font-size: 12px;
        }
        
        .el-table td {
            padding: 4px 0;
        }
        
        /* 紧凑的按钮样式 */
        .el-table .el-button--small {
            padding: 2px 6px;
            font-size: 10px;
            min-height: 22px;
            line-height: 1.2;
            border-radius: 3px;
        }
        
        /* 操作按钮横向布局 */
        .action-buttons {
            display: flex;
            flex-direction: row;
            gap: 3px;
            width: 100%;
            justify-content: center;
            align-items: center;
            flex-wrap: nowrap;
            padding: 0 8px;
        }
        
        .action-buttons .el-button {
            flex: 0 0 auto;
            min-width: 38px;
        }
        
        /* Tag样式优化 */
        .el-tag--small {
            height: 16px;
            line-height: 14px;
            font-size: 9px;
            padding: 0 3px;
            transform: scale(0.9);
        }
        
        /* 配置对话框样式 - 标题居中，内容左对齐 */
        .el-dialog .el-dialog__header {
            text-align: center !important;
        }
        
        .el-dialog .el-dialog__title {
            text-align: center !important;
        }
        
        .el-dialog .el-form {
            text-align: left;
        }
        
        .el-dialog .el-form .el-form-item {
            text-align: left;
        }
        
        .el-dialog .el-form .el-form-item__label {
            text-align: left !important;
            justify-content: flex-start !important;
        }
        
        .el-dialog .el-form .el-form-item__content {
            text-align: left !important;
            justify-content: flex-start !important;
        }
        
        .el-dialog .el-form .el-radio-group {
            text-align: left;
            justify-content: flex-start;
        }
        
        .el-dialog .el-form .el-radio {
            margin-right: 20px;
        }
        
        .el-dialog .el-form .el-input,
        .el-dialog .el-form .el-input-number,
        .el-dialog .el-form .el-textarea {
            text-align: left;
        }
        
        /* 对话框底部按钮左对齐 */
        .el-dialog .dialog-footer {
            text-align: left !important;
        }

        /* 响应式表格样式 */
        @media (max-width: 1400px) {
            .container {
                margin: 20px 10px;
                padding: 0 10px;
                max-width: calc(100vw - 40px);
            }
            
            .table-container {
                overflow-x: auto;
                max-width: 100%;
            }
            
            .el-table {
                min-width: 650px;
            }
        }
        
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .toolbar > div {
                justify-content: center;
                flex-wrap: wrap;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px 5px;
                padding: 0 5px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .stat-card {
                padding: 15px;
            }
            
            .stat-number {
                font-size: 24px;
            }
            
            .toolbar {
                padding: 15px;
            }
            
            .el-table {
                min-width: 600px;
            }
            
            .action-buttons {
                flex-wrap: wrap;
                gap: 1px;
            }
            
            .action-buttons .el-button {
                min-width: 30px;
                padding: 1px 3px;
                font-size: 9px;
            }
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">


        <!-- 页面头部 -->
        <div class="header">
            <h1>🗄️ 数据库配置管理</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">集中管理源数据库和目标数据库配置信息</p>
        </div>

        <!-- 主容器 -->
        <div class="container">
            <!-- 统计卡片 -->
            <div class="stats-grid" v-loading="statsLoading">
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
                    <div class="stat-label">总配置数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.activeCount || 0 }}</div>
                    <div class="stat-label">启用配置</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.sourceCount || 0 }}</div>
                    <div class="stat-label">源数据库配置</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ statistics.targetCount || 0 }}</div>
                    <div class="stat-label">目标数据库配置</div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div style="display: flex; gap: 16px; align-items: center;">
                    <el-button type="primary" @click="showAddDialog" icon="Plus">
                        新增配置
                    </el-button>
                    <el-button @click="batchTestConnection" :disabled="!selectedConfigs.length" icon="Connection">
                        批量测试连接
                    </el-button>
                    <el-button @click="refreshData" icon="Refresh">刷新</el-button>
                </div>
            </div>

            <!-- 配置列表 -->
            <div class="table-container">
                <el-table 
                    :data="configs" 
                    v-loading="loading"
                    @selection-change="handleSelectionChange"
                    height="600"
                    stripe
                    size="small"
                    style="width: 100%">
                    
                    <el-table-column type="selection" width="35"/>
                    
                    <el-table-column prop="configName" label="配置名称" width="140" show-overflow-tooltip>
                        <template #default="scope">
                            <div style="font-size: 12px;">
                                <div v-if="scope.row.configName" style="font-weight: 500;">{{ scope.row.configName }}</div>
                                <div v-else style="color: #999; font-style: italic;">未命名配置</div>
                                <div style="color: #666; font-size: 10px;">
                                    <el-tag v-if="scope.row.configType === 'SOURCE'" type="primary" size="small" style="transform: scale(0.8); margin-right: 2px;">源</el-tag>
                                    <el-tag v-else-if="scope.row.configType === 'TARGET'" type="success" size="small" style="transform: scale(0.8); margin-right: 2px;">目标</el-tag>
                                    <span v-if="scope.row.databaseType">{{ scope.row.databaseType === 'POSTGRESQL' ? 'PG' : 'MySQL' }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="连接信息" width="160">
                        <template #default="scope">
                            <div style="font-size: 11px;">
                                <div style="font-family: monospace; font-weight: 500;">
                                    {{ scope.row.host }}:{{ scope.row.port }}
                                </div>
                                <div style="color: #666;">
                                    {{ scope.row.username }}
                                    <span v-if="scope.row.defaultDatabase">@{{ scope.row.defaultDatabase }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="环境/状态" width="80" align="center">
                        <template #default="scope">
                            <div style="display: flex; flex-direction: column; gap: 1px;">
                                <el-tag size="small" :type="scope.row.environment === 'prod' ? 'danger' : (scope.row.environment === 'dev' ? 'warning' : 'info')" style="transform: scale(1.0);">
                                    {{ scope.row.environment }}
                                </el-tag>
                                <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small" style="transform: scale(1.0);">
                                    {{ scope.row.isActive ? '启用' : '禁用' }}
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="连接状态" width="90" align="center">
                        <template #default="scope">
                            <div style="display: flex; flex-direction: column; gap: 2px; align-items: center;">
                                <el-tag v-if="scope.row.connectionStatus === 'success'" type="success" size="small" style="transform: scale(0.8);">
                                    <i class="el-icon-success" style="margin-right: 2px;"></i>成功
                                </el-tag>
                                <el-tag v-else-if="scope.row.connectionStatus === 'failed'" type="danger" size="small" style="transform: scale(0.8);">
                                    <i class="el-icon-error" style="margin-right: 2px;"></i>失败
                                </el-tag>
                                <el-tag v-else-if="scope.row.connectionStatus === 'testing'" type="warning" size="small" style="transform: scale(0.8);">
                                    <i class="el-icon-loading" style="margin-right: 2px;"></i>测试中
                                </el-tag>
                                <el-tag v-else type="info" size="small" style="transform: scale(0.8);">
                                    <i class="el-icon-question" style="margin-right: 2px;"></i>未测试
                                </el-tag>
                                <div v-if="scope.row.lastTestTime" style="font-size: 9px; color: #999; line-height: 1;">
                                    {{ formatTime(scope.row.lastTestTime) }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.description" style="font-size: 11px;">{{ scope.row.description }}</span>
                            <span v-else style="color: #999; font-size: 11px;">无描述</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="210" fixed="right">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button size="small" @click="testConnection(scope.row)" type="primary" plain>
                                    测试
                                </el-button>
                                <el-button size="small" @click="editConfig(scope.row)" type="success" plain>
                                    编辑
                                </el-button>
                                <el-button size="small" 
                                           :type="scope.row.isActive ? 'warning' : 'success'"
                                           @click="toggleStatus(scope.row)" plain>
                                    {{ scope.row.isActive ? '禁用' : '启用' }}
                                </el-button>
                                <el-button size="small" type="danger" @click="deleteConfig(scope.row)" plain>
                                    删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="padding: 15px 20px; text-align: center;">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[20, 50, 100, 200]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="loadConfigs"
                        @current-change="loadConfigs"/>
                </div>
            </div>
        </div>

        <!-- 新增/编辑配置对话框 -->
        <el-dialog 
            v-model="configDialog.visible" 
            :title="configDialog.isEdit ? '编辑配置' : '新增配置'"
            width="600px"
            @close="resetConfigForm">
            
            <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="120px">
                <el-form-item label="配置名称" prop="configName">
                    <el-input v-model="configForm.configName" placeholder="请输入配置名称"/>
                </el-form-item>
                
                <el-form-item label="配置类型" prop="configType">
                    <el-radio-group v-model="configForm.configType">
                        <el-radio label="SOURCE">源数据库</el-radio>
                        <el-radio label="TARGET">目标数据库</el-radio>
                    </el-radio-group>
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                        系统固定支持PostgreSQL迁移到MySQL，选择后会自动设置数据库类型和默认值
                    </div>
                </el-form-item>
                
                <el-form-item label="数据库类型" prop="databaseType">
                    <el-radio-group v-model="configForm.databaseType" disabled>
                        <el-radio label="POSTGRESQL">PostgreSQL</el-radio>
                        <el-radio label="MYSQL">MySQL</el-radio>
                    </el-radio-group>
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                        根据配置类型自动设置：源数据库=PostgreSQL，目标数据库=MySQL
                    </div>
                </el-form-item>
                
                <el-form-item label="主机地址" prop="host">
                    <el-input v-model="configForm.host" placeholder="请输入主机地址"/>
                </el-form-item>
                
                <el-form-item label="端口" prop="port">
                    <el-input-number v-model="configForm.port" :min="1" :max="65535" style="width: 100%;"/>
                </el-form-item>
                
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="configForm.username" placeholder="请输入用户名"/>
                </el-form-item>
                
                <el-form-item label="密码" prop="password">
                    <el-input v-model="configForm.password" type="password" placeholder="请输入密码" show-password/>
                </el-form-item>
                
                <el-form-item label="默认数据库">
                    <el-input v-model="configForm.defaultDatabase" placeholder="可选，默认连接的数据库"/>
                </el-form-item>
                
                <el-form-item label="环境" prop="environment">
                    <el-input v-model="configForm.environment" placeholder="如：dev, test, prod, default"/>
                </el-form-item>
                
                <el-form-item label="描述">
                    <el-input v-model="configForm.description" type="textarea" placeholder="配置描述"/>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="configDialog.visible = false">取消</el-button>
                    <el-button type="primary" @click="saveConfig" :loading="configDialog.saving">
                        {{ configDialog.isEdit ? '更新' : '保存' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>

    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载数据库配置管理页面...');

                cdnLoader.updateProgress(20);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(50);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('elementPlusIconsJs');

                cdnLoader.updateProgress(90);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
            }
        }

        function initVueApp() {
            const { createApp, ref, reactive, onMounted, watch, nextTick } = Vue;
            const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
        
        createApp({
            setup() {
                // 响应式数据
                const loading = ref(false);
                const statsLoading = ref(false);
                const configs = ref([]);
                const environments = ref([]);
                const selectedConfigs = ref([]);
                
                const statistics = reactive({
                    totalCount: 0,
                    activeCount: 0,
                    sourceCount: 0,
                    targetCount: 0
                });
                
                const searchParams = reactive({
                    environment: '',
                    configType: '',
                    databaseType: '',
                    keyword: ''
                });
                
                const pagination = reactive({
                    pageNum: 1,
                    pageSize: 20,
                    total: 0
                });
                
                const configDialog = reactive({
                    visible: false,
                    isEdit: false,
                    saving: false
                });
                
                const configForm = reactive({
                    id: null,
                    configName: '',
                    configType: 'SOURCE',
                    databaseType: 'POSTGRESQL',
                    host: '127.0.0.1',
                    port: 5432,
                    username: 'postgres',
                    password: '',
                    defaultDatabase: '',
                    environment: 'default',
                    description: '',
                    isActive: true
                });
                
                const configRules = {
                    configName: [
                        { required: true, message: '请输入配置名称', trigger: 'blur' }
                    ],
                    configType: [
                        { required: true, message: '请选择配置类型', trigger: 'change' }
                    ],
                    databaseType: [
                        { required: true, message: '请选择数据库类型', trigger: 'change' }
                    ],
                    host: [
                        { required: true, message: '请输入主机地址', trigger: 'blur' }
                    ],
                    port: [
                        { required: true, message: '请输入端口', trigger: 'blur' }
                    ],
                    username: [
                        { required: true, message: '请输入用户名', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' }
                    ],
                    environment: [
                        { required: true, message: '请输入环境', trigger: 'blur' }
                    ]
                };
                
                // 配置axios默认设置
                axios.defaults.headers.post['Content-Type'] = 'application/json';
                
                // 本地存储工具函数
                const CONNECTION_STATUS_KEY = 'db_config_connection_status';
                
                const saveConnectionStatus = (configId, status, message, testTime) => {
                    try {
                        const stored = JSON.parse(localStorage.getItem(CONNECTION_STATUS_KEY) || '{}');
                        stored[configId] = {
                            status,
                            message,
                            testTime: testTime || new Date().getTime()
                        };
                        localStorage.setItem(CONNECTION_STATUS_KEY, JSON.stringify(stored));
                    } catch (error) {
                        console.error('保存连接状态到本地存储失败:', error);
                    }
                };
                
                const loadConnectionStatuses = () => {
                    try {
                        return JSON.parse(localStorage.getItem(CONNECTION_STATUS_KEY) || '{}');
                    } catch (error) {
                        console.error('从本地存储加载连接状态失败:', error);
                        return {};
                    }
                };
                
                const clearConnectionStatus = (configId) => {
                    try {
                        const stored = JSON.parse(localStorage.getItem(CONNECTION_STATUS_KEY) || '{}');
                        delete stored[configId];
                        localStorage.setItem(CONNECTION_STATUS_KEY, JSON.stringify(stored));
                    } catch (error) {
                        console.error('清除连接状态失败:', error);
                    }
                };
                
                // API方法
                const api = {
                    async getConfigs(params) {
                        const response = await axios.get('/api/database-config/list', { params });
                        return response.data;
                    },
                    
                    async saveConfig(config) {
                        const response = await axios.post('/api/database-config/save', config, {
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        return response.data;
                    },
                    
                    async deleteConfig(id) {
                        const response = await axios.delete(`/api/database-config/${id}`);
                        return response.data;
                    },
                    
                    async testConnection(config) {
                        const response = await axios.post('/api/database-config/test-connection', config);
                        return response.data;
                    },
                    
                    async toggleStatus(id, isActive) {
                        const response = await axios.post(`/api/database-config/${id}/toggle-status?isActive=${isActive}`);
                        return response.data;
                    },
                    
                    async getEnvironments() {
                        const response = await axios.get('/api/database-config/environments');
                        return response.data;
                    },
                    
                    async getStatistics() {
                        const response = await axios.get('/api/database-config/statistics');
                        return response.data;
                    },
                    
                    async batchTestConnection(configIds) {
                        const response = await axios.post('/api/database-config/batch-test-connection', configIds);
                        return response.data;
                    }
                };
                
                // 业务方法
                const loadConfigs = async () => {
                    loading.value = true;
                    try {
                        const params = {
                            pageNum: pagination.pageNum,
                            pageSize: pagination.pageSize,
                            ...searchParams
                        };
                        
                        const result = await api.getConfigs(params);
                        if (result.success) {
                            configs.value = result.data;
                            pagination.total = result.total;
                            
                            // 从本地存储恢复连接状态
                            const storedStatuses = loadConnectionStatuses();
                            configs.value.forEach(config => {
                                const stored = storedStatuses[config.id];
                                if (stored) {
                                    config.connectionStatus = stored.status;
                                    config.connectionMessage = stored.message;
                                    config.lastTestTime = new Date(stored.testTime);
                                }
                            });
                        }
                    } catch (error) {
                        ElMessage.error('加载配置列表失败: ' + error.message);
                    } finally {
                        loading.value = false;
                    }
                };
                
                const loadEnvironments = async () => {
                    try {
                        const result = await api.getEnvironments();
                        if (result.success) {
                            environments.value = result.data;
                        }
                    } catch (error) {
                        console.error('加载环境列表失败:', error);
                    }
                };
                
                const loadStatistics = async () => {
                    statsLoading.value = true;
                    try {
                        const result = await api.getStatistics();
                        if (result.success) {
                            Object.assign(statistics, result.data);
                        }
                    } catch (error) {
                        console.error('加载统计信息失败:', error);
                    } finally {
                        statsLoading.value = false;
                    }
                };
                
                const refreshData = () => {
                    loadConfigs();
                    loadStatistics();
                };
                
                const showAddDialog = () => {
                    configDialog.isEdit = false;
                    configDialog.visible = true;
                    resetConfigForm();
                };

                const handleSelectionChange = (selection) => {
                    selectedConfigs.value = selection;
                };

                const batchTestConnection = async () => {
                    if (!selectedConfigs.value.length) {
                        ElMessage.warning('请先选择要测试的配置');
                        return;
                    }

                    // 设置选中配置为测试中状态
                    selectedConfigs.value.forEach(config => {
                        config.connectionStatus = 'testing';
                        config.lastTestTime = new Date();
                    });

                    const loading = ElLoading.service({
                        lock: true,
                        text: `正在测试 ${selectedConfigs.value.length} 个配置的连接...`,
                        spinner: 'el-icon-loading'
                    });

                    try {
                        const configIds = selectedConfigs.value.map(config => config.id);
                        const result = await api.batchTestConnection(configIds);
                        
                        if (result.success) {
                            const results = result.data;
                            let successCount = 0;
                            let failCount = 0;
                            
                            // 更新配置列表中的连接状态
                            results.forEach(testResult => {
                                const config = configs.value.find(c => c.id === testResult.configId);
                                if (config) {
                                    config.connectionStatus = testResult.connected ? 'success' : 'failed';
                                    config.lastTestTime = new Date();
                                    config.connectionMessage = testResult.message;
                                    
                                    // 保存状态到本地存储
                                    saveConnectionStatus(
                                        config.id, 
                                        testResult.connected ? 'success' : 'failed', 
                                        testResult.message
                                    );
                                }
                                
                                if (testResult.connected) successCount++;
                                else failCount++;
                            });
                            
                            ElMessage.success(`批量测试完成: ${successCount} 个成功, ${failCount} 个失败`);
                            
                            // 显示详细结果
                            if (failCount > 0) {
                                const failedConfigs = results.filter(r => !r.connected);
                                const failedNames = failedConfigs.map(r => r.configName).join(', ');
                                ElMessage.warning(`连接失败的配置: ${failedNames}`);
                            }
                        } else {
                            // 测试失败，恢复状态
                            selectedConfigs.value.forEach(config => {
                                config.connectionStatus = 'failed';
                                config.connectionMessage = result.message;
                                config.lastTestTime = new Date();
                                
                                // 保存失败状态到本地存储
                                saveConnectionStatus(config.id, 'failed', result.message);
                            });
                            ElMessage.error(result.message);
                        }
                    } catch (error) {
                        // 发生异常，恢复状态
                        selectedConfigs.value.forEach(config => {
                            config.connectionStatus = 'failed';
                            config.connectionMessage = error.message;
                            config.lastTestTime = new Date();
                            
                            // 保存错误状态到本地存储
                            saveConnectionStatus(config.id, 'failed', error.message);
                        });
                        ElMessage.error('批量测试失败: ' + error.message);
                    } finally {
                        loading.close();
                    }
                };
                
                const editConfig = (config) => {
                    configDialog.isEdit = true;
                    configDialog.visible = true;
                    Object.assign(configForm, config);
                };
                
                const resetConfigForm = () => {
                    Object.assign(configForm, {
                        id: null,
                        configName: '',
                        configType: 'SOURCE',
                        databaseType: 'POSTGRESQL',
                        host: '127.0.0.1',
                        port: 5432,
                        username: 'postgres',
                        password: '',
                        defaultDatabase: '',
                        environment: 'default',
                        description: '',
                        isActive: true
                    });
                    
                    // 清除表单验证状态
                    nextTick(() => {
                        if (configFormRef.value) {
                            configFormRef.value.clearValidate();
                        }
                    });
                };
                
                // 创建表单引用
                const configFormRef = ref();
                
                const saveConfig = async () => {
                    if (!configFormRef.value) {
                        ElMessage.error('表单引用错误，请重新打开对话框');
                        return;
                    }
                    
                    try {
                        await configFormRef.value.validate();
                        configDialog.saving = true;
                        
                        const result = await api.saveConfig(configForm);
                        
                        if (result.success) {
                            ElMessage.success(configDialog.isEdit ? '更新成功' : '保存成功');
                            configDialog.visible = false;
                            refreshData();
                        } else {
                            ElMessage.error(result.message);
                        }
                    } catch (error) {
                        if (typeof error === 'object' && error.message) {
                            ElMessage.error('保存失败: ' + error.message);
                        } else if (error !== false) { // 表单验证失败时会返回false
                            ElMessage.error('表单验证失败，请检查输入');
                        }
                    } finally {
                        configDialog.saving = false;
                    }
                };
                
                const deleteConfig = async (config) => {
                    try {
                        await ElMessageBox.confirm(
                            `确定要删除配置 "${config.configName || '未命名配置'}" 吗？此操作不可恢复！`,
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );
                        
                        const result = await api.deleteConfig(config.id);
                        if (result.success) {
                            // 清除本地存储中的连接状态
                            clearConnectionStatus(config.id);
                            
                            ElMessage.success('删除成功');
                            refreshData();
                        } else {
                            ElMessage.error(result.message);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('删除失败: ' + error.message);
                        }
                    }
                };
                
                const testConnection = async (config) => {
                    // 设置为测试中状态
                    config.connectionStatus = 'testing';
                    config.lastTestTime = new Date();
                    
                    const loading = ElLoading.service({
                        lock: true,
                        text: '正在测试连接...',
                        spinner: 'el-icon-loading'
                    });
                    
                    try {
                        const result = await api.testConnection(config);
                        if (result.success) {
                            if (result.connected) {
                                config.connectionStatus = 'success';
                                config.connectionMessage = '连接成功';
                                config.lastTestTime = new Date();
                                
                                // 保存成功状态到本地存储
                                saveConnectionStatus(config.id, 'success', '连接成功');
                                
                                ElMessage.success('连接成功');
                            } else {
                                config.connectionStatus = 'failed';
                                config.connectionMessage = result.message || '连接失败';
                                config.lastTestTime = new Date();
                                
                                // 保存失败状态到本地存储
                                saveConnectionStatus(config.id, 'failed', result.message || '连接失败');
                                
                                ElMessage.error('连接失败');
                            }
                        } else {
                            config.connectionStatus = 'failed';
                            config.connectionMessage = result.message;
                            config.lastTestTime = new Date();
                            
                            // 保存失败状态到本地存储
                            saveConnectionStatus(config.id, 'failed', result.message);
                            
                            ElMessage.error(result.message);
                        }
                    } catch (error) {
                        config.connectionStatus = 'failed';
                        config.connectionMessage = error.message;
                        config.lastTestTime = new Date();
                        
                        // 保存错误状态到本地存储
                        saveConnectionStatus(config.id, 'failed', error.message);
                        
                        ElMessage.error('测试连接失败: ' + error.message);
                    } finally {
                        loading.close();
                    }
                };
                
                const toggleStatus = async (config) => {
                    try {
                        const result = await api.toggleStatus(config.id, !config.isActive);
                        if (result.success) {
                            ElMessage.success(result.message);
                            refreshData();
                        } else {
                            ElMessage.error(result.message);
                        }
                    } catch (error) {
                        ElMessage.error('操作失败: ' + error.message);
                    }
                };
                

                
                // 时间格式化函数
                const formatTime = (time) => {
                    if (!time) return '';
                    const date = new Date(time);
                    const now = new Date();
                    const diff = now - date;
                    
                    // 如果是今天
                    if (diff < 24 * 60 * 60 * 1000) {
                        const hours = Math.floor(diff / (60 * 60 * 1000));
                        const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));
                        
                        if (hours > 0) {
                            return `${hours}小时前`;
                        } else if (minutes > 0) {
                            return `${minutes}分钟前`;
                        } else {
                            return '刚刚';
                        }
                    } else {
                        // 如果不是今天，显示日期
                        return date.toLocaleDateString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    }
                };

                // 监听配置类型变化，自动设置数据库类型和默认值
                watch(() => configForm.configType, (newType) => {
                    if (newType === 'SOURCE') {
                        // 源数据库固定为PostgreSQL
                        configForm.databaseType = 'POSTGRESQL';
                        configForm.port = 5432;
                        configForm.username = 'postgres';
                        configForm.host = '127.0.0.1';
                    } else if (newType === 'TARGET') {
                        // 目标数据库固定为MySQL
                        configForm.databaseType = 'MYSQL';
                        configForm.port = 3306;
                        configForm.username = 'root';
                        configForm.host = '127.0.0.1';
                    }
                });

                // 监听数据库类型变化，自动设置默认端口和用户名
                watch(() => configForm.databaseType, (newType) => {
                    if (newType === 'POSTGRESQL') {
                        configForm.port = 5432;
                        configForm.username = 'postgres';
                        configForm.host = '127.0.0.1';
                    } else if (newType === 'MYSQL') {
                        configForm.port = 3306;
                        configForm.username = 'root';
                        configForm.host = '127.0.0.1';
                    }
                });
                
                // 组件挂载
                onMounted(() => {
                    loadEnvironments();
                    loadConfigs();
                    loadStatistics();
                });
                
                return {
                    loading,
                    statsLoading,
                    configs,
                    environments,
                    selectedConfigs,
                    statistics,
                    searchParams,
                    pagination,
                    configDialog,
                    configForm,
                    configRules,
                    configFormRef,
                    
                    loadConfigs,
                    refreshData,
                    showAddDialog,
                    editConfig,
                    resetConfigForm,
                    saveConfig,
                    deleteConfig,
                    testConnection,
                    toggleStatus,
                    handleSelectionChange,
                    batchTestConnection,
                    formatTime,
                    
                    // 本地存储工具函数（调试用）
                    saveConnectionStatus,
                    loadConnectionStatuses,
                    clearConnectionStatus
                };
            }
        }).use(ElementPlus).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>