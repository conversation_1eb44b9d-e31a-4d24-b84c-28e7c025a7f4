<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sa-Token测试页面 - 数据库迁移系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section-title {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #34495e;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .status-card.logged-in {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .status-card.logged-out {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }

        .status-card h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="container">
            <div class="header">
                <h1 class="title">🔐 Sa-Token测试页面</h1>
                <p class="subtitle">JWT + Sa-Token认证功能测试</p>
            </div>

            <!-- 登录状态显示 -->
            <div class="section">
                <h3 class="section-title">📊 当前状态</h3>
                <div class="status-info">
                    <div class="status-card" :class="isLoggedIn ? 'logged-in' : 'logged-out'">
                        <h4>登录状态</h4>
                        <p>{{ isLoggedIn ? '✅ 已登录' : '❌ 未登录' }}</p>
                    </div>
                    <div class="status-card logged-in" v-if="currentUser">
                        <h4>当前用户</h4>
                        <p>{{ currentUser.displayName }}</p>
                        <small>{{ currentUser.username }} ({{ currentUser.role }})</small>
                    </div>
                    <div class="status-card logged-in" v-if="token">
                        <h4>Token</h4>
                        <p style="word-break: break-all; font-size: 12px;">{{ token.substring(0, 20) }}...</p>
                    </div>
                </div>
            </div>

            <!-- 登录测试 -->
            <div class="section">
                <h3 class="section-title">🔐 登录测试</h3>
                <div class="form-group">
                    <label>用户名:</label>
                    <input v-model="loginForm.username" placeholder="admin 或 testuser">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input v-model="loginForm.password" type="password" placeholder="admin123 或 user123">
                </div>
                <button class="btn btn-primary" @click="testLogin">登录</button>
                <div v-if="loginResult" class="result" :class="loginResult.success ? 'success' : 'error'">
                    {{ JSON.stringify(loginResult, null, 2) }}
                </div>
            </div>

            <!-- 注册测试 -->
            <div class="section">
                <h3 class="section-title">👤 注册测试</h3>
                <div class="form-group">
                    <label>用户名:</label>
                    <input v-model="registerForm.username" placeholder="新用户名">
                </div>
                <div class="form-group">
                    <label>真实姓名:</label>
                    <input v-model="registerForm.realName" placeholder="真实姓名">
                </div>
                <div class="form-group">
                    <label>邮箱:</label>
                    <input v-model="registerForm.email" placeholder="邮箱地址">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input v-model="registerForm.password" type="password" placeholder="密码">
                </div>
                <button class="btn btn-primary" @click="testRegister">注册</button>
                <div v-if="registerResult" class="result" :class="registerResult.success ? 'success' : 'error'">
                    {{ JSON.stringify(registerResult, null, 2) }}
                </div>
            </div>

            <!-- 功能测试 -->
            <div class="section">
                <h3 class="section-title">🧪 功能测试</h3>
                <button class="btn btn-secondary" @click="testCurrentUser">获取当前用户</button>
                <button class="btn btn-secondary" @click="testTokenInfo">获取Token信息</button>
                <button class="btn btn-secondary" @click="testRefreshUser">刷新用户信息</button>
                <button class="btn btn-danger" @click="testLogout">登出</button>
                <div v-if="testResult" class="result info">
                    {{ JSON.stringify(testResult, null, 2) }}
                </div>
            </div>

            <!-- 返回链接 -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="/" class="btn btn-secondary">🏠 返回首页</a>
                <a href="/login.html" class="btn btn-primary">🔐 登录页面</a>
            </div>
        </div>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script src="js/satoken-auth.js"></script>
    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载Sa-Token测试页面...');

                cdnLoader.updateProgress(30);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
            }
        }

        function initVueApp() {
            const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const isLoggedIn = ref(false);
                const currentUser = ref(null);
                const token = ref(null);
                
                const loginForm = ref({
                    username: 'admin',
                    password: 'admin123'
                });
                
                const registerForm = ref({
                    username: '',
                    realName: '',
                    email: '',
                    password: ''
                });
                
                const loginResult = ref(null);
                const registerResult = ref(null);
                const testResult = ref(null);

                // 测试登录
                const testLogin = async () => {
                    loginResult.value = null;
                    try {
                        const result = await saTokenAuthManager.login(
                            loginForm.value.username, 
                            loginForm.value.password
                        );
                        loginResult.value = result;
                        updateStatus();
                    } catch (error) {
                        loginResult.value = { success: false, message: error.message };
                    }
                };

                // 测试注册
                const testRegister = async () => {
                    registerResult.value = null;
                    try {
                        const result = await saTokenAuthManager.register(registerForm.value);
                        registerResult.value = result;
                    } catch (error) {
                        registerResult.value = { success: false, message: error.message };
                    }
                };

                // 测试获取当前用户
                const testCurrentUser = async () => {
                    try {
                        const response = await axios.get('/api/satoken/current-user');
                        testResult.value = response.data;
                    } catch (error) {
                        testResult.value = { error: error.message };
                    }
                };

                // 测试获取Token信息
                const testTokenInfo = async () => {
                    try {
                        const result = await saTokenAuthManager.getTokenInfo();
                        testResult.value = result;
                    } catch (error) {
                        testResult.value = { error: error.message };
                    }
                };

                // 测试刷新用户信息
                const testRefreshUser = async () => {
                    try {
                        const result = await saTokenAuthManager.refreshUserInfo();
                        testResult.value = { success: result, message: result ? '刷新成功' : '刷新失败' };
                        updateStatus();
                    } catch (error) {
                        testResult.value = { error: error.message };
                    }
                };

                // 测试登出
                const testLogout = async () => {
                    try {
                        await saTokenAuthManager.logout();
                        testResult.value = { success: true, message: '登出成功' };
                        updateStatus();
                    } catch (error) {
                        testResult.value = { error: error.message };
                    }
                };

                // 更新状态显示
                const updateStatus = () => {
                    isLoggedIn.value = saTokenAuthManager.isUserLoggedIn();
                    currentUser.value = saTokenAuthManager.getCurrentUser();
                    token.value = saTokenAuthManager.getToken();
                };

                // 页面加载时更新状态
                onMounted(() => {
                    // 监听认证状态变化
                    saTokenAuthManager.onLogin(() => updateStatus());
                    saTokenAuthManager.onLogout(() => updateStatus());
                    
                    // 初始状态更新
                    setTimeout(updateStatus, 500);
                });

                return {
                    isLoggedIn,
                    currentUser,
                    token,
                    loginForm,
                    registerForm,
                    loginResult,
                    registerResult,
                    testResult,
                    testLogin,
                    testRegister,
                    testCurrentUser,
                    testTokenInfo,
                    testRefreshUser,
                    testLogout
                };
            }
        }).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
