<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库配置 - DB Copy Data</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="shortcut icon" href="favicon.ico">

    <link rel="stylesheet" href="libs/element-plus.css">
    <style>
        body {
            margin: 0;
            padding: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
        }

        .config-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .config-header {
            text-align: center;
            margin-bottom: 15px;
        }

        .config-header h1 {
            color: #2c3e50;
            margin: 0 0 5px 0;
            font-size: 1.4em;
            font-weight: 300;
        }

        .config-header p {
            color: #7f8c8d;
            font-size: 0.85em;
            margin: 0;
        }

        .config-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .database-config-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .database-config-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .config-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 6px;
            border-bottom: 1px solid #e9ecef;
        }

        .config-card-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 0.95em;
            margin-left: 6px;
        }

        .database-icon {
            font-size: 1.0em;
        }

        .form-section {
            margin-bottom: 8px;
        }

        .form-section:last-child {
            margin-bottom: 0;
        }

        .connection-status {
            margin-top: 8px;
            padding: 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.8em;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-testing {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .database-list {
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: white;
        }

        .database-item {
            padding: 6px 8px;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            font-size: 0.8em;
        }

        .database-item:hover {
            background-color: #e3f2fd;
            transform: translateX(5px);
        }

        .database-item:hover:not(.selected)::after {
            content: '👆 点击选择';
            position: absolute;
            right: 15px;
            font-size: 12px;
            color: #666;
            animation: fadeIn 0.2s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .database-item:last-child {
            border-bottom: none;
        }

        .database-item.selected {
            background-color: #667eea;
            color: white;
            transform: translateX(5px);
            font-weight: bold;
        }

        .action-buttons {
            text-align: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e9ecef;
        }

        .btn-next {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-next:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-next:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .config-content {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .config-container {
                margin: 5px;
                padding: 8px;
            }
            
            /* 配置选择器响应式 */
            .config-selector {
                grid-template-columns: 1fr !important;
                gap: 8px !important;
            }
        }

        .config-selector {
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 12px;
        }

        .el-form-item {
            margin-bottom: 10px;
        }

        .el-input, .el-select {
            width: 100%;
        }

        .loading-text {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="config-container">
            <!-- 页面标题 -->
            <div class="config-header">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <div>
                        <h1 style="margin: 0;">🔧 数据库配置</h1>
                        <p style="margin: 5px 0 0 0;">请配置源数据库和目标数据库连接信息</p>
                    </div>
                    <div>
                        <button @click="navigateToConfigManagement"
                           style="background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%); 
                                  color: white; 
                                  padding: 6px 12px; 
                                  border-radius: 4px; 
                                  border: none;
                                  cursor: pointer;
                                  font-size: 12px;
                                  font-weight: 500;
                                  transition: all 0.3s ease;
                                  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.3);"
                           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(82, 196, 26, 0.4)';"
                           onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(82, 196, 26, 0.3)';">
                            🗄️ 配置管理
                        </button>
                    </div>
                </div>
            </div>

            <!-- 配置选择器 -->
            <div style="margin-bottom: 15px; background: white; border-radius: 8px; padding: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" v-loading="loading">
                <h3 style="margin-top: 0; margin-bottom: 10px; color: #2c3e50; font-size: 1.1em;">📋 选择数据库配置</h3>
                <div class="config-selector">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057; font-size: 0.9em;">源数据库配置：</label>
                        <el-select v-model="selectedSourceId" @change="loadSourceConfig" placeholder="请选择源数据库配置" style="width: 100%;">
                            <el-option v-for="config in sourceConfigs" :key="config.id" :value="config.id"
                                       :label="`${config.configName} (${config.databaseType})`">
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #495057; font-size: 0.9em;">目标数据库配置：</label>
                        <el-select v-model="selectedTargetId" @change="loadTargetConfig" placeholder="请选择目标数据库配置" style="width: 100%;">
                            <el-option v-for="config in targetConfigs" :key="config.id" :value="config.id"
                                       :label="`${config.configName} (${config.databaseType})`">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div style="margin-top: 15px; text-align: center;">
                    <el-button type="primary" @click="loadSelectedConfigs" 
                               :disabled="!selectedSourceId || !selectedTargetId"
                               :loading="loading"
                               icon="Refresh">
                        🔄 加载选择的配置
                    </el-button>
                    <div style="margin-top: 10px; font-size: 14px; color: #666;">
                        <span v-if="sourceConfigs.length > 0 && targetConfigs.length > 0">
                            共 {{ sourceConfigs.length }} 个源配置，{{ targetConfigs.length }} 个目标配置可选
                        </span>
                    </div>
                </div>
            </div>

            <!-- 配置内容 -->
            <div class="config-content">
                <!-- 源数据库配置 -->
                <div class="database-config-card">
                    <div class="config-card-header">
                        <span class="database-icon">{{ sourceConfig.databaseType === 'POSTGRESQL' ? '🐘' : '🐬' }}</span>
                        <h3>源数据库 ({{ sourceConfig.databaseType === 'POSTGRESQL' ? 'PostgreSQL' : 'MySQL' }})</h3>
                    </div>
                    
                    <el-form :model="sourceConfig" label-width="60px" size="small">
                        <el-form-item label="主机地址">
                            <el-input 
                                v-model="sourceConfig.host" 
                                placeholder="localhost"
                                @change="resetSourceStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="端口">
                            <el-input-number 
                                v-model="sourceConfig.port" 
                                :min="1" 
                                :max="65535"
                                style="width: 100%"
                                @change="resetSourceStatus">
                            </el-input-number>
                        </el-form-item>
                        
                        <el-form-item label="用户名">
                            <el-input 
                                v-model="sourceConfig.username"
                                @change="resetSourceStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="密码">
                            <el-input 
                                v-model="sourceConfig.password" 
                                type="password" 
                                show-password
                                @change="resetSourceStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button 
                                type="primary" 
                                @click="testSourceConnection"
                                :loading="sourceConnecting">
                                测试连接
                            </el-button>
                        </el-form-item>
                    </el-form>

                    <!-- 连接状态 -->
                    <div v-if="sourceStatus.message" 
                         :class="['connection-status', sourceStatus.type]">
                        {{ sourceStatus.message }}
                    </div>


                </div>

                <!-- 目标数据库配置 -->
                <div class="database-config-card">
                    <div class="config-card-header">
                        <span class="database-icon">{{ targetConfig.databaseType === 'MYSQL' ? '🐬' : '🐘' }}</span>
                        <h3>目标数据库 ({{ targetConfig.databaseType === 'MYSQL' ? 'MySQL' : 'PostgreSQL' }})</h3>
                    </div>
                    
                    <el-form :model="targetConfig" label-width="60px" size="small">
                        <el-form-item label="主机地址">
                            <el-input 
                                v-model="targetConfig.host" 
                                placeholder="localhost"
                                @change="resetTargetStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="端口">
                            <el-input-number 
                                v-model="targetConfig.port" 
                                :min="1" 
                                :max="65535"
                                style="width: 100%"
                                @change="resetTargetStatus">
                            </el-input-number>
                        </el-form-item>
                        
                        <el-form-item label="用户名">
                            <el-input 
                                v-model="targetConfig.username"
                                @change="resetTargetStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item label="密码">
                            <el-input 
                                v-model="targetConfig.password" 
                                type="password" 
                                show-password
                                @change="resetTargetStatus">
                            </el-input>
                        </el-form-item>
                        
                        <el-form-item>
                            <el-button 
                                type="primary" 
                                @click="testTargetConnection"
                                :loading="targetConnecting">
                                测试连接
                            </el-button>
                        </el-form-item>
                    </el-form>

                    <!-- 连接状态 -->
                    <div v-if="targetStatus.message" 
                         :class="['connection-status', targetStatus.type]">
                        {{ targetStatus.message }}
                    </div>


                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <!-- 进度提示 -->
                <div v-if="!canProceed" style="text-align: center; margin-bottom: 15px; padding: 10px; background: #fff3cd; border-radius: 8px; color: #856404; border: 1px solid #ffeaa7;">
                    <div style="font-weight: bold; margin-bottom: 5px;">⚠️ 请完成以下配置步骤：</div>
                    <div style="font-size: 14px;">
                        <div>1. 测试源数据库连接 {{ sourceStatus.type === 'status-success' ? '✅' : '❌' }}</div>
                        <div>2. 测试目标数据库连接 {{ targetStatus.type === 'status-success' ? '✅' : '❌' }}</div>
                    </div>
                </div>
                
                <!-- 连接成功提示 -->
                <div v-if="canProceed" style="text-align: center; margin-bottom: 15px; padding: 15px; background: #d4edda; border-radius: 8px; color: #155724; border: 1px solid #c3e6cb;">
                    <div style="font-weight: bold; margin-bottom: 5px;">🎉 数据库连接配置完成！</div>
                    <div style="font-size: 14px;">
                        <div>✅ PostgreSQL源数据库连接成功</div>
                        <div>✅ MySQL目标数据库连接成功</div>
                        <div style="margin-top: 10px; font-style: italic;">现在可以开始数据迁移了</div>
                    </div>
                </div>
                
                <button 
                    class="btn-next"
                    :disabled="!canProceed"
                    @click="proceedToMain">
                    {{ canProceed ? '进入数据迁移系统 →' : '请完成上述配置步骤' }}
                </button>
            </div>
        </div>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载数据库配置页面...');

                cdnLoader.updateProgress(20);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(60);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(80);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
            }
        }

        function initVueApp() {
            const { createApp } = Vue;
            const { ElMessage, ElMessageBox } = ElementPlus;

            createApp({
            data() {
                return {
                    // 配置选择
                    sourceConfigs: [],
                    targetConfigs: [],
                    selectedSourceId: '',
                    selectedTargetId: '',
                    
                    // 源数据库配置
                    sourceConfig: {
                        databaseType: 'POSTGRESQL',
                        host: 'localhost',
                        port: 5432,
                        username: 'postgres',
                        password: '',
                        description: 'PostgreSQL源数据库'
                    },
                    
                    // 目标数据库配置
                    targetConfig: {
                        databaseType: 'MYSQL',
                        host: 'localhost',
                        port: 3306,
                        username: 'root',
                        password: '',
                        description: 'MySQL目标数据库'
                    },

                    // 连接状态
                    sourceConnecting: false,
                    targetConnecting: false,
                    sourceStatus: { message: '', type: '' },
                    targetStatus: { message: '', type: '' },

                    // 数据库列表
                    sourceDatabases: [],
                    targetDatabases: [],

                    // 加载状态
                    loading: false
                }
            },

            computed: {
                canProceed() {
                    return this.sourceStatus.type === 'status-success' && 
                           this.targetStatus.type === 'status-success';
                }
            },

            mounted() {
                this.loadAllConfigs();
            },

            methods: {
                // 获取用于连接测试的配置（将localhost转换为127.0.0.1）
                getTestConfig(config) {
                    return {
                        ...config,
                        host: config.host === 'localhost' ? '127.0.0.1' : config.host
                    };
                },

                // 加载所有配置
                async loadAllConfigs() {
                    try {
                        this.loading = true;
                        
                        // 获取所有配置
                        const allConfigResponse = await axios.get('/api/database-config/list');
                        
                        if (allConfigResponse.data.success) {
                            const allConfigs = allConfigResponse.data.data;
                            
                            // 按类型分类配置
                            this.sourceConfigs = allConfigs.filter(config => 
                                config.configType === 'SOURCE' && config.isActive
                            );
                            this.targetConfigs = allConfigs.filter(config => 
                                config.configType === 'TARGET' && config.isActive
                            );
                            
                            if (this.sourceConfigs.length === 0 || this.targetConfigs.length === 0) {
                                ElMessage.warning('未找到可用的数据库配置，请先在配置管理中添加配置');
                                return;
                            }

                            // 首先尝试从localStorage恢复用户之前的选择
                            const savedConfig = this.loadSavedConfig();
                            let foundSavedSource = false;
                            let foundSavedTarget = false;

                            if (savedConfig) {
                                console.log('发现保存的配置，尝试恢复用户选择...');
                                
                                // 尝试根据配置内容匹配对应的ID
                                const matchingSource = this.sourceConfigs.find(config =>
                                    config.databaseType === savedConfig.sourceConfig.databaseType &&
                                    config.host === (savedConfig.sourceConfig.host === 'localhost' ? '127.0.0.1' : savedConfig.sourceConfig.host) &&
                                    config.port === savedConfig.sourceConfig.port &&
                                    config.username === savedConfig.sourceConfig.username
                                );

                                const matchingTarget = this.targetConfigs.find(config =>
                                    config.databaseType === savedConfig.targetConfig.databaseType &&
                                    config.host === (savedConfig.targetConfig.host === 'localhost' ? '127.0.0.1' : savedConfig.targetConfig.host) &&
                                    config.port === savedConfig.targetConfig.port &&
                                    config.username === savedConfig.targetConfig.username
                                );

                                if (matchingSource && matchingTarget) {
                                    this.selectedSourceId = matchingSource.id;
                                    this.selectedTargetId = matchingTarget.id;
                                    foundSavedSource = true;
                                    foundSavedTarget = true;
                                    await this.loadSelectedConfigs();
                                    ElMessage.success('恢复了您上次的配置选择');
                                } else {
                                    console.log('无法匹配保存的配置，使用默认选择');
                                }
                            }

                            // 如果没有找到保存的配置，使用默认逻辑
                            if (!foundSavedSource || !foundSavedTarget) {
                                if (this.sourceConfigs.length === 1 && this.targetConfigs.length === 1) {
                                    this.selectedSourceId = this.sourceConfigs[0].id;
                                    this.selectedTargetId = this.targetConfigs[0].id;
                                    await this.loadSelectedConfigs();
                                } else {
                                    ElMessage.info(`找到 ${this.sourceConfigs.length} 个源数据库配置和 ${this.targetConfigs.length} 个目标数据库配置，请从下拉列表中选择`);
                                }
                            }
                        } else {
                            ElMessage.error('获取配置列表失败');
                        }
                    } catch (error) {
                        console.error('加载配置失败:', error);
                        ElMessage.error('加载配置失败，请刷新页面重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 加载选择的配置
                async loadSelectedConfigs() {
                    if (!this.selectedSourceId || !this.selectedTargetId) {
                        ElMessage.warning('请选择源数据库和目标数据库配置');
                        return;
                    }

                    try {
                        // 找到选择的配置
                        const sourceConfig = this.sourceConfigs.find(c => c.id === this.selectedSourceId);
                        const targetConfig = this.targetConfigs.find(c => c.id === this.selectedTargetId);

                        if (sourceConfig && targetConfig) {
                            // 更新源数据库配置
                            this.sourceConfig = {
                                databaseType: sourceConfig.databaseType,
                                host: sourceConfig.host === '127.0.0.1' ? 'localhost' : sourceConfig.host,
                                port: sourceConfig.port,
                                username: sourceConfig.username,
                                password: sourceConfig.password,
                                description: sourceConfig.description || sourceConfig.configName || '源数据库'
                            };

                            // 更新目标数据库配置
                            this.targetConfig = {
                                databaseType: targetConfig.databaseType,
                                host: targetConfig.host === '127.0.0.1' ? 'localhost' : targetConfig.host,
                                port: targetConfig.port,
                                username: targetConfig.username,
                                password: targetConfig.password,
                                description: targetConfig.description || targetConfig.configName || '目标数据库'
                            };

                            // 重置连接状态
                            this.resetSourceStatus();
                            this.resetTargetStatus();

                            ElMessage.success('配置加载成功！请测试连接');

                            // 自动测试连接
                            await this.testSourceConnection();
                            await this.testTargetConnection();
                            
                            // 保存当前配置
                            this.saveCurrentConfig();
                        }
                    } catch (error) {
                        console.error('加载配置失败:', error);
                        ElMessage.error('加载配置失败: ' + error.message);
                    }
                },

                // 从localStorage加载保存的配置
                loadSavedConfig() {
                    try {
                        const configStr = localStorage.getItem('databaseConfig');
                        if (configStr) {
                            const config = JSON.parse(configStr);
                            console.log('找到保存的配置:', config);
                            return config;
                        }
                    } catch (error) {
                        console.error('解析保存的配置失败:', error);
                        localStorage.removeItem('databaseConfig'); // 清除损坏的配置
                    }
                    return null;
                },

                // 保存当前配置到localStorage（实时保存）
                saveCurrentConfig() {
                    if (this.sourceConfig && this.targetConfig) {
                        const config = {
                            sourceConfig: { ...this.sourceConfig },
                            targetConfig: { ...this.targetConfig },
                            selectedSourceId: this.selectedSourceId,
                            selectedTargetId: this.selectedTargetId,
                            configuredAt: new Date().toISOString()
                        };
                        
                        localStorage.setItem('databaseConfig', JSON.stringify(config));
                        console.log('配置已保存到localStorage');
                        
                        // 触发storage事件，通知其他页面配置已更新
                        window.dispatchEvent(new StorageEvent('storage', {
                            key: 'databaseConfig',
                            newValue: JSON.stringify(config)
                        }));
                    }
                },

                // 当选择源配置改变时
                async loadSourceConfig() {
                    if (this.selectedSourceId && this.selectedTargetId) {
                        await this.loadSelectedConfigs();
                        // 实时保存配置
                        this.saveCurrentConfig();
                    }
                },

                // 当选择目标配置改变时
                async loadTargetConfig() {
                    if (this.selectedSourceId && this.selectedTargetId) {
                        await this.loadSelectedConfigs();
                        // 实时保存配置
                        this.saveCurrentConfig();
                    }
                },
                // 加载默认配置
                async loadDefaultConfigs() {
                    try {
                        // 首先尝试从数据库配置管理获取已保存的配置
                        const savedResponse = await axios.get('/api/database-config/current/source');
                        const savedTargetResponse = await axios.get('/api/database-config/current/target');
                        
                        if (savedResponse.data.success && savedTargetResponse.data.success) {
                            // 使用已保存的配置
                            const sourceData = savedResponse.data.data;
                            const targetData = savedTargetResponse.data.data;
                            
                            this.sourceConfig = {
                                databaseType: sourceData.databaseType,
                                host: sourceData.host === '127.0.0.1' ? 'localhost' : sourceData.host,
                                port: sourceData.port,
                                username: sourceData.username,
                                password: sourceData.password,
                                description: sourceData.description || sourceData.configName || 'PostgreSQL源数据库'
                            };
                            
                            this.targetConfig = {
                                databaseType: targetData.databaseType,
                                host: targetData.host === '127.0.0.1' ? 'localhost' : targetData.host,
                                port: targetData.port,
                                username: targetData.username,
                                password: targetData.password,
                                description: targetData.description || targetData.configName || 'MySQL目标数据库'
                            };
                            
                            ElMessage.success('已保存的配置加载成功');
                            
                            // 自动测试连接
                            await this.testSourceConnection();
                            await this.testTargetConnection();
                            
                            return;
                        }
                    } catch (error) {
                        console.log('未找到已保存的配置，尝试加载默认配置');
                    }
                    
                    try {
                        // 如果没有保存的配置，则加载默认配置
                        const response = await axios.get('/api/db-config/defaults');
                        if (response.data.success) {
                            const configs = response.data.data;
                            
                            if (configs.postgresql) {
                                Object.assign(this.sourceConfig, configs.postgresql);
                            }
                            
                            if (configs.mysql) {
                                Object.assign(this.targetConfig, configs.mysql);
                            }
                            
                            ElMessage.success('默认配置加载成功');
                        }
                    } catch (error) {
                        console.error('加载默认配置失败:', error);
                        ElMessage.warning('无法加载配置，请手动填写');
                    }
                },

                // 测试源数据库连接
                async testSourceConnection() {
                    this.sourceConnecting = true;
                    this.sourceStatus = { message: '正在测试连接...', type: 'status-testing' };

                    try {
                        const response = await axios.post('/api/db-config/validate', this.getTestConfig(this.sourceConfig));
                        
                        if (response.data.success) {
                            this.sourceStatus = { 
                                message: `连接成功！发现 ${response.data.databaseCount} 个数据库`, 
                                type: 'status-success' 
                            };
                            this.sourceDatabases = response.data.databases;
                        } else {
                            this.sourceStatus = { 
                                message: response.data.message, 
                                type: 'status-error' 
                            };
                            this.sourceDatabases = [];
                        }
                    } catch (error) {
                        this.sourceStatus = { 
                            message: '连接失败: ' + (error.response?.data?.message || error.message), 
                            type: 'status-error' 
                        };
                        this.sourceDatabases = [];
                    } finally {
                        this.sourceConnecting = false;
                    }
                },

                // 测试目标数据库连接
                async testTargetConnection() {
                    this.targetConnecting = true;
                    this.targetStatus = { message: '正在测试连接...', type: 'status-testing' };

                    try {
                        const response = await axios.post('/api/db-config/validate', this.getTestConfig(this.targetConfig));
                        
                        if (response.data.success) {
                            this.targetStatus = { 
                                message: `连接成功！发现 ${response.data.databaseCount} 个MySQL数据库`, 
                                type: 'status-success' 
                            };
                            this.targetDatabases = response.data.databases;
                        } else {
                            this.targetStatus = { 
                                message: response.data.message, 
                                type: 'status-error' 
                            };
                            this.targetDatabases = [];
                        }
                    } catch (error) {
                        this.targetStatus = { 
                            message: '连接失败: ' + (error.response?.data?.message || error.message), 
                            type: 'status-error' 
                        };
                        this.targetDatabases = [];
                    } finally {
                        this.targetConnecting = false;
                    }
                },



                // 重置源数据库状态
                resetSourceStatus() {
                    this.sourceStatus = { message: '', type: '' };
                    this.sourceDatabases = [];
                },

                // 重置目标数据库状态
                resetTargetStatus() {
                    this.targetStatus = { message: '', type: '' };
                    this.targetDatabases = [];
                },

                // 进入主页面
                proceedToMain() {
                    // 保存配置到localStorage（使用统一的保存方法）
                    this.saveCurrentConfig();
                    
                    ElMessage.success('配置已保存，即将跳转到数据迁移页面');
                    
                    // 延迟跳转，让用户看到保存成功的消息
                    setTimeout(() => {
                        // 检查是否在iframe中
                        if (window.parent !== window) {
                            // 在iframe中，通知父窗口切换到迁移页面
                            window.parent.postMessage({
                                type: 'navigateToMigration'
                            }, '*');
                        } else {
                            // 不在iframe中，直接跳转
                            window.location.href = '/migration.html';
                        }
                    }, 1000);
                },

                // 导航到配置管理页面
                navigateToConfigManagement() {
                    console.log('导航到配置管理页面');
                    
                    // 检查是否在iframe中
                    if (window.parent !== window) {
                        // 在iframe中，通知父窗口切换到配置管理页面
                        window.parent.postMessage({
                            type: 'navigate',
                            menu: 'config-management'
                        }, '*');
                    } else {
                        // 不在iframe中，直接跳转
                        window.location.href = '/database-config-management.html';
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>