<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>迁移记录管理</title>
    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        .page-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e4e7ed;
        }
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin: 0;
        }
        .page-subtitle {
            font-size: 14px;
            color: #909399;
            margin: 8px 0 0 0;
        }
        .detail-card {
            margin-top: 16px;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            overflow: hidden;
        }
        .card-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #ebeef5;
            font-weight: 600;
            color: #303133;
        }
        .card-content {
            padding: 16px;
        }
        .field-row {
            display: flex;
            margin-bottom: 12px;
        }
        .field-label {
            width: 120px;
            color: #606266;
            font-weight: 500;
        }
        .field-value {
            flex: 1;
            color: #303133;
        }
        .ddl-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
        }
        .fields-table {
            margin-top: 16px;
        }
        .status-success { color: #67c23a; }
        .status-failed { color: #f56c6c; }
        .status-partial { color: #e6a23c; }
        .status-in-progress { color: #409eff; }
        .mapping-rule-tag {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
            color: white;
        }
        .rule-direct { background-color: #67c23a; }
        .rule-type-convert { background-color: #e6a23c; }
        .rule-name-convert { background-color: #409eff; }
        .rule-custom { background-color: #909399; }

        /* 字段映射表格样式 */
        .system-field-row {
            background-color: #f0f9ff !important;
        }

        .primary-field-row {
            background-color: #fef3c7 !important;
        }

        .system-field-row:hover {
            background-color: #e0f2fe !important;
        }

        .primary-field-row:hover {
            background-color: #fde68a !important;
        }

        /* 字段映射详情卡片 */
        .detail-card .card-header {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .detail-card .card-content {
            overflow-x: auto;
        }

        /* 字段映射表格头部样式 */
        .field-mapping-header th {
            background-color: #f8f9fa !important;
            font-weight: 600 !important;
            color: #495057 !important;
        }

        /* 确保表格容器有足够空间 */
        .detail-card {
            margin-bottom: 20px;
        }

        /* 转换说明列样式优化 */
        .el-table .cell {
            line-height: 1.4;
            word-break: break-word;
            white-space: normal;
        }

        /* 字段映射表格滚动条优化 */
        .detail-card .card-content::-webkit-scrollbar {
            height: 8px;
        }

        .detail-card .card-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .detail-card .card-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .detail-card .card-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="main-container">
            <div class="page-header">
                <h1 class="page-title">迁移记录管理</h1>
                <p class="page-subtitle">查看数据库迁移记录和目标表详细信息</p>
            </div>

            <!-- 迁移记录列表 -->
            <el-card v-if="currentView === 'list'">
                <template #header>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>迁移记录列表</span>
                        <div>
                            <el-button
                                @click="batchDeleteMigrationRecords"
                                :icon="ElIconDelete"
                                type="danger"
                                size="small"
                                :disabled="selectedRecords.length === 0"
                                style="margin-right: 8px;"
                            >
                                批量删除 ({{ selectedRecords.length }})
                            </el-button>
                            <el-button @click="loadMigrationRecords" :icon="ElIconRefresh" type="primary" size="small">刷新</el-button>
                        </div>
                    </div>
                </template>

                <el-table
                    :data="migrationRecords"
                    v-loading="loading"
                    style="width: 100%"
                    :key="tableKey"
                    stripe
                    border
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="migrationBatchId" label="批次ID" width="280" show-overflow-tooltip>
                        <template #default="scope">
                            <el-link type="primary" @click="viewDetail(scope.row.migrationBatchId)">
                                {{ scope.row.migrationBatchId }}
                            </el-link>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="源数据库" width="150">
                        <template #default="scope">
                            <span>{{ scope.row.sourceDatabaseName || '-' }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="目标数据库" width="150">
                        <template #default="scope">
                            <span>{{ scope.row.targetDatabaseName || '-' }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="源表名" width="150">
                        <template #default="scope">
                            <span>{{ scope.row.sourceTableName || '-' }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="目标表名" width="150">
                        <template #default="scope">
                            <span>{{ scope.row.targetTableName || '-' }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <span :class="getStatusClass(scope.row.migrationStatus)">
                                {{ getStatusText(scope.row.migrationStatus) }}
                            </span>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作人" width="120">
                        <template #default="scope">
                            <div v-if="scope.row.operatorName" style="display: flex; align-items: center;">
                                <div style="width: 24px; height: 24px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: 600; margin-right: 8px;">
                                    {{ scope.row.operatorName.charAt(0).toUpperCase() }}
                                </div>
                                <span style="font-size: 13px;">{{ scope.row.operatorName }}</span>
                            </div>
                            <span v-else style="color: #999;">-</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="表数量" width="80">
                        <template #default="scope">
                            <span>{{ scope.row.totalTables || 0 }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="总记录数" width="120">
                        <template #default="scope">
                            <span>{{ scope.row.totalRecords || 0 }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="迁移时间" width="180">
                        <template #default="scope">
                            <span>{{ formatDateTime(scope.row.migrationStartTime) }}</span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column label="操作" width="180" fixed="right">
                        <template #default="scope">
                            <el-button size="small" @click="viewDetail(scope.row.migrationBatchId)">
                                查看详情
                            </el-button>
                            <el-button
                                size="small"
                                type="danger"
                                @click="deleteMigrationRecord(scope.row)"
                                style="margin-left: 8px;"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    style="margin-top: 20px; text-align: center;"
                />
            </el-card>

            <!-- 迁移记录详情 -->
            <div v-if="currentView === 'detail'" class="detail-view">
                <el-card>
                    <template #header>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>迁移记录详情</span>
                            <el-button @click="backToList" :icon="ElIconArrowLeft" size="small">返回列表</el-button>
                        </div>
                    </template>

                    <div v-loading="detailLoading">
                        <div v-if="migrationDetail">
                            <!-- 基本信息 -->
                            <div class="detail-card">
                                <div class="card-header">基本信息</div>
                                <div class="card-content">
                                    <div class="field-row">
                                        <span class="field-label">批次ID:</span>
                                        <span class="field-value">{{ migrationDetail.migrationBatchId }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">源数据库:</span>
                                        <span class="field-value">{{ migrationDetail.sourceDatabaseName }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">目标数据库:</span>
                                        <span class="field-value">{{ migrationDetail.targetDatabaseName }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">源表名:</span>
                                        <span class="field-value">{{ migrationDetail.sourceTableName || '批量迁移' }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">目标表名:</span>
                                        <span class="field-value">{{ migrationDetail.targetTableName || '批量迁移' }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">迁移状态:</span>
                                        <span class="field-value" :class="getStatusClass(migrationDetail.migrationStatus)">
                                            {{ getStatusText(migrationDetail.migrationStatus) }}
                                        </span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">开始时间:</span>
                                        <span class="field-value">{{ formatDateTime(migrationDetail.migrationStartTime) }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">结束时间:</span>
                                        <span class="field-value">{{ formatDateTime(migrationDetail.migrationEndTime) }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 目标表信息 -->
                            <div v-if="migrationDetail.targetTable" class="detail-card">
                                <div class="card-header">目标表信息 (MySQL)</div>
                                <div class="card-content">
                                    <div class="field-row">
                                        <span class="field-label">表名:</span>
                                        <span class="field-value">{{ migrationDetail.targetTable.tableName }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">表注释:</span>
                                        <span class="field-value">{{ migrationDetail.targetTable.tableComment || '无' }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">记录数:</span>
                                        <span class="field-value">{{ migrationDetail.targetTable.recordCount }}</span>
                                    </div>
                                    <div class="field-row">
                                        <span class="field-label">字段数:</span>
                                        <span class="field-value">{{ migrationDetail.targetTable.fieldCount }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- DDL语句 -->
                            <div v-if="migrationDetail.targetTable && migrationDetail.targetTable.ddlStatement" class="detail-card">
                                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>目标表DDL语句</span>
                                    <el-button 
                                        type="primary" 
                                        size="small" 
                                        :icon="ElIconCopyDocument"
                                        @click="copyDdlToClipboard"
                                        plain>
                                        复制DDL
                                    </el-button>
                                </div>
                                <div class="card-content">
                                    <div class="ddl-container">{{ migrationDetail.targetTable.ddlStatement }}</div>
                                </div>
                            </div>

                            <!-- 字段映射详情 -->
                            <div v-if="migrationDetail.targetTable && migrationDetail.targetTable.fields" class="detail-card">
                                <div class="card-header">
                                    <span>字段映射详情</span>
                                    <span style="font-size: 14px; color: #666; margin-left: 10px;">
                                        (共 {{ migrationDetail.targetTable.fields.length }} 个字段)
                                    </span>
                                </div>
                                <div class="card-content">
                                    <el-table 
                                        :data="migrationDetail.targetTable.fields" 
                                        style="width: 100%; min-width: 1200px;"
                                        stripe
                                        border
                                        size="small"
                                        :row-class-name="getFieldRowClass"
                                        :default-sort="{prop: 'position', order: 'ascending'}"
                                        header-row-class-name="field-mapping-header">
                                        <el-table-column prop="position" label="位置" width="60" align="center"/>
                                        <el-table-column label="源字段" width="220">
                                            <template #default="scope">
                                                <div>
                                                    <div style="font-weight: 600; color: #2c3e50;">
                                                        <span v-if="scope.row.sourceFieldName && scope.row.sourceFieldName !== '__SYSTEM_GENERATED__'">
                                                            {{ scope.row.sourceFieldName }}
                                                        </span>
                                                        <span v-else style="color: #999; font-style: italic;">
                                                            —
                                                        </span>
                                                        <el-tag v-if="scope.row.sourceFieldName === '__SYSTEM_GENERATED__'" 
                                                               type="info" size="small" style="margin-left: 5px;">
                                                            系统生成
                                                        </el-tag>
                                                    </div>
                                                    <div style="font-size: 12px; color: #909399;">
                                                        <span v-if="scope.row.sourceFieldType && scope.row.sourceFieldType !== '__SYSTEM_GENERATED__'">
                                                            {{ scope.row.sourceFieldType }}
                                                        </span>
                                                        <span v-else style="color: #999;">—</span>
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="目标字段" width="220">
                                            <template #default="scope">
                                                <div>
                                                    <div style="font-weight: 600; color: #2c3e50;">
                                                        {{ scope.row.fieldName }}
                                                        <el-tag v-if="scope.row.isPrimary" 
                                                               type="warning" size="small" style="margin-left: 5px;">
                                                            主键
                                                        </el-tag>
                                                        <el-tag v-if="scope.row.isUnique" 
                                                               type="success" size="small" style="margin-left: 5px;">
                                                            唯一
                                                        </el-tag>
                                                    </div>
                                                    <div style="font-size: 12px; color: #909399;">
                                                        {{ scope.row.fieldType }}
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="映射规则" width="120" align="center">
                                            <template #default="scope">
                                                <el-tag :type="getMappingRuleType(scope.row.mappingRule)" size="small">
                                                    {{ getMappingRuleText(scope.row.mappingRule) }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="字段注释" min-width="200" show-overflow-tooltip>
                                            <template #default="scope">
                                                <span>{{ scope.row.fieldComment || scope.row.sourceFieldComment || '-' }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="约束" width="120">
                                            <template #default="scope">
                                                <div style="font-size: 12px;">
                                                    <div v-if="scope.row.nullable === false" style="color: #e74c3c; margin-bottom: 2px;">
                                                        <el-tag type="danger" size="small">NOT NULL</el-tag>
                                                    </div>
                                                    <div v-if="scope.row.defaultValue" style="color: #27ae60; font-size: 11px;">
                                                        默认: {{ scope.row.defaultValue }}
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="转换说明" min-width="250" show-overflow-tooltip>
                                            <template #default="scope">
                                                <span style="font-size: 12px; color: #666; line-height: 1.4;">
                                                    {{ scope.row.conversionNotes || '-' }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>
        </div>
    </div>

    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载迁移历史页面...');

                cdnLoader.updateProgress(20);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(50);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('elementPlusIconsJs');

                cdnLoader.updateProgress(90);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    // 检查关键依赖是否已加载
                    if (typeof Vue === 'undefined') {
                        console.error('❌ Vue 未加载');
                        return;
                    }
                    if (typeof ElementPlus === 'undefined') {
                        console.error('❌ ElementPlus 未加载');
                        return;
                    }
                    if (typeof ElementPlusIconsVue === 'undefined') {
                        console.error('❌ ElementPlusIconsVue 未加载');
                        return;
                    }

                    console.log('✅ 所有依赖已加载，初始化Vue应用');
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
            }
        }

        function initVueApp() {
            const { createApp, ref, reactive, onMounted } = Vue;
            const { ElMessage, ElMessageBox } = ElementPlus;

            createApp({
            setup() {
                // 响应式数据
                const currentView = ref('list');
                const loading = ref(false);
                const detailLoading = ref(false);
                const migrationRecords = ref([]);
                const migrationDetail = ref(null);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const total = ref(0);
                const tableKey = ref(0); // 用于强制重新渲染表格
                const selectedRecords = ref([]); // 选中的记录

                // Element Plus 图标 - 安全访问，避免未定义错误
                const ElIconRefresh = ElementPlusIconsVue?.Refresh || null;
                const ElIconArrowLeft = ElementPlusIconsVue?.ArrowLeft || null;
                const ElIconCopyDocument = ElementPlusIconsVue?.CopyDocument || null;
                const ElIconDelete = ElementPlusIconsVue?.Delete || null;

                // 加载迁移记录列表
                const loadMigrationRecords = async () => {
                    loading.value = true;
                    try {
                        const response = await axios.get('/api/migration-history/list', {
                            params: {
                                pageNum: currentPage.value,
                                pageSize: pageSize.value
                            }
                        });
                        
                        if (response.data.success) {
                            migrationRecords.value = response.data.data;
                            total.value = response.data.total;
                            
                            // 强制重新渲染表格
                            tableKey.value++;
                            
                            // 超详细调试信息
                            console.log('✅ 迁移记录加载成功！');
                            console.log('📊 API响应数据:', response.data);
                            console.log('🎯 记录总数:', response.data.total);
                            console.log('📋 记录数组长度:', response.data.data.length);
                            console.log('🔧 表格渲染Key:', tableKey.value);
                            
                            if (response.data.data && response.data.data.length > 0) {
                                response.data.data.forEach((record, index) => {
                                    console.log(`📝 记录${index + 1}详细字段:`, {
                                        id: record.id,
                                        migrationBatchId: record.migrationBatchId,
                                        sourceDatabaseName: record.sourceDatabaseName,
                                        targetDatabaseName: record.targetDatabaseName,
                                        migrationStatus: record.migrationStatus,
                                        totalTables: record.totalTables,
                                        totalRecords: record.totalRecords,
                                        migrationStartTime: record.migrationStartTime
                                    });
                                });
                                
                                console.log('🎲 Vue响应式数据同步后:', migrationRecords.value);
                                
                                // 验证Vue响应式数据
                                setTimeout(() => {
                                    console.log('⏰ 延迟验证Vue数据状态:', {
                                        length: migrationRecords.value.length,
                                        firstRecord: migrationRecords.value[0],
                                        tableKey: tableKey.value
                                    });
                                }, 100);
                            }
                        } else {
                            console.error('❌ API响应失败:', response.data);
                            ElMessage.error(response.data.message || '加载失败');
                        }
                    } catch (error) {
                        console.error('加载迁移记录失败:', error);
                        ElMessage.error('加载迁移记录失败');
                    } finally {
                        loading.value = false;
                    }
                };

                // 查看详情
                const viewDetail = async (migrationBatchId) => {
                    currentView.value = 'detail';
                    detailLoading.value = true;
                    try {
                        console.log('🔍 正在加载迁移详情，批次ID:', migrationBatchId);
                        const response = await axios.get(`/api/migration-history/target-table-detail/${migrationBatchId}`);
                        
                        console.log('📊 API响应完整数据:', response.data);
                        
                        if (response.data.success) {
                            migrationDetail.value = response.data.data;
                            console.log('✅ 迁移详情加载成功:', migrationDetail.value);
                            
                            // 检查目标表信息
                            if (migrationDetail.value.targetTable) {
                                console.log('📋 目标表信息:', migrationDetail.value.targetTable);
                                console.log('🔧 字段信息:', migrationDetail.value.targetTable.fields);
                                console.log('📏 字段数量:', migrationDetail.value.targetTable.fields?.length);
                            } else {
                                console.warn('⚠️ 未找到目标表信息');
                            }
                        } else {
                            console.error('❌ API响应失败:', response.data);
                            ElMessage.error(response.data.message || '加载详情失败');
                        }
                    } catch (error) {
                        console.error('💥 加载详情失败:', error);
                        ElMessage.error('加载详情失败');
                    } finally {
                        detailLoading.value = false;
                    }
                };

                // 返回列表
                const backToList = () => {
                    currentView.value = 'list';
                    migrationDetail.value = null;
                };

                // 分页处理
                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    loadMigrationRecords();
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                    loadMigrationRecords();
                };

                // 工具函数
                const formatDateTime = (dateTime) => {
                    if (!dateTime) return '-';
                    return new Date(dateTime).toLocaleString('zh-CN');
                };

                const getStatusClass = (status) => {
                    const statusMap = {
                        'SUCCESS': 'status-success',
                        'FAILED': 'status-failed',
                        'PARTIAL': 'status-partial',
                        'IN_PROGRESS': 'status-in-progress'
                    };
                    return statusMap[status] || '';
                };

                const getStatusText = (status) => {
                    const statusMap = {
                        'SUCCESS': '成功',
                        'FAILED': '失败',
                        'PARTIAL': '部分成功',
                        'IN_PROGRESS': '进行中'
                    };
                    return statusMap[status] || status;
                };

                const getMappingRuleText = (rule) => {
                    const ruleMap = {
                        'DIRECT': '直接映射',
                        'TYPE_CONVERT': '类型转换',
                        'NAME_CONVERT': '名称转换',
                        'CUSTOM': '自定义'
                    };
                    return ruleMap[rule] || rule;
                };

                const getMappingRuleType = (rule) => {
                    const typeMap = {
                        'DIRECT': 'success',
                        'TYPE_CONVERT': 'warning',
                        'NAME_CONVERT': 'info',
                        'CUSTOM': 'primary'
                    };
                    return typeMap[rule] || 'default';
                };

                const getFieldRowClass = ({ row, rowIndex }) => {
                    // 为系统生成字段添加特殊样式
                    if (row.sourceFieldName === '__SYSTEM_GENERATED__') {
                        return 'system-field-row';
                    }
                    // 为主键字段添加特殊样式
                    if (row.isPrimary === true) {
                        return 'primary-field-row';
                    }
                    return '';
                };

                // 复制DDL语句到剪贴板
                const copyDdlToClipboard = async () => {
                    if (!migrationDetail.value?.targetTable?.ddlStatement) {
                        ElMessage.warning('没有可复制的DDL语句');
                        return;
                    }

                    try {
                        // 使用现代的 Clipboard API
                        if (navigator.clipboard && window.isSecureContext) {
                            await navigator.clipboard.writeText(migrationDetail.value.targetTable.ddlStatement);
                            ElMessage.success('DDL语句已复制到剪贴板');
                        } else {
                            // 降级方案：使用传统的 document.execCommand
                            const textArea = document.createElement('textarea');
                            textArea.value = migrationDetail.value.targetTable.ddlStatement;
                            textArea.style.position = 'fixed';
                            textArea.style.left = '-999999px';
                            textArea.style.top = '-999999px';
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            
                            const successful = document.execCommand('copy');
                            document.body.removeChild(textArea);
                            
                            if (successful) {
                                ElMessage.success('DDL语句已复制到剪贴板');
                            } else {
                                throw new Error('复制失败');
                            }
                        }
                        
                        console.log('✅ DDL语句复制成功');
                    } catch (error) {
                        console.error('❌ 复制DDL语句失败:', error);
                        ElMessage.error('复制失败，请手动选择文本复制');
                    }
                };

                // 处理表格选择变化
                const handleSelectionChange = (selection) => {
                    selectedRecords.value = selection;
                };

                // 删除单个迁移记录
                const deleteMigrationRecord = async (record) => {
                    try {
                        await ElMessageBox.confirm(
                            `确定要删除这条迁移记录吗？\n\n源表: ${record.sourceTableName}\n目标表: ${record.targetTableName}\n\n⚠️ 此操作将同时删除所有相关的表映射记录和字段映射记录，且无法恢复！`,
                            '确认删除',
                            {
                                confirmButtonText: '确定删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                dangerouslyUseHTMLString: true
                            }
                        );

                        loading.value = true;
                        const response = await axios.delete(`/api/migration-history/records/${record.id}`);

                        if (response.data.success) {
                            ElMessage.success(response.data.message);
                            await loadMigrationRecords(); // 重新加载列表
                        } else {
                            ElMessage.error(response.data.message);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除迁移记录失败:', error);
                            ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message));
                        }
                    } finally {
                        loading.value = false;
                    }
                };

                // 批量删除迁移记录
                const batchDeleteMigrationRecords = async () => {
                    if (selectedRecords.value.length === 0) {
                        ElMessage.warning('请先选择要删除的记录');
                        return;
                    }

                    try {
                        await ElMessageBox.confirm(
                            `确定要删除选中的 ${selectedRecords.value.length} 条迁移记录吗？\n\n⚠️ 此操作将同时删除所有相关的表映射记录和字段映射记录，且无法恢复！`,
                            '确认批量删除',
                            {
                                confirmButtonText: '确定删除',
                                cancelButtonText: '取消',
                                type: 'warning',
                                dangerouslyUseHTMLString: true
                            }
                        );

                        loading.value = true;
                        const ids = selectedRecords.value.map(record => record.id);

                        const response = await axios.delete('/api/migration-history/records/batch', {
                            data: { ids }
                        });

                        if (response.data.success) {
                            ElMessage.success(response.data.message);
                            selectedRecords.value = []; // 清空选择
                            await loadMigrationRecords(); // 重新加载列表
                        } else {
                            ElMessage.error(response.data.message);
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('批量删除迁移记录失败:', error);
                            ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message));
                        }
                    } finally {
                        loading.value = false;
                    }
                };

                // 组件挂载时加载数据
                onMounted(() => {
                    loadMigrationRecords();
                });

                return {
                    currentView,
                    loading,
                    detailLoading,
                    migrationRecords,
                    migrationDetail,
                    currentPage,
                    pageSize,
                    total,
                    tableKey,
                    selectedRecords,
                    ElIconRefresh,
                    ElIconArrowLeft,
                    ElIconCopyDocument,
                    ElIconDelete,
                    loadMigrationRecords,
                    viewDetail,
                    backToList,
                    handleSizeChange,
                    handleCurrentChange,
                    formatDateTime,
                    getStatusClass,
                    getStatusText,
                    getMappingRuleText,
                    getMappingRuleType,
                    getFieldRowClass,
                    copyDdlToClipboard,
                    handleSelectionChange,
                    deleteMigrationRecord,
                    batchDeleteMigrationRecords
                };
            }
        }).use(ElementPlus).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>