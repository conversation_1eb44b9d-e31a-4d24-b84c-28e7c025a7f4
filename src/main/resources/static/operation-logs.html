<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志管理 - 数据库迁移系统</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-item label {
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }

        .filter-item input,
        .filter-item select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .logs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .logs-table th,
        .logs-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .logs-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-weight: 600;
            color: white;
            font-size: 13px;
            white-space: nowrap;
        }

        .logs-table tr:hover {
            background: #f8f9fa;
        }

        /* 列宽优化 */
        .logs-table th:nth-child(1), .logs-table td:nth-child(1) { width: 12%; } /* 时间 */
        .logs-table th:nth-child(2), .logs-table td:nth-child(2) { width: 8%; }  /* 用户 */
        .logs-table th:nth-child(3), .logs-table td:nth-child(3) { width: 12%; } /* 操作类型 */
        .logs-table th:nth-child(4), .logs-table td:nth-child(4) { width: 30%; } /* 操作描述 */
        .logs-table th:nth-child(5), .logs-table td:nth-child(5) { width: 15%; } /* 目标 */
        .logs-table th:nth-child(6), .logs-table td:nth-child(6) { width: 6%; }  /* 状态 */
        .logs-table th:nth-child(7), .logs-table td:nth-child(7) { width: 8%; }  /* 执行时间 */
        .logs-table th:nth-child(8), .logs-table td:nth-child(8) { width: 9%; }  /* IP地址 */

        /* 时间列样式 */
        .time-cell {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        /* 用户名样式 */
        .user-cell {
            font-weight: 500;
            color: #333;
        }

        /* 操作描述样式 */
        .desc-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;
        }

        .desc-cell:hover {
            white-space: normal;
            word-break: break-all;
        }

        /* 目标样式 */
        .target-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 执行时间样式 */
        .time-duration {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-align: right;
        }

        /* IP地址样式 */
        .ip-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
        }

        .status-success {
            color: #28a745;
            font-weight: 500;
            padding: 2px 6px;
            background: #d4edda;
            border-radius: 8px;
            font-size: 11px;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            color: #dc3545;
            font-weight: 500;
            padding: 2px 6px;
            background: #f8d7da;
            border-radius: 8px;
            font-size: 11px;
            border: 1px solid #f5c6cb;
        }

        .operation-type {
            padding: 3px 6px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
            min-width: 60px;
            text-align: center;
            white-space: nowrap;
        }

        .type-login {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
            border: 1px solid #90caf9;
        }

        .type-migration {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }

        .type-config {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #388e3c;
            border: 1px solid #a5d6a7;
        }

        .type-query {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            color: #f57c00;
            border: 1px solid #ffcc02;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .logs-table th:nth-child(4), .logs-table td:nth-child(4) { width: 25%; }
            .logs-table th:nth-child(5), .logs-table td:nth-child(5) { width: 12%; }
        }

        @media (max-width: 768px) {
            .logs-table {
                font-size: 12px;
            }

            .logs-table th,
            .logs-table td {
                padding: 8px 4px;
            }

            .logs-table th:nth-child(1), .logs-table td:nth-child(1) { width: 15%; }
            .logs-table th:nth-child(2), .logs-table td:nth-child(2) { width: 10%; }
            .logs-table th:nth-child(3), .logs-table td:nth-child(3) { width: 15%; }
            .logs-table th:nth-child(4), .logs-table td:nth-child(4) { width: 20%; }
            .logs-table th:nth-child(5), .logs-table td:nth-child(5) { width: 10%; }
            .logs-table th:nth-child(6), .logs-table td:nth-child(6) { width: 8%; }
            .logs-table th:nth-child(7), .logs-table td:nth-child(7) { width: 10%; }
            .logs-table th:nth-child(8), .logs-table td:nth-child(8) { width: 12%; }

            .time-cell {
                font-size: 10px;
            }

            .operation-type {
                font-size: 10px;
                padding: 2px 4px;
                min-width: 50px;
            }

            .desc-cell {
                max-width: 100px;
            }

            .target-cell {
                max-width: 80px;
            }
        }

        /* 表格容器滚动 */
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="container">
            <div class="header">
                <h1>📊 操作日志管理</h1>
                <p>系统操作记录与审计</p>
            </div>

            <div class="content">
                <a href="/" class="back-link">← 返回首页</a>

                <!-- 统计信息 -->
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">{{ totalLogs }}</div>
                        <div class="stat-label">总日志数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ todayLogs }}</div>
                        <div class="stat-label">今日日志</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ errorLogs }}</div>
                        <div class="stat-label">错误日志</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ activeUsers }}</div>
                        <div class="stat-label">活跃用户</div>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="filters">
                    <div class="filter-item">
                        <label>用户名</label>
                        <input type="text" v-model="filters.username" placeholder="输入用户名">
                    </div>
                    <div class="filter-item">
                        <label>操作类型</label>
                        <select v-model="filters.operationType">
                            <option value="">全部</option>
                            <option value="USER_LOGIN">用户登录</option>
                            <option value="USER_LOGOUT">用户登出</option>
                            <option value="USER_REGISTER">用户注册</option>
                            <option value="DATABASE_MIGRATION">数据库迁移</option>
                            <option value="CONFIG_MANAGEMENT">配置管理</option>
                            <option value="LOG_QUERY">日志查询</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <label>开始时间</label>
                        <input type="datetime-local" v-model="filters.startTime">
                    </div>
                    <div class="filter-item">
                        <label>结束时间</label>
                        <input type="datetime-local" v-model="filters.endTime">
                    </div>
                    <div class="filter-item" style="align-self: end;">
                        <button class="btn btn-primary" @click="searchLogs">搜索</button>
                    </div>
                    <div class="filter-item" style="align-self: end;">
                        <button class="btn btn-secondary" @click="resetFilters">重置</button>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div v-if="errorMessage" class="error">
                    {{ errorMessage }}
                </div>

                <!-- 加载状态 -->
                <div v-if="loading" class="loading">
                    正在加载日志数据...
                </div>

                <!-- 日志表格 -->
                <div v-else>
                    <div class="table-container">
                        <table class="logs-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作类型</th>
                                <th>操作描述</th>
                                <th>目标</th>
                                <th>状态</th>
                                <th>执行时间</th>
                                <th>IP地址</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="log in logs" :key="log.id">
                                <td class="time-cell">{{ formatTime(log.createdTime) }}</td>
                                <td class="user-cell">{{ log.username || '未知' }}</td>
                                <td>
                                    <span :class="getOperationTypeClass(log.operationType)">
                                        {{ getOperationTypeName(log.operationType) }}
                                    </span>
                                </td>
                                <td class="desc-cell" :title="log.operationDesc">{{ log.operationDesc }}</td>
                                <td class="target-cell" :title="log.targetId">{{ log.targetId || '-' }}</td>
                                <td>
                                    <span :class="log.responseStatus >= 400 ? 'status-error' : 'status-success'">
                                        {{ log.responseStatus >= 400 ? '失败' : '成功' }}
                                    </span>
                                </td>
                                <td class="time-duration">{{ log.executionTime }}ms</td>
                                <td class="ip-cell">{{ log.clientIp }}</td>
                            </tr>
                        </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <button @click="prevPage" :disabled="currentPage <= 1">上一页</button>
                        <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
                        <button @click="nextPage" :disabled="currentPage >= totalPages">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script src="js/satoken-auth.js"></script>
    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载操作日志页面...');

                cdnLoader.updateProgress(20);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(90);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
                document.body.innerHTML = '<div style="text-align: center; padding: 50px; color: red;">页面加载失败，请刷新重试</div>';
            }
        }

        function initVueApp() {
            const { createApp, ref, onMounted } = Vue;

            createApp({
                setup() {
                    const logs = ref([]);
                    const loading = ref(false);
                    const errorMessage = ref('');
                    const currentPage = ref(1);
                    const totalPages = ref(1);
                    const totalLogs = ref(0);
                    const todayLogs = ref(0);
                    const errorLogs = ref(0);
                    const activeUsers = ref(0);

                    const filters = ref({
                        username: '',
                        operationType: '',
                        startTime: '',
                        endTime: ''
                    });

                    // 加载日志数据
                    const loadLogs = async () => {
                        loading.value = true;
                        errorMessage.value = '';

                        try {
                            const params = new URLSearchParams({
                                page: currentPage.value,
                                pageSize: 20
                            });

                            if (filters.value.username) params.append('username', filters.value.username);
                            if (filters.value.operationType) params.append('operationType', filters.value.operationType);
                            if (filters.value.startTime) params.append('startTime', filters.value.startTime);
                            if (filters.value.endTime) params.append('endTime', filters.value.endTime);

                            const response = await axios.get(`/api/operation-logs/list?${params}`);

                            if (response.data.success) {
                                logs.value = response.data.data;
                                totalPages.value = response.data.totalPages;
                                totalLogs.value = response.data.total;
                            } else {
                                errorMessage.value = response.data.message || '加载日志失败';
                            }
                        } catch (error) {
                            console.error('加载日志失败:', error);
                            errorMessage.value = '网络错误，请稍后重试';
                        } finally {
                            loading.value = false;
                        }
                    };

                    // 搜索日志
                    const searchLogs = () => {
                        currentPage.value = 1;
                        loadLogs();
                    };

                    // 重置筛选条件
                    const resetFilters = () => {
                        filters.value = {
                            username: '',
                            operationType: '',
                            startTime: '',
                            endTime: ''
                        };
                        currentPage.value = 1;
                        loadLogs();
                    };

                    // 上一页
                    const prevPage = () => {
                        if (currentPage.value > 1) {
                            currentPage.value--;
                            loadLogs();
                        }
                    };

                    // 下一页
                    const nextPage = () => {
                        if (currentPage.value < totalPages.value) {
                            currentPage.value++;
                            loadLogs();
                        }
                    };

                    // 格式化时间
                    const formatTime = (timeStr) => {
                        if (!timeStr) return '-';
                        const date = new Date(timeStr);
                        const now = new Date();
                        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

                        if (logDate.getTime() === today.getTime()) {
                            // 今天的记录只显示时间
                            return date.toLocaleTimeString('zh-CN', {
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });
                        } else {
                            // 其他日期显示月日和时间
                            return date.toLocaleString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        }
                    };

                    // 获取操作类型样式类
                    const getOperationTypeClass = (type) => {
                        const classMap = {
                            'USER_LOGIN': 'operation-type type-login',
                            'USER_LOGOUT': 'operation-type type-login',
                            'USER_REGISTER': 'operation-type type-login',
                            'DATABASE_MIGRATION': 'operation-type type-migration',
                            'CONFIG_MANAGEMENT': 'operation-type type-config',
                            'LOG_QUERY': 'operation-type type-query'
                        };
                        return classMap[type] || 'operation-type';
                    };

                    // 获取操作类型名称
                    const getOperationTypeName = (type) => {
                        const nameMap = {
                            'USER_LOGIN': '用户登录',
                            'USER_LOGOUT': '用户登出',
                            'USER_REGISTER': '用户注册',
                            'DATABASE_MIGRATION': '数据库迁移',
                            'CONFIG_MANAGEMENT': '配置管理',
                            'LOG_QUERY': '日志查询'
                        };
                        return nameMap[type] || type;
                    };

                    // 页面加载时初始化
                    onMounted(() => {
                        loadLogs();
                    });

                    return {
                        logs,
                        loading,
                        errorMessage,
                        currentPage,
                        totalPages,
                        totalLogs,
                        todayLogs,
                        errorLogs,
                        activeUsers,
                        filters,
                        loadLogs,
                        searchLogs,
                        resetFilters,
                        prevPage,
                        nextPage,
                        formatTime,
                        getOperationTypeClass,
                        getOperationTypeName
                    };
                }
            }).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
