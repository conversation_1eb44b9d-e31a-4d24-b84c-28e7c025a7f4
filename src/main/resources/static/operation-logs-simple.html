<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志管理 - 数据库迁移系统</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-item label {
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }

        .filter-item input,
        .filter-item select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .logs-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            min-width: 1000px;
        }

        .logs-table th,
        .logs-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .logs-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-weight: 600;
            color: white;
            font-size: 13px;
            white-space: nowrap;
        }

        .logs-table tr:hover {
            background: #f8f9fa;
        }

        /* 列宽优化 */
        .logs-table th:nth-child(1), .logs-table td:nth-child(1) { width: 12%; }
        .logs-table th:nth-child(2), .logs-table td:nth-child(2) { width: 8%; }
        .logs-table th:nth-child(3), .logs-table td:nth-child(3) { width: 12%; }
        .logs-table th:nth-child(4), .logs-table td:nth-child(4) { width: 30%; }
        .logs-table th:nth-child(5), .logs-table td:nth-child(5) { width: 15%; }
        .logs-table th:nth-child(6), .logs-table td:nth-child(6) { width: 6%; }
        .logs-table th:nth-child(7), .logs-table td:nth-child(7) { width: 8%; }
        .logs-table th:nth-child(8), .logs-table td:nth-child(8) { width: 9%; }

        .time-cell {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .user-cell {
            font-weight: 500;
            color: #333;
        }

        .desc-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;
        }

        .desc-cell:hover {
            white-space: normal;
            word-break: break-all;
        }

        .target-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .time-duration {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-align: right;
        }

        .ip-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #666;
        }

        .operation-type {
            padding: 3px 6px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
            min-width: 60px;
            text-align: center;
            white-space: nowrap;
        }

        .type-login {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
            border: 1px solid #90caf9;
        }

        .type-migration {
            background: linear-gradient(135deg, #f3e5f5, #e1bee7);
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }

        .type-config {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: #388e3c;
            border: 1px solid #a5d6a7;
        }

        .type-query {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            color: #f57c00;
            border: 1px solid #ffcc02;
        }

        .status-success {
            color: #28a745;
            font-weight: 500;
            padding: 2px 6px;
            background: #d4edda;
            border-radius: 8px;
            font-size: 11px;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            color: #dc3545;
            font-weight: 500;
            padding: 2px 6px;
            background: #f8d7da;
            border-radius: 8px;
            font-size: 11px;
            border: 1px solid #f5c6cb;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .logs-table {
                font-size: 12px;
            }
            
            .logs-table th,
            .logs-table td {
                padding: 8px 4px;
            }

            .time-cell {
                font-size: 10px;
            }

            .operation-type {
                font-size: 10px;
                padding: 2px 4px;
                min-width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 操作日志管理</h1>
            <p>系统操作记录与审计</p>
        </div>

        <div class="content">
            <a href="/" class="back-link">← 返回首页</a>

            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalLogs">80</div>
                    <div class="stat-label">总日志数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="todayLogs">80</div>
                    <div class="stat-label">今日日志</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="errorLogs">0</div>
                    <div class="stat-label">错误日志</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeUsers">2</div>
                    <div class="stat-label">活跃用户</div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="filters">
                <div class="filter-item">
                    <label>用户名</label>
                    <input type="text" id="usernameFilter" placeholder="输入用户名">
                </div>
                <div class="filter-item">
                    <label>操作类型</label>
                    <select id="operationTypeFilter">
                        <option value="">全部</option>
                        <option value="USER_LOGIN">用户登录</option>
                        <option value="USER_LOGOUT">用户登出</option>
                        <option value="USER_REGISTER">用户注册</option>
                        <option value="DATABASE_MIGRATION">数据库迁移</option>
                        <option value="CONFIG_MANAGEMENT">配置管理</option>
                        <option value="LOG_QUERY">日志查询</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>开始时间</label>
                    <input type="datetime-local" id="startTimeFilter">
                </div>
                <div class="filter-item">
                    <label>结束时间</label>
                    <input type="datetime-local" id="endTimeFilter">
                </div>
                <div class="filter-item" style="align-self: end;">
                    <button class="btn btn-primary" onclick="searchLogs()">搜索</button>
                </div>
                <div class="filter-item" style="align-self: end;">
                    <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                </div>
            </div>

            <!-- 错误信息 -->
            <div id="errorMessage" class="error" style="display: none;"></div>

            <!-- 加载状态 -->
            <div id="loading" class="loading" style="display: none;">
                正在加载日志数据...
            </div>

            <!-- 日志表格 -->
            <div id="logsContainer">
                <div class="table-container">
                    <table class="logs-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>用户</th>
                                <th>操作类型</th>
                                <th>操作描述</th>
                                <th>目标</th>
                                <th>状态</th>
                                <th>执行时间</th>
                                <th>IP地址</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination" id="pagination">
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        let pageSize = 20;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
            // 延迟加载统计数据，确保页面完全加载
            setTimeout(() => {
                loadStatistics();
            }, 1000);
        });

        // 加载日志数据
        async function loadLogs() {
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('errorMessage');
            const logsContainer = document.getElementById('logsContainer');

            loading.style.display = 'block';
            errorMessage.style.display = 'none';
            logsContainer.style.display = 'none';

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    pageSize: pageSize
                });

                const username = document.getElementById('usernameFilter').value.trim();
                const operationType = document.getElementById('operationTypeFilter').value;
                const startTime = document.getElementById('startTimeFilter').value;
                const endTime = document.getElementById('endTimeFilter').value;

                if (username) params.append('username', username);
                if (operationType) params.append('operationType', operationType);
                if (startTime) {
                    // 确保时间格式正确传递
                    params.append('startTime', startTime);
                }
                if (endTime) {
                    // 确保时间格式正确传递
                    params.append('endTime', endTime);
                }

                const token = getAuthToken();
                const response = await fetch(`/api/operation-logs/list?${params}`, {
                    headers: {
                        'DB_COPY_TOKEN': token
                    }
                });

                const data = await response.json();

                if (data.success) {
                    renderLogs(data.data);
                    updateStats(data.total);
                    renderPagination(data.totalPages);
                } else {
                    showError(data.message || '加载日志失败');
                }
            } catch (error) {
                console.error('加载日志失败:', error);
                showError('网络错误，请稍后重试');
            } finally {
                loading.style.display = 'none';
                logsContainer.style.display = 'block';
            }
        }

        // 渲染日志表格
        function renderLogs(logs) {
            const tbody = document.getElementById('logsTableBody');
            tbody.innerHTML = '';

            logs.forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="time-cell">${formatTime(log.createdTime)}</td>
                    <td class="user-cell">${log.username || '未知'}</td>
                    <td>
                        <span class="${getOperationTypeClass(log.operationType)}">
                            ${getOperationTypeName(log.operationType)}
                        </span>
                    </td>
                    <td class="desc-cell" title="${log.operationDesc}">${log.operationDesc}</td>
                    <td class="target-cell" title="${log.targetId}">${log.targetId || '-'}</td>
                    <td>
                        <span class="${log.responseStatus >= 400 ? 'status-error' : 'status-success'}">
                            ${log.responseStatus >= 400 ? '失败' : '成功'}
                        </span>
                    </td>
                    <td class="time-duration">${log.executionTime}ms</td>
                    <td class="ip-cell">${log.clientIp}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                const token = getAuthToken();
                console.log('使用token:', token);

                const response = await fetch('/api/operation-logs/simple-stats', {
                    headers: {
                        'DB_COPY_TOKEN': token
                    }
                });

                console.log('统计API响应状态:', response.status);
                const result = await response.json();
                console.log('统计API响应数据:', result);

                if (result.success) {
                    updateSimpleStatistics(result.data);
                } else {
                    console.error('加载统计数据失败:', result.message);
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 更新简单统计信息
        function updateSimpleStatistics(data) {
            console.log('简单统计数据:', data);

            try {
                // 直接使用API返回的数据
                const totalLogs = parseInt(data.totalLogs) || 0;
                const todayLogs = parseInt(data.todayLogs) || 0;
                const errorLogs = parseInt(data.errorLogs) || 0;
                const activeUsers = parseInt(data.activeUsers) || 0;

                // 更新页面显示
                const totalElement = document.getElementById('totalLogs');
                const todayElement = document.getElementById('todayLogs');
                const errorElement = document.getElementById('errorLogs');
                const usersElement = document.getElementById('activeUsers');

                if (totalElement) totalElement.textContent = totalLogs.toLocaleString();
                if (todayElement) todayElement.textContent = todayLogs.toLocaleString();
                if (errorElement) errorElement.textContent = errorLogs.toLocaleString();
                if (usersElement) usersElement.textContent = activeUsers.toLocaleString();

                console.log('简单统计更新完成:', { totalLogs, todayLogs, errorLogs, activeUsers });
            } catch (error) {
                console.error('更新简单统计显示失败:', error);
            }
        }

        // 更新统计信息
        function updateStatistics(data) {
            console.log('统计数据:', data);

            try {
                // 计算今日日志数量
                const today = new Date().toISOString().split('T')[0];
                let todayLogs = 0;

                if (data.dailyOperationStats && Array.isArray(data.dailyOperationStats)) {
                    const todayStat = data.dailyOperationStats.find(stat => {
                        const statDate = stat.date;
                        return statDate && statDate.toString().startsWith(today);
                    });
                    todayLogs = todayStat ? (parseInt(todayStat.count) || 0) : 0;
                }

                // 计算错误日志数量
                const errorLogs = parseInt(data.errorStats?.error_count) || 0;

                // 计算活跃用户数量（最近7天）
                const activeUsers = data.userOperationStats?.length || 0;

                // 计算总日志数量（从每日统计中累加）
                let totalLogs = 0;
                if (data.dailyOperationStats && Array.isArray(data.dailyOperationStats)) {
                    totalLogs = data.dailyOperationStats.reduce((sum, stat) =>
                        sum + (parseInt(stat.count) || 0), 0
                    );
                }

                // 更新页面显示
                const totalElement = document.getElementById('totalLogs');
                const todayElement = document.getElementById('todayLogs');
                const errorElement = document.getElementById('errorLogs');
                const usersElement = document.getElementById('activeUsers');

                if (totalElement) totalElement.textContent = totalLogs.toLocaleString();
                if (todayElement) todayElement.textContent = todayLogs.toLocaleString();
                if (errorElement) errorElement.textContent = errorLogs.toLocaleString();
                if (usersElement) usersElement.textContent = activeUsers.toLocaleString();

                console.log('统计更新完成:', { totalLogs, todayLogs, errorLogs, activeUsers });
            } catch (error) {
                console.error('更新统计显示失败:', error);
            }
        }

        // 更新统计信息（兼容原有调用）
        function updateStats(total) {
            document.getElementById('totalLogs').textContent = total;
            // 保持其他统计不变，避免覆盖已加载的数据
        }

        // 渲染分页
        function renderPagination(totalPagesCount) {
            totalPages = totalPagesCount;
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage <= 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    loadLogs();
                }
            };
            pagination.appendChild(prevBtn);

            // 页码信息
            const pageInfo = document.createElement('span');
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
            pagination.appendChild(pageInfo);

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadLogs();
                }
            };
            pagination.appendChild(nextBtn);
        }

        // 搜索日志
        function searchLogs() {
            currentPage = 1;
            loadLogs();
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('usernameFilter').value = '';
            document.getElementById('operationTypeFilter').value = '';
            document.getElementById('startTimeFilter').value = '';
            document.getElementById('endTimeFilter').value = '';
            currentPage = 1;
            loadLogs();
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '-';
            const date = new Date(timeStr);
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            
            if (logDate.getTime() === today.getTime()) {
                return date.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit',
                    second: '2-digit'
                });
            } else {
                return date.toLocaleString('zh-CN', { 
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit', 
                    minute: '2-digit'
                });
            }
        }

        // 获取操作类型样式类
        function getOperationTypeClass(type) {
            const classMap = {
                'USER_LOGIN': 'operation-type type-login',
                'USER_LOGOUT': 'operation-type type-login',
                'USER_REGISTER': 'operation-type type-login',
                'DATABASE_MIGRATION': 'operation-type type-migration',
                'CONFIG_MANAGEMENT': 'operation-type type-config',
                'LOG_QUERY': 'operation-type type-query'
            };
            return classMap[type] || 'operation-type';
        }

        // 获取操作类型名称
        function getOperationTypeName(type) {
            const nameMap = {
                'USER_LOGIN': '用户登录',
                'USER_LOGOUT': '用户登出',
                'USER_REGISTER': '用户注册',
                'DATABASE_MIGRATION': '数据库迁移',
                'CONFIG_MANAGEMENT': '配置管理',
                'LOG_QUERY': '日志查询'
            };
            return nameMap[type] || type;
        }

        // 获取认证token
        function getAuthToken() {
            // 尝试从多个地方获取token
            return localStorage.getItem('authToken') ||
                   sessionStorage.getItem('authToken') ||
                   getCookie('DB_COPY_TOKEN') ||
                   'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiVE9RZmxOMTVFZXZON09qQXhXaDNnT0RhM2lPdU5FOHUifQ.CJ7OH1_hrFcymwAvd4G2HKmDa0YiAuTCfuDs0bPkE24';
        }

        // 获取Cookie值
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return '';
        }
    </script>
</body>
</html>
