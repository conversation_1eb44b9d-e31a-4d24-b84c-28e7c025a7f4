<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据迁移系统 - 引导式操作</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="shortcut icon" href="favicon.ico">
    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: #333;
        }

        .migration-container {
            max-width: 1100px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 12px;
        }

        .header h1 {
            color: #2c3e50;
            margin: 0 0 4px 0;
            font-size: 1.4em;
            font-weight: 300;
        }

        .header p {
            color: #7f8c8d;
            font-size: 0.85em;
            margin: 0;
        }

        .steps-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .steps-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 8px;
            text-align: center;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .steps-progress {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 8px;
        }

        .step-item {
            display: flex;
            align-items: center;
            position: relative;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            background: rgba(255,255,255,0.3);
            border: 2px solid rgba(255,255,255,0.5);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .step-circle.active {
            background: white;
            color: #667eea;
            border-color: white;
            transform: scale(1.1);
        }

        .step-circle.completed {
            background: rgba(255,255,255,0.9);
            color: #667eea;
            border-color: rgba(255,255,255,0.9);
        }

        .step-line {
            width: 60px;
            height: 2px;
            background: rgba(255,255,255,0.3);
            margin: 0 8px;
        }

        .step-line.completed {
            background: rgba(255,255,255,0.8);
        }

        .step-title {
            color: white;
            font-size: 18px;
            font-weight: 300;
            margin-top: 8px;
        }

        .step-content {
            padding: 10px;
        }

        .step-panel {
            display: none;
        }

        .step-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 历史记录检查样式 */
        .loading-history {
            text-align: center;
            padding: 40px;
        }

        .loading-history .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        .no-history, .history-mismatch, .history-match {
            text-align: center;
            padding: 30px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .no-history {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
        }

        .no-history-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .history-mismatch {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
        }

        .history-mismatch-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .history-match {
            background: #d4edda;
            border: 2px solid #00b894;
        }

        .history-match-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .history-list {
            text-align: left;
            margin-top: 20px;
        }

        .history-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .history-basic-info {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 8px;
        }

        .history-date {
            font-weight: 600;
            color: #495057;
        }

        .history-target {
            color: #007bff;
            font-weight: 500;
        }

        .history-fields-count {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .field-diff-summary {
            font-size: 14px;
            color: #6c757d;
        }

        .diff-label {
            font-weight: 500;
        }

        .diff-details {
            margin-left: 5px;
        }

        .matched-record {
            text-align: left;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .record-header h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .record-meta {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .field-comparison h6 {
            margin: 15px 0 10px 0;
            color: #495057;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            font-size: 13px;
        }

        .comparison-table td {
            font-size: 13px;
        }

        /* 系统字段行样式 */
        .system-field-row {
            background-color: #f0f9ff;
            border-left: 3px solid #3b82f6;
        }

        /* 主键映射行样式 */
        .primary-mapping-row {
            background-color: #fef3c7;
            border-left: 3px solid #f59e0b;
        }

        /* 空字段样式 */
        .empty-field {
            background-color: #f9fafb;
            color: #9ca3af;
            font-style: italic;
            text-align: center;
        }

        .empty-type {
            color: #9ca3af;
            font-style: italic;
        }

        /* 目标系统字段样式 */
        .target-system-field {
            position: relative;
            font-weight: 600;
            color: #1d4ed8;
        }

        .system-field-label {
            display: inline-block;
            background-color: #dbeafe;
            color: #1e40af;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
            font-weight: 500;
        }

        .system-type {
            background-color: #3b82f6 !important;
            color: white !important;
        }

        .history-actions {
            margin-top: 20px;
        }

        .action-option {
            margin-bottom: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }

        .action-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .radio-label {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            width: 100%;
        }

        .radio-label input[type="radio"] {
            margin-right: 12px;
            margin-top: 2px;
        }

        .radio-text {
            display: flex;
            flex-direction: column;
        }

        .radio-text strong {
            margin-bottom: 4px;
            color: #495057;
        }

        .radio-text small {
            color: #6c757d;
            font-size: 13px;
        }

        .action-option:has(input:checked) {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .database-list-container {
            margin-top: 10px;
        }

        .step-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            overflow: hidden;
            max-height: 400px;
            display: block;
            overflow-y: auto;
        }

        .data-table thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th {
            padding: 8px 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85em;
        }

        .data-table tbody {
            display: table-row-group;
        }

        .data-table tr {
            display: table-row;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f8f9fa;
        }

        .data-table tr:hover {
            background: #e3f2fd;
        }

        .data-table tr.selected {
            background: #667eea;
            color: white;
        }

        .data-table td {
            padding: 6px 12px;
            font-size: 0.85em;
            display: table-cell;
            vertical-align: middle;
        }

        .data-table tr.selected td {
            color: white;
        }

        .table-icon {
            font-size: 1.2em;
            margin-right: 8px;
        }

        .db-name {
            font-weight: 600;
            font-size: 0.9em;
        }

        .db-type {
            color: #666;
            font-size: 0.8em;
        }

        .data-table tr.selected .db-type {
            color: rgba(255, 255, 255, 0.8);
        }

        .db-status {
            font-size: 0.8em;
        }

        .status-connected {
            color: #4CAF50;
        }

        .status-disconnected {
            color: #f44336;
        }

        .data-table tr.selected .status-connected,
        .data-table tr.selected .status-disconnected {
            color: rgba(255, 255, 255, 0.9);
        }



        /* 删除旧的数据库和表格样式，使用新的表格样式 */

        .mapping-container {
            display: grid;
            grid-template-columns: 1fr 50px 1fr;
            gap: 15px;
            align-items: start;
        }

        .mapping-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .mapping-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
            text-align: center;
        }

        .field-mapping-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 6px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            font-size: 0.9em;
        }

        .field-original {
            flex: 1;
            color: #666;
        }

        .field-optimized {
            flex: 1;
            font-weight: 600;
            color: #667eea;
        }

        .mapping-arrow {
            display: flex;
            justify-content: center;
            align-items: center;
            height: auto;
            min-height: 100px;
        }

        .arrow-icon {
            width: 32px;
            height: 32px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
        }

        .btn {
            padding: 8px 24px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 30px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f5f5f5;
            color: #666;
            border: 2px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background: #e8e8e8;
            border-color: #ccc;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .summary-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .summary-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            border-bottom: 1px solid #e0e0e0;
            font-size: 0.9em;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .result-panel {
            text-align: center;
            padding: 30px 15px;
        }

        .empty-state {
            text-align: center;
            padding: 30px 15px;
            color: #666;
        }

        .empty-icon {
            font-size: 36px;
            margin-bottom: 12px;
        }

        .empty-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }

        .result-icon {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 36px;
        }

        .result-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .result-details {
            color: #666;
            margin-bottom: 30px;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 科技感错误消息样式 */
        .error-message {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%);
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 12px;
            padding: 16px;
            margin-top: 20px;
            color: #d32f2f;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .error-message::before {
            content: '⚠️';
            margin-right: 8px;
        }

        /* 步骤操作按钮增强样式 */
        .step-actions .btn-primary {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
            position: relative;
            overflow: hidden;
        }

        .step-actions .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .step-actions .btn-primary:hover::before {
            left: 100%;
        }

        .step-actions .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        /* 迁移按钮进度条样式 */
        .migration-btn.migrating {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2) !important;
        }

        .migration-progress {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border-radius: 6px;
        }

        .progress-bar-bg {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
            border-radius: 6px;
            z-index: 1;
        }

        .progress-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
            font-weight: 500;
        }

        .progress-percentage {
            font-size: 14px;
            font-weight: bold;
        }

        .progress-status {
            font-size: 13px;
        }

        .migration-warning {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: #fff3cd;
            color: #856404;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            border: 1px solid #ffeaa7;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 10;
        }

        .migration-warning::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #ffeaa7;
        }

        .large-data-info {
            margin-top: 8px;
            padding: 8px;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 4px;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .large-data-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            color: #28a745;
            font-size: 11px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .large-data-badge i {
            font-size: 10px;
        }

        .migration-details {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            font-size: 10px;
            color: #666;
        }

        .migration-details span {
            background: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .migration-status {
            font-size: 14px;
            color: white;
            white-space: nowrap;
        }

        /* 数据库列表滚动条样式 */
        .database-list::-webkit-scrollbar {
            width: 8px;
        }

        .database-list::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        .database-list::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 4px;
        }

        .database-list::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #45a049, #388E3C);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .database-item {
                padding: 14px 16px;
            }
            
            .database-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
            
            .database-name {
                font-size: 16px;
            }
            
            .database-info {
                font-size: 13px;
            }
            
            .step-actions {
                padding: 15px;
            }
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #f44336;
            background: #ffebee;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }

        .success-message {
            color: #4CAF50;
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }

        /* 新增的表格和工具栏样式 */
        .optimization-toolbar {
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-name-section {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .table-name-section label {
            font-weight: bold;
            color: #333;
            min-width: 50px;
        }

        .table-name-input {
            flex: 1;
            max-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .table-description-input {
            flex: 2;
            max-width: 400px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-left: 8px;
            color: #333;
        }

        .table-description-input::placeholder {
            color: #999;
            font-style: italic;
        }

        .batch-operations {
            flex-shrink: 0;
        }

        .btn-ai-batch {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-ai-batch:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .btn-ai-batch:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .field-mapping-table {
            margin-top: 15px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            width: 100%;
        }

        .mapping-table {
            width: 100%;
            min-width: 1000px;
            border-collapse: collapse;
            background: white;
            font-size: 14px;
            position: relative;
        }

        .mapping-table th {
            position: sticky;
            top: 0;
            z-index: 10;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            border: none;
        }

        .mapping-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        .field-row:hover {
            background: #f8f9fa;
        }

        .field-row:nth-child(even) {
            background: #fafafa;
        }

        .field-row:nth-child(even):hover {
            background: #f0f0f0;
        }

        .field-name {
            font-weight: bold;
            color: #333;
            font-family: 'Courier New', monospace;
        }

        .type-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            white-space: nowrap;
            display: inline-block;
        }

        .field-type {
            text-align: left;
            white-space: nowrap;
            padding-left: 8px;
        }

        .field-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            transition: all 0.3s ease;
        }

        .field-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .field-input.optimized {
            background: #e8f5e8;
            border-color: #4caf50;
        }

        .ai-status {
            text-align: center;
            padding: 6px 4px;
        }

        .status-indicators {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .status-check {
            display: inline-block;
            font-size: 14px;
            line-height: 1;
            cursor: default;
        }

        .status-check.field-optimized {
            color: #28a745;
            font-weight: bold;
        }

        .status-check.comment-generated {
            color: #ffc107;
            font-size: 12px;
        }

        .comment-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .comment-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .field-actions {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        .btn-ai {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .btn-ai:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .btn-ai:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background: #e8e8e8 !important;
            color: #999 !important;
        }
        
        .btn-ai:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .btn-ai-comment {
            background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-ai-comment:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(255, 112, 67, 0.3);
        }

        .btn-ai-comment:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 日志区域样式 */
        .log-container {
            margin-top: 30px;
            background: #1e1e1e;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .log-header {
            background: #2d2d2d;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #404040;
        }

        .log-title {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
        }

        .log-icon {
            font-size: 16px;
        }

        .log-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .log-status.connected {
            background: #10b981;
            color: white;
        }

        .log-status.disconnected {
            background: #ef4444;
            color: white;
        }

        .log-controls {
            display: flex;
            gap: 8px;
        }

        .log-btn {
            background: #404040;
            color: #ffffff;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .log-btn:hover {
            background: #505050;
        }

        .log-btn.active {
            background: #667eea;
        }

        .log-content {
            height: 300px;
            overflow-y: auto;
            background: #1e1e1e;
        }

        .log-messages {
            padding: 10px;
        }

        .log-message {
            color: #e5e5e5;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 2px;
            padding: 2px 0;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .log-message.info {
            color: #60a5fa;
        }

        .log-message.warn {
            color: #fbbf24;
        }

        .log-message.error {
            color: #f87171;
        }

        .log-message.debug {
            color: #a3a3a3;
        }

        .log-empty {
            text-align: center;
            color: #6b7280;
            padding: 40px 20px;
            font-style: italic;
        }

        .log-empty-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 8px;
        }

        /* 日志滚动条样式 */
        .log-content::-webkit-scrollbar {
            width: 8px;
        }

        .log-content::-webkit-scrollbar-track {
            background: #2d2d2d;
        }

        .log-content::-webkit-scrollbar-thumb {
            background: #505050;
            border-radius: 4px;
        }

        .log-content::-webkit-scrollbar-thumb:hover {
            background: #606060;
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="migration-container">
            <div class="header">
                <h1>数据迁移系统</h1>
                <p>智能化的数据库表迁移和字段优化平台</p>
            </div>

            <div class="steps-container">
                <!-- 步骤指示器 -->
                <div class="steps-header">
                    <div class="steps-progress">
                        <div v-for="(step, index) in steps" :key="index" class="step-item">
                            <div 
                                class="step-circle" 
                                :class="{
                                    'active': currentStep === index + 1,
                                    'completed': currentStep > index + 1
                                }"
                                @click="goToStep(index + 1)"
                            >
                                {{ index + 1 }}
                            </div>
                            <div 
                                v-if="index < steps.length - 1" 
                                class="step-line"
                                :class="{ 'completed': currentStep > index + 1 }"
                            ></div>
                        </div>
                    </div>
                    <div class="step-title">{{ steps[currentStep - 1]?.title }}</div>
                </div>

                <!-- 步骤内容 -->
                <div class="step-content">
                    <!-- 步骤1: 选取源数据库 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 1 }">
                        <h3>选择源数据库</h3>
                        <p style="color: #666; margin-bottom: 15px;">请选择要迁移数据的源数据库</p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                                style="visibility: hidden;"
                            >
                                上一步
                            </button>
                            <button 
                                class="btn btn-primary" 
                                @click="nextStep"
                                :disabled="!canProceed"
                            >
                                下一步
                            </button>
                        </div>

                        <div v-if="loadingDatabases" class="loading">
                            <div class="loading-spinner"></div>
                            <p>正在加载数据库列表...</p>
                        </div>

                        <div v-else class="database-list-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">类型</th>
                                        <th>数据库名称</th>
                                        <th style="width: 100px;">连接状态</th>
                                        <th style="width: 120px;">环境</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr 
                                    v-for="db in availableSourceDatabases" 
                                    :key="db.name"
                                    :class="{ 'selected': selectedSourceDb === db.name }"
                                    @click="selectSourceDatabase(db.name)"
                                >
                                        <td>
                                            <span class="table-icon">🗄️</span>
                                        </td>
                                        <td>
                                            <div class="db-name">{{ db.name }}</div>
                                            <div class="db-type">PostgreSQL 数据库</div>
                                        </td>
                                        <td>
                                            <span :class="db.connected ? 'status-connected' : 'status-disconnected'">
                                                ● {{ db.connected ? '已连接' : '未连接' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="db-status">📊 生产环境</div>
                                            <div class="db-status">🔒 安全连接</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 空状态提示 -->
                        <div v-if="!loadingDatabases && availableSourceDatabases.length === 0" style="text-align: center; color: #666; padding: 30px;">
                            <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                            <div style="font-weight: 600; margin-bottom: 8px;">未找到PostgreSQL数据库</div>
                            <div style="font-size: 0.9em;">
                                请确保PostgreSQL连接配置正确，并且PostgreSQL服务正在运行
                            </div>
                        </div>

                        <div v-if="errorMessage" class="error-message">
                            {{ errorMessage }}
                        </div>
                    </div>

                    <!-- 步骤2: 选择源数据库表 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 2 }">
                        <h3>选择要迁移的表</h3>
                        <p style="color: #666; margin-bottom: 15px;">从 <strong>{{ selectedSourceDb }}</strong> 数据库中选择要迁移的表</p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                            >
                                上一步
                            </button>
                            <button 
                                class="btn btn-primary" 
                                @click="nextStep"
                                :disabled="!canProceed"
                            >
                                下一步
                            </button>
                        </div>

                        <div v-if="loadingTables" class="loading">
                            <div class="loading-spinner"></div>
                            <p>正在加载表列表...</p>
                        </div>

                        <div v-else-if="tables.length > 0" class="database-list-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">类型</th>
                                        <th>表名称</th>
                                        <th style="width: 100px;">行数</th>
                                        <th style="width: 120px;">状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr 
                                    v-for="table in tables" 
                                    :key="table.tableName"
                                    :class="{ 'selected': selectedTable === table.tableName }"
                                    @click="selectTable(table.tableName)"
                                >
                                        <td>
                                            <span class="table-icon">📋</span>
                                        </td>
                                        <td>
                                            <div class="db-name">{{ table.tableName }}</div>
                                            <div class="db-type">{{ table.tableType || 'BASE TABLE' }}</div>
                                        </td>
                                        <td>
                                            <span class="db-status">{{ table.rowCount || '未知' }}</span>
                                        </td>
                                        <td>
                                            <div class="db-status">📊 数据表</div>
                                            <div class="db-status">🔄 可迁移</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div v-else-if="!loadingTables">
                            <p style="text-align: center; color: #666; padding: 30px;">
                                该数据库中没有找到表
                                <br>
                                <small style="color: #999;">调试信息: 表数量 = {{ tables.length }}</small>
                            </p>
                        </div>
                    </div>

                    <!-- 步骤3: 选择目标数据库 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 3 }">
                        <h3>选择目标数据库</h3>
                        <p style="color: #666; margin-bottom: 15px;">请选择数据迁移的目标数据库</p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                            >
                                上一步
                            </button>
                            <button 
                                class="btn btn-primary" 
                                @click="nextStep"
                                :disabled="!canProceed"
                            >
                                下一步
                            </button>
                        </div>
                        
                        <div class="database-list-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">类型</th>
                                        <th>数据库名称</th>
                                        <th style="width: 100px;">数据库类型</th>
                                        <th style="width: 120px;">环境</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr 
                                    v-for="db in availableTargetDatabases" 
                                    :key="db.name"
                                        :class="{ 'selected': selectedTargetDb === db.name && selectedTargetDbType === db.type }"
                                        @click="selectTargetDatabase(db.name, db.type)"
                                >
                                        <td>
                                            <span class="table-icon">🐬</span>
                                        </td>
                                        <td>
                                            <div class="db-name">{{ db.name }}</div>
                                            <div class="db-type">MySQL 数据库</div>
                                        </td>
                                        <td>
                                            <span class="db-status">MySQL</span>
                                        </td>
                                        <td>
                                            <div class="db-status">🚀 跨数据库迁移</div>
                                            <div class="db-status">🔄 双主键设计</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                                
                            <!-- 空状态提示 -->
                            <div v-if="availableTargetDatabases.length === 0" style="text-align: center; color: #666; padding: 30px;">
                                <div style="font-size: 2em; margin-bottom: 10px;">🔍</div>
                                <div style="font-weight: 600; margin-bottom: 8px;">未找到MySQL数据库</div>
                                <div style="font-size: 0.9em;">
                                    请确保MySQL连接配置正确，并且MySQL服务正在运行
                                    </div>
                                        </div>
                                        </div>
                        </div>
                    </div>

                    <!-- 步骤4: 检查迁移历史记录 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 4 }">
                        <h3>检查迁移历史记录</h3>
                        <p style="color: #666; margin-bottom: 15px;">
                            检查表 <strong>{{ selectedTable }}</strong> 是否有历史迁移记录，如果有相同字段结构的记录，可以直接复用历史DDL
                        </p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                            >
                                上一步
                            </button>
                            <button 
                                class="btn btn-primary" 
                                @click="nextStep"
                                :disabled="!canProceed"
                            >
                                {{ hasMatchingHistory && useHistoryRecord ? '跳过AI优化' : '继续AI优化' }}
                            </button>
                        </div>

                        <!-- 加载状态 -->
                        <div v-if="loadingHistory" class="loading-history">
                            <div class="loading-spinner"></div>
                            <p>正在查询迁移历史记录...</p>
                        </div>

                        <!-- 无历史记录 -->
                        <div v-else-if="!hasHistoryRecords" class="no-history">
                            <div class="no-history-icon">📋</div>
                            <h4>未找到历史迁移记录</h4>
                            <p>表 <strong>{{ selectedTable }}</strong> 没有历史迁移记录，将进入AI优化流程。</p>
                        </div>

                        <!-- 有历史记录但字段不匹配 -->
                        <div v-else-if="hasHistoryRecords && !hasMatchingHistory" class="history-mismatch">
                            <div class="history-mismatch-icon">⚠️</div>
                            <h4>找到历史记录但字段结构不匹配</h4>
                            <p>找到 {{ historyRecords.length }} 条历史迁移记录，但字段结构与当前表不完全匹配。</p>
                            
                            <!-- 历史记录列表 -->
                            <div class="history-list">
                                <h5>历史迁移记录:</h5>
                                <div v-for="record in historyRecords" :key="record.id" class="history-item">
                                    <div class="history-basic-info">
                                        <span class="history-date">{{ formatDate(record.migrationStartTime) }}</span>
                                        <span class="history-target">→ {{ record.targetTableName }}</span>
                                        <span class="history-fields-count">{{ record.fieldMappingRecords?.length || 0 }} 个字段</span>
                                    </div>
                                    <div class="field-diff-summary">
                                        <span class="diff-label">字段差异:</span>
                                        <span class="diff-details">{{ getFieldDifferenceSummary(record) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 有匹配的历史记录 -->
                        <div v-else-if="hasMatchingHistory" class="history-match">
                            <div class="history-match-icon">✅</div>
                            <h4>找到匹配的历史迁移记录</h4>
                            <p>找到字段结构完全匹配的历史记录，可以直接复用DDL语句。</p>
                            
                            <!-- 匹配的记录详情 -->
                            <div class="matched-record">
                                <div class="record-header">
                                    <h5>匹配记录详情:</h5>
                                    <div class="record-meta">
                                        <span>迁移时间: {{ formatDate(matchedRecord.migrationStartTime) }}</span>
                                        <span>目标表: {{ matchedRecord.targetTableName }}</span>
                                        <span>字段数: {{ matchedRecord.fieldMappingRecords?.length || 0 }}</span>
                                    </div>
                                </div>
                                
                                <!-- 字段对比表格 -->
                                <div class="field-comparison">
                                    <h6>字段结构对比:</h6>
                                    <table class="comparison-table">
                                        <thead>
                                            <tr>
                                                <th>源字段名</th>
                                                <th>源字段类型</th>
                                                <th>目标字段名</th>
                                                <th>目标字段类型</th>
                                                <th>字段描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="field in getAdjustedFieldMappings(matchedRecord.fieldMappingRecords)" 
                                                :key="field.id"
                                                :class="{ 'system-field-row': field.isSystemField, 'primary-mapping-row': field.sourceFieldName === 'id' && field.targetFieldName === 'business_id' }">
                                                <td :class="{ 'empty-field': !field.sourceFieldName }">
                                                    {{ field.sourceFieldName || '—' }}
                                                </td>
                                                <td :class="{ 'empty-field': !field.sourceFieldType }">
                                                    <span v-if="field.sourceFieldType" class="type-tag">{{ field.sourceFieldType }}</span>
                                                    <span v-else class="empty-type">—</span>
                                                </td>
                                                <td :class="{ 'target-system-field': field.isSystemField }">
                                                    {{ field.targetFieldName }}
                                                    <span v-if="field.isSystemField" class="system-field-label">系统字段</span>
                                                </td>
                                                <td>
                                                    <span class="type-tag" :class="{ 'system-type': field.isSystemField }">{{ field.targetFieldType }}</span>
                                                </td>
                                                <td>{{ field.targetFieldComment || '-' }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 操作选择 -->
                                <div class="history-actions">
                                    <div class="action-option">
                                        <label class="radio-label">
                                            <input type="radio" v-model="useHistoryRecord" :value="true" name="historyChoice">
                                            <span class="radio-text">
                                                <strong>使用历史记录</strong>
                                                <small>直接使用历史DDL，跳过AI优化步骤</small>
                                            </span>
                                        </label>
                                    </div>
                                    <div class="action-option">
                                        <label class="radio-label">
                                            <input type="radio" v-model="useHistoryRecord" :value="false" name="historyChoice">
                                            <span class="radio-text">
                                                <strong>重新AI优化</strong>
                                                <small>忽略历史记录，使用AI重新优化表名和字段名</small>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤5: AI表名和字段名优化 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 5 }">
                        <h3>AI智能优化表名和字段名</h3>
                        <p style="color: #666; margin-bottom: 8px;">使用AI智能优化表名和字段名，支持单个字段优化或一键批量优化</p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                            >
                                上一步
                            </button>
                            <button 
                                class="btn btn-primary" 
                                @click="nextStep"
                                :disabled="!canProceed"
                            >
                                下一步
                            </button>
                        </div>
                        
                        <!-- 操作按钮区域 -->
                        <div class="optimization-toolbar">
                            <div class="table-name-section">
                                <label>表名:</label>
                                <input 
                                    v-model="optimizedTableName" 
                                    type="text" 
                                    class="table-name-input"
                                    :placeholder="selectedTable"
                                />
                                <input 
                                    v-model="tableDescription" 
                                    type="text" 
                                    class="table-description-input"
                                    placeholder="请输入表的中文功能描述，如：用户信息表、订单记录表等"
                                />
                                <button 
                                    class="btn btn-sm btn-ai" 
                                    @click="optimizeTableNameOnly" 
                                    :disabled="loadingTableNameOptimization"
                                >
                                    <span v-if="loadingTableNameOptimization">优化中...</span>
                                    <span v-else>🤖 AI优化表名</span>
                                </button>
                        </div>

                            <div class="batch-operations">
                                <button 
                                    class="btn btn-primary btn-ai-batch" 
                                    @click="optimizeAllFields" 
                                    :disabled="loadingBatchOptimization"
                                >
                                    <span v-if="loadingBatchOptimization">批量优化中...</span>
                                    <span v-else>🤖 一键AI优化所有字段</span>
                                </button>
                                </div>
                            </div>

                        <!-- 字段映射表格 -->
                        <div class="field-mapping-table">
                            <table class="mapping-table">
                                <thead>
                                    <tr>
                                        <th width="14%">原始字段名</th>
                                        <th width="10%">字段类型</th>
                                        <th width="20%">优化后字段名</th>
                                        <th width="24%">字段描述(Comment)</th>
                                        <th width="8%">AI状态</th>
                                        <th width="24%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(field, index) in fieldsForOptimization" :key="field.name" class="field-row">
                                        <td class="original-field">
                                            <span class="field-name">{{ field.name }}</span>
                                        </td>
                                        <td class="field-type">
                                            <span class="type-tag">{{ field.type }}</span>
                                        </td>
                                        <td class="optimized-field">
                                    <input 
                                                v-model="field.optimizedName" 
                                                type="text" 
                                                class="field-input"
                                                :class="{ 'optimized': field.optimizedName !== field.name }"
                                                :placeholder="field.name"
                                            />
                                        </td>
                                        <td class="field-comment">
                                        <input 
                                                v-model="field.comment" 
                                                type="text" 
                                                class="comment-input"
                                                placeholder="字段描述..."
                                            />
                                        </td>
                                        <td class="ai-status">
                                            <div class="status-indicators">
                                                <span v-if="field.optimizedName !== field.name" class="status-check field-optimized" title="字段名已优化">✓</span>
                                                <span v-if="field.comment && field.comment.trim()" class="status-check comment-generated" title="描述已生成">💬</span>
                                    </div>
                                        </td>
                                        <td class="field-actions">
                                            <button 
                                                class="btn btn-sm btn-ai" 
                                                @click="optimizeSingleField(index)" 
                                                :disabled="field.optimizing || !field.comment || !field.comment.trim()"
                                                :title="!field.comment || !field.comment.trim() ? '请先生成字段描述' : 'AI优化字段名'"
                                            >
                                                <span v-if="field.optimizing">优化中</span>
                                                <span v-else>🤖 字段</span>
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-ai-comment" 
                                                @click="generateSingleComment(index)" 
                                                :disabled="field.commentGenerating"
                                                title="AI生成描述"
                                            >
                                                <span v-if="field.commentGenerating">生成中</span>
                                                <span v-else>💬 描述</span>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 优化统计信息 -->
                        <div class="optimization-stats" v-if="optimizedFieldsCount > 0">
                            <p>
                                <span class="stats-item">
                                    <strong>优化字段:</strong> {{ optimizedFieldsCount }} / {{ fieldsForOptimization.length }}
                                </span>
                                <span class="stats-item">
                                    <strong>优化率:</strong> {{ Math.round(optimizedFieldsCount / fieldsForOptimization.length * 100) }}%
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- 步骤6: 确认数据迁移 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 6 }">
                        <h3>确认迁移配置</h3>
                        <p style="color: #666; margin-bottom: 15px;">请确认以下迁移配置信息</p>
                        
                        <!-- 上方操作区域 -->
                        <div class="step-actions">
                            <button 
                                class="btn btn-secondary" 
                                @click="previousStep"
                                :disabled="currentStep === 1"
                            >
                                上一步
                            </button>
                            <button
                                class="btn btn-primary migration-btn"
                                @click="nextStep"
                                :disabled="!canProceed || migrating"
                                :class="{ 'migrating': migrating }"
                                style="position: relative;"
                            >
                                <!-- 迁移进度效果 -->
                                <div v-if="migrating && currentStep === 6" class="migration-progress">
                                    <!-- 进度条背景 -->
                                    <div class="progress-bar-bg" :style="{ width: migrationProgress + '%' }"></div>

                                    <!-- 进度内容 -->
                                    <div class="progress-content">
                                        <span class="progress-percentage">{{ migrationProgress }}%</span>
                                        <span class="progress-status">{{ migrationStatus }}</span>

                                        <!-- 大数据量迁移详细信息 -->
                                        <div v-if="migrationStats && migrationStats.isLargeData" class="large-data-info">
                                            <div class="large-data-badge">
                                                <i class="fas fa-rocket"></i>
                                                <span>大数据量优化模式</span>
                                            </div>
                                            <div class="migration-details">
                                                <span v-if="migrationStats && migrationStats.totalRows">
                                                    {{ (migrationStats.processedRows || 0).toLocaleString() }} / {{ migrationStats.totalRows.toLocaleString() }} 条
                                                </span>
                                                <span v-if="migrationStats && migrationStats.speed">
                                                    {{ migrationStats.speed.toLocaleString() }} 条/秒
                                                </span>
                                                <span v-if="migrationStats && migrationStats.eta">
                                                    剩余 {{ migrationStats.eta }}
                                                </span>
                                                <span v-if="migrationStats && migrationStats.threadCount">
                                                    {{ migrationStats.threadCount }} 线程
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 警告提示 -->
                                    <div class="migration-warning">
                                        <span v-if="migrationStats && migrationStats.isLargeData">
                                            🚀 大数据量迁移中，请耐心等待...
                                        </span>
                                        <span v-else>
                                            ⚠️ 请稍等，不要操作网页，等待迁移成功
                                        </span>
                                    </div>
                                </div>

                                <!-- 正常按钮文字 -->
                                <span v-else>
                                    {{ currentStep === 6 ? '开始迁移' : '下一步' }}
                                </span>
                            </button>
                        </div>
                        
                        <div class="summary-card">
                            <div class="summary-title">迁移概要</div>
                            <div class="summary-item">
                                <span>源数据库:</span>
                                <strong>{{ selectedSourceDb }} (PostgreSQL)</strong>
                            </div>
                            <div class="summary-item">
                                <span>源表名:</span>
                                <strong>{{ selectedTable }}</strong>
                            </div>
                            <div class="summary-item">
                                <span>目标数据库:</span>
                                <strong>{{ selectedTargetDb }} ({{ selectedTargetDbType === 'MYSQL' ? 'MySQL' : 'PostgreSQL' }})</strong>
                            </div>
                            <div class="summary-item">
                                <span>目标表名:</span>
                                <strong>{{ optimizedTableName }}</strong>
                            </div>
                            <div class="summary-item">
                                <span>迁移类型:</span>
                                <strong>{{ selectedTargetDbType === 'MYSQL' ? '跨数据库迁移 (PostgreSQL→MySQL)' : '同数据库迁移' }}</strong>
                            </div>
                            <div class="summary-item" v-if="selectedTargetDbType === 'MYSQL'">
                                <span>双主键设计:</span>
                                <strong>启用 (新增id主键+business_id业务键)</strong>
                            </div>
                            <div class="summary-item">
                                <span>字段数量:</span>
                                <strong>{{ optimizedFields.length }}{{ selectedTargetDbType === 'MYSQL' ? ' (+1新主键)' : '' }}</strong>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-title">迁移选项</div>
                            <div style="margin: 15px 0;">
                                <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <input type="checkbox" v-model="copyData" style="margin-right: 8px;">
                                    同时复制表数据
                                </label>
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" v-model="dropIfExists" style="margin-right: 8px;">
                                    如果目标表已存在则覆盖
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤7: 迁移结果 -->
                    <div class="step-panel" :class="{ 'active': currentStep === 7 }">
                        <div v-if="migrating" class="loading">
                            <div class="loading-spinner"></div>
                            <p>正在执行数据迁移...</p>
                            <p style="color: #666; font-size: 14px;">请耐心等待，不要关闭页面</p>
                        </div>

                        <div v-else class="result-panel">
                            <div class="result-icon">
                                <span v-if="migrationResult.success">✓</span>
                                <span v-else>✗</span>
                            </div>
                            <div class="result-title">
                                {{ migrationResult.success ? '迁移成功！' : '迁移失败' }}
                            </div>
                            <div class="result-details">
                                {{ migrationResult.message }}
                            </div>
                            
                            <div v-if="migrationResult.success" class="summary-card" style="text-align: left; max-width: 500px; margin: 0 auto;">
                                <div class="summary-title">迁移详情</div>
                                <div class="summary-item">
                                    <span>源表:</span>
                                    <span>{{ migrationResult.data?.sourceDatabase }}.{{ migrationResult.data?.sourceTable }}</span>
                                </div>
                                <div class="summary-item">
                                    <span>目标表:</span>
                                    <span>{{ migrationResult.data?.targetDatabase }}.{{ migrationResult.data?.targetTable }}</span>
                                </div>
                                <div class="summary-item">
                                    <span>数据复制:</span>
                                    <span>{{ migrationResult.data?.copyData ? '是' : '否' }}</span>
                                </div>
                            </div>

                            <div style="margin-top: 30px;">
                                <button class="btn btn-primary" @click="startNewMigration">
                                    开始新的迁移
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮已移至各步骤顶部 -->
            </div>

            <!-- 系统运行日志区域 -->
            <div class="log-container">
                <div class="log-header">
                    <div class="log-title">
                        <span class="log-icon">📊</span>
                        <span>系统运行日志</span>
                        <span class="log-status" :class="{ 'connected': logConnected, 'disconnected': !logConnected }">
                            {{ logConnected ? '已连接' : '未连接' }}
                        </span>
                    </div>
                    <div class="log-controls">
                        <button class="log-btn" @click="clearLogs" title="清空日志">
                            🗑️ 清空
                        </button>
                        <button class="log-btn" @click="toggleAutoScroll" :class="{ 'active': autoScroll }" title="自动滚动">
                            {{ autoScroll ? '📍' : '📌' }} 自动滚动
                        </button>
                        <button class="log-btn" @click="toggleLogPanel" title="折叠/展开">
                            {{ logPanelExpanded ? '🔼' : '🔽' }}
                        </button>
                    </div>
                </div>
                <div class="log-content" v-show="logPanelExpanded" ref="logContent">
                    <div class="log-messages">
                        <div
                            v-for="(log, index) in logMessages"
                            :key="index"
                            class="log-message"
                            :class="getLogClass(log)"
                        >
                            {{ log }}
                        </div>
                        <div v-if="logMessages.length === 0" class="log-empty">
                            <span class="log-empty-icon">📝</span>
                            <span>等待系统日志...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载数据迁移页面...');

                cdnLoader.updateProgress(15);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(30);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(50);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
            }
        }

        function initVueApp() {
            const { createApp, ref, computed, onMounted } = Vue;
            const { ElMessage } = ElementPlus;

        // 全局WebSocket管理器 - 避免重复连接
        class LogWebSocketManager {
            constructor() {
                this.socket = null;
                this.connected = false;
                this.messageHandlers = new Set();
                this.statusHandlers = new Set();
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectDelay = 5000;
            }

            connect() {
                if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                    console.log('📱 WebSocket已连接，跳过重复连接');
                    return;
                }

                try {
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const wsUrl = `${protocol}//${window.location.host}/ws/logs`;

                    this.socket = new WebSocket(wsUrl);

                    this.socket.onopen = () => {
                        this.connected = true;
                        this.reconnectAttempts = 0;
                        console.log('📱 日志WebSocket连接已建立');
                        this.notifyStatusHandlers(true);
                    };

                    this.socket.onmessage = (event) => {
                        const message = event.data.trim();
                        if (message) {
                            this.notifyMessageHandlers(message);
                        }
                    };

                    this.socket.onclose = () => {
                        this.connected = false;
                        console.log('📱 日志WebSocket连接已关闭');
                        this.notifyStatusHandlers(false);

                        // 自动重连
                        if (this.reconnectAttempts < this.maxReconnectAttempts) {
                            this.reconnectAttempts++;
                            setTimeout(() => {
                                console.log(`📱 尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                                this.connect();
                            }, this.reconnectDelay);
                        }
                    };

                    this.socket.onerror = (error) => {
                        console.error('📱 日志WebSocket连接错误:', error);
                        this.connected = false;
                        this.notifyStatusHandlers(false);
                    };

                } catch (error) {
                    console.error('📱 初始化日志WebSocket失败:', error);
                }
            }

            addMessageHandler(handler) {
                this.messageHandlers.add(handler);
            }

            removeMessageHandler(handler) {
                this.messageHandlers.delete(handler);
            }

            addStatusHandler(handler) {
                this.statusHandlers.add(handler);
            }

            removeStatusHandler(handler) {
                this.statusHandlers.delete(handler);
            }

            notifyMessageHandlers(message) {
                this.messageHandlers.forEach(handler => {
                    try {
                        handler(message);
                    } catch (error) {
                        console.error('消息处理器错误:', error);
                    }
                });
            }

            notifyStatusHandlers(connected) {
                this.statusHandlers.forEach(handler => {
                    try {
                        handler(connected);
                    } catch (error) {
                        console.error('状态处理器错误:', error);
                    }
                });
            }

            disconnect() {
                if (this.socket) {
                    this.socket.close();
                    this.socket = null;
                }
                this.connected = false;
            }

            isConnected() {
                return this.connected && this.socket && this.socket.readyState === WebSocket.OPEN;
            }
        }

        // 全局WebSocket实例
        const globalLogWebSocket = new LogWebSocketManager();
        
        createApp({
            setup() {
                // 响应式数据
                const currentStep = ref(1);
                const loadingDatabases = ref(false);
                const loadingTables = ref(false);
                const loadingOptimization = ref(false);
                const migrating = ref(false);
                const migrationProgress = ref(0);
                const migrationStatus = ref('');
                const errorMessage = ref('');

                // 大数据量迁移统计信息
                const migrationStats = ref({
                    isLargeData: false,
                    totalRows: 0,
                    processedRows: 0,
                    speed: 0,
                    eta: '',
                    threadCount: 1,
                    startTime: null
                });

                // 日志相关状态
                const logMessages = ref([]);
                const logConnected = ref(false);
                const logPanelExpanded = ref(true);
                const autoScroll = ref(true);
                const logContent = ref(null);
                
                const databases = ref([]);
                const tables = ref([]);
                const originalFields = ref([]);
                const optimizedFields = ref([]);
                const fieldsForOptimization = ref([]);
                
                const selectedSourceDb = ref('');
                const selectedTargetDb = ref('');
                const selectedTargetDbType = ref('POSTGRESQL');
                const selectedTable = ref('');
                const optimizedTableName = ref('');
                const tableDescription = ref('');
                
                // 新增的加载状态
                const loadingTableNameOptimization = ref(false);
                const loadingBatchOptimization = ref(false);
                
                const copyData = ref(true);
                const dropIfExists = ref(false);
                
                const migrationResult = ref({
                    success: false,
                    message: '',
                    data: null
                });

                // 历史记录相关数据
                const loadingHistory = ref(false);
                const historyRecords = ref([]);
                const hasHistoryRecords = computed(() => historyRecords.value.length > 0);
                const hasMatchingHistory = ref(false);
                const matchedRecord = ref(null);
                const useHistoryRecord = ref(false);

                // 步骤配置
                const steps = ref([
                    { title: '选取源数据库', key: 'source-db' },
                    { title: '选择源数据库表', key: 'source-table' },
                    { title: '选择目标数据库', key: 'target-db' },
                    { title: '检查迁移历史记录', key: 'check-history' },
                    { title: 'AI优化表名和字段名', key: 'ai-optimize' },
                    { title: '确认数据迁移', key: 'confirm' },
                    { title: '迁移结果', key: 'result' }
                ]);

                // 计算属性
                const availableSourceDatabases = computed(() => {
                    // 只显示PostgreSQL类型的数据库作为源数据库
                    return databases.value.filter(db => 
                        db.type === 'POSTGRESQL'
                    );
                });

                const availableTargetDatabases = computed(() => {
                    // 只显示MySQL类型的数据库作为目标数据库
                    return databases.value.filter(db => 
                        db.type === 'MYSQL' && db.name !== selectedSourceDb.value
                    );
                });

                const optimizedFieldsCount = computed(() => {
                    return fieldsForOptimization.value.filter(field => 
                        field.optimizedName !== field.name
                    ).length;
                });

                const canProceed = computed(() => {
                    switch (currentStep.value) {
                        case 1:
                            return selectedSourceDb.value !== '';
                        case 2:
                            return selectedTable.value !== '';
                        case 3:
                            return selectedTargetDb.value !== '';
                        case 4:
                            return true; // 历史记录检查步骤可以继续
                        case 5:
                            return optimizedTableName.value !== '' && fieldsForOptimization.value.length > 0;
                        case 6:
                            return true;
                        default:
                            return false;
                    }
                });

                // 数据库配置
                const databaseConfig = ref(null);
                
                // 方法
                const loadDatabaseConfig = () => {
                    const configStr = localStorage.getItem('databaseConfig');
                    if (configStr) {
                        databaseConfig.value = JSON.parse(configStr);
                        // 如果有具体的数据库选择，则设置默认值（兼容旧版本）
                        if (databaseConfig.value.selectedSourceDb) {
                            selectedSourceDb.value = databaseConfig.value.selectedSourceDb;
                        }
                        if (databaseConfig.value.selectedTargetDb) {
                            selectedTargetDb.value = databaseConfig.value.selectedTargetDb;
                        }
                        if (databaseConfig.value.targetConfig) {
                            selectedTargetDbType.value = databaseConfig.value.targetConfig.databaseType;
                        }
                        return true;
                    }
                    return false;
                };

                const loadDatabases = async () => {
                    loadingDatabases.value = true;
                    errorMessage.value = '';
                    
                    try {
                        // 检查数据库配置
                        if (!databaseConfig.value) {
                            throw new Error('数据库配置未找到，请先进行配置');
                        }

                        // 加载源数据库列表
                        const sourceResponse = await axios.post('/api/db-config/databases', databaseConfig.value.sourceConfig);
                        let sourceDatabases = [];
                        if (sourceResponse.data.success) {
                            sourceDatabases = sourceResponse.data.data.map(db => ({
                                name: db.name,
                                type: 'POSTGRESQL',
                                connected: true,
                                description: db.description || 'PostgreSQL数据库'
                            }));
                        }

                        // 加载目标数据库列表  
                        const targetResponse = await axios.post('/api/db-config/databases', databaseConfig.value.targetConfig);
                        let targetDatabases = [];
                        if (targetResponse.data.success) {
                            targetDatabases = targetResponse.data.data.map(db => ({
                                name: db.name,
                                type: databaseConfig.value.targetConfig.databaseType,
                                connected: true,
                                description: db.description || 'MySQL数据库'
                            }));
                        }

                        // 合并数据库列表
                        databases.value = [...sourceDatabases, ...targetDatabases];
                        
                    } catch (error) {
                        let rawErrorMsg = '加载数据库列表失败';
                        if (error.response && error.response.data && error.response.data.message) {
                            rawErrorMsg = error.response.data.message;
                        } else if (error.message) {
                            rawErrorMsg = error.message;
                        }

                        // 转换为用户友好的错误信息
                        errorMessage.value = translateErrorMessage(rawErrorMsg);
                        console.error('Error loading databases:', error);
                        
                        // 如果配置有问题，重定向到配置页面
                        if (error.message.includes('配置未找到')) {
                            window.location.href = '/database-config.html';
                        }
                    } finally {
                        loadingDatabases.value = false;
                    }
                };

                const selectSourceDatabase = (dbName) => {
                    selectedSourceDb.value = dbName;
                    tables.value = []; // 清空表列表
                    selectedTable.value = '';
                };

                const selectTargetDatabase = (dbName, dbType = 'MYSQL') => {
                    selectedTargetDb.value = dbName;
                    selectedTargetDbType.value = dbType;
                };

                const loadTables = async () => {
                    if (!selectedSourceDb.value || !databaseConfig.value) return;
                    
                    loadingTables.value = true;
                    try {
                        // 使用动态数据库服务获取表列表
                        const response = await axios.post(`/api/db-config/tables?databaseName=${selectedSourceDb.value}`, 
                            databaseConfig.value.sourceConfig);
                        if (response.data.success) {
                            // 新API返回字符串数组，需要转换为对象数组
                            tables.value = response.data.data.map(tableName => ({
                                tableName: tableName,
                                tableType: 'BASE TABLE',
                                rowCount: '未知',
                                comment: '可迁移'
                            }));
                        } else {
                            errorMessage.value = response.data.message;
                        }
                    } catch (error) {
                        let rawErrorMsg = '加载表列表失败';
                        if (error.response && error.response.data && error.response.data.message) {
                            rawErrorMsg = error.response.data.message;
                        } else if (error.message) {
                            rawErrorMsg = error.message;
                        }

                        // 转换为用户友好的错误信息
                        errorMessage.value = translateErrorMessage(rawErrorMsg);
                        console.error('Error loading tables:', error);
                    } finally {
                        loadingTables.value = false;
                    }
                };

                const selectTable = async (tableName) => {
                    if (!tableName) {
                        console.error('表名为空，无法选择表');
                        return;
                    }
                    
                    selectedTable.value = tableName;
                    optimizedTableName.value = tableName; // 初始化表名
                    console.log('选择表:', tableName, '数据库:', selectedSourceDb.value);
                    
                    // 获取表结构
                    try {
                        // 继续使用原有的API，因为这个API应该能正常工作
                        const response = await axios.get(`/api/tables/${selectedSourceDb.value}/${tableName}/structure`);
                        if (response.data.success) {
                            originalFields.value = response.data.data.columns.map(col => ({
                                name: col.columnName,
                                type: col.dataType,
                                nullable: col.nullable,
                                comment: col.comment
                            }));
                            
                            // 初始化字段优化数据
                            fieldsForOptimization.value = originalFields.value.map(field => ({
                                name: field.name,
                                type: field.type,
                                nullable: field.nullable,
                                comment: field.comment || '',
                                optimizedName: field.name, // 初始时使用原字段名
                                optimizing: false,
                                commentGenerating: false
                            }));
                            
                            console.log('获取表结构成功，字段数:', originalFields.value.length);
                        }
                    } catch (error) {
                        console.error('Error loading table structure:', error);
                        errorMessage.value = '获取表结构失败: ' + error.message;
                    }
                };

                // 历史记录相关方法
                const loadMigrationHistory = async () => {
                    if (!selectedTable.value) return;
                    
                    loadingHistory.value = true;
                    try {
                        console.log(`🔍 开始查询表 "${selectedTable.value}" 的历史迁移记录...`);
                        console.log('📋 使用新的查询逻辑：基于迁移记录中的source_table_name字段进行匹配');
                        
                        const response = await axios.get(`/api/migration-history/table-history?sourceTableName=${encodeURIComponent(selectedTable.value)}`);
                        if (response.data.success) {
                            historyRecords.value = response.data.data || [];
                            console.log(`✅ 查询到 ${historyRecords.value.length} 条历史记录`);
                            
                            if (historyRecords.value.length > 0) {
                                console.log('📊 历史记录详情：', historyRecords.value.map(record => ({
                                    sourceTable: record.sourceTableName,
                                    targetTable: record.targetTableName,
                                    migrationTime: record.migrationStartTime,
                                    fieldCount: record.fieldMappingRecords?.length || 0
                                })));
                            }
                            
                            // 检查是否有匹配的历史记录
                            const foundRecord = findMatchingRecord();
                            if (foundRecord) {
                                hasMatchingHistory.value = true;
                                matchedRecord.value = foundRecord;
                                useHistoryRecord.value = true; // 默认选择使用历史记录
                                console.log('🎯 找到字段结构完全匹配的历史记录：', {
                                    targetTable: foundRecord.targetTableName,
                                    migrationTime: foundRecord.migrationStartTime,
                                    fieldCount: foundRecord.fieldMappingRecords?.length
                                });
                            } else {
                                hasMatchingHistory.value = false;
                                matchedRecord.value = null;
                                useHistoryRecord.value = false;
                                if (historyRecords.value.length > 0) {
                                    console.log('⚠️ 找到历史记录但字段结构不匹配');
                                } else {
                                    console.log('ℹ️ 没有找到历史迁移记录');
                                }
                            }
                        } else {
                            historyRecords.value = [];
                            hasMatchingHistory.value = false;
                            console.log('❌ 查询历史记录失败：', response.data.message);
                        }
                    } catch (error) {
                        console.error('💥 加载迁移历史失败:', error);
                        historyRecords.value = [];
                        hasMatchingHistory.value = false;
                        errorMessage.value = '查询历史记录失败: ' + error.message;
                    } finally {
                        loadingHistory.value = false;
                    }
                };

                const findMatchingRecord = () => {
                    if (!historyRecords.value.length || !originalFields.value.length) return null;
                    
                    console.log('🔍 开始字段结构匹配分析...');
                    console.log('📝 当前表字段结构：', originalFields.value.map(f => `${f.name}:${f.type}`));
                    
                    for (const record of historyRecords.value) {
                        if (!record.fieldMappingRecords || !record.fieldMappingRecords.length) {
                            console.log(`⏭️ 跳过记录 ${record.targetTableName}：无字段映射数据`);
                            continue;
                        }
                        
                        console.log(`🔎 分析历史记录: ${record.sourceTableName} -> ${record.targetTableName}`);
                        
                        // 检查历史记录是否为双主键设计
                        const hasSystemIdField = record.fieldMappingRecords.some(f => 
                            f.sourceFieldName === '__SYSTEM_GENERATED__' && 
                            f.targetFieldName === 'id' && 
                            f.targetFieldIsPrimary
                        );
                        const hasBusinessIdField = record.fieldMappingRecords.some(f => 
                            f.targetFieldName === 'business_id'
                        );
                        const isDualPrimaryKey = hasSystemIdField && hasBusinessIdField;
                        
                        console.log(`🔍 双主键设计检测: 系统ID=${hasSystemIdField}, 业务ID=${hasBusinessIdField}, 双主键=${isDualPrimaryKey}`);
                        
                        // 比较字段结构 - 如果是双主键设计，忽略系统生成的id字段
                        const currentFieldsSet = new Set(originalFields.value.map(f => `${f.name}:${f.type}`));
                        
                        // 过滤历史记录中的字段，如果是双主键设计则忽略系统生成的id字段
                        const effectiveHistoryFields = isDualPrimaryKey ? 
                            record.fieldMappingRecords.filter(f => 
                                !(f.sourceFieldName === '__SYSTEM_GENERATED__' && f.targetFieldName === 'id')
                            ) : 
                            record.fieldMappingRecords;
                            
                        const historyFieldsSet = new Set(effectiveHistoryFields.map(f => `${f.sourceFieldName}:${f.sourceFieldType}`));
                        
                        console.log(`📊 历史记录字段结构（${isDualPrimaryKey ? '已过滤系统ID' : '原始'}）：`, Array.from(historyFieldsSet));
                        console.log(`📏 字段数量对比: 当前${currentFieldsSet.size} vs 历史${historyFieldsSet.size}`);
                        
                        // 检查字段是否完全匹配
                        if (currentFieldsSet.size === historyFieldsSet.size) {
                            let isMatch = true;
                            const missingFields = [];
                            const extraFields = [];
                            
                            for (const fieldKey of currentFieldsSet) {
                                if (!historyFieldsSet.has(fieldKey)) {
                                    isMatch = false;
                                    missingFields.push(fieldKey);
                                }
                            }
                            
                            for (const fieldKey of historyFieldsSet) {
                                if (!currentFieldsSet.has(fieldKey)) {
                                    extraFields.push(fieldKey);
                                }
                            }
                            
                            if (isMatch) {
                                console.log(`✅ 找到完全匹配的记录: ${record.sourceTableName} -> ${record.targetTableName}`);
                                console.log(`🎯 匹配详情: ${currentFieldsSet.size} 个字段完全一致${isDualPrimaryKey ? '（忽略系统ID字段）' : ''}`);
                                return record;
                            } else {
                                console.log(`❌ 字段结构不匹配: ${record.sourceTableName} -> ${record.targetTableName}`);
                                if (missingFields.length > 0) {
                                    console.log(`🔴 历史记录缺少字段: ${missingFields.join(', ')}`);
                                }
                                if (extraFields.length > 0) {
                                    console.log(`🟡 历史记录多出字段: ${extraFields.join(', ')}`);
                                }
                            }
                        } else {
                            console.log(`📏 字段数量不匹配: 当前${currentFieldsSet.size}个 vs 历史${historyFieldsSet.size}个`);
                        }
                    }
                    
                    console.log('🚫 未找到字段结构完全匹配的历史记录');
                    return null;
                };

                const applyHistoryRecord = async () => {
                    if (!matchedRecord.value) return;
                    
                    try {
                        // 应用历史记录的表名和字段映射
                        optimizedTableName.value = matchedRecord.value.targetTableName;
                        
                        // 构建字段优化数据
                        fieldsForOptimization.value = originalFields.value.map(field => {
                            const historyField = matchedRecord.value.fieldMappingRecords.find(
                                hf => hf.sourceFieldName === field.name && hf.sourceFieldType === field.type
                            );
                            
                            return {
                                name: field.name,
                                type: field.type,
                                nullable: field.nullable,
                                comment: historyField ? historyField.targetFieldComment || '' : '',
                                optimizedName: historyField ? historyField.targetFieldName : field.name,
                                optimizing: false,
                                commentGenerating: false
                            };
                        });
                        
                        ElMessage.success('已应用历史迁移记录');
                    } catch (error) {
                        console.error('应用历史记录失败:', error);
                        ElMessage.error('应用历史记录失败: ' + error.message);
                    }
                };

                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN');
                };

                // 错误信息转换函数 - 将技术错误转换为用户友好的描述
                const translateErrorMessage = (errorMessage) => {
                    if (!errorMessage) return '迁移失败，请重试';

                    // 表已存在错误
                    if (errorMessage.includes('already exists') || errorMessage.includes('表已存在')) {
                        return '数据库表已经存在，如果需要成功迁移请勾选"覆盖现有表"选项';
                    }

                    // 连接失败错误
                    if (errorMessage.includes('连接失败') || errorMessage.includes('Connection refused') ||
                        errorMessage.includes('timeout') || errorMessage.includes('连接超时')) {
                        return '数据库连接失败，请检查数据库服务是否正常运行，或联系管理员确认网络连接';
                    }

                    // 权限错误
                    if (errorMessage.includes('Access denied') || errorMessage.includes('权限') ||
                        errorMessage.includes('Permission denied') || errorMessage.includes('认证失败')) {
                        return '数据库访问权限不足，请检查用户名和密码是否正确，或联系管理员分配相应权限';
                    }

                    // 数据库不存在
                    if (errorMessage.includes('database') && errorMessage.includes('不存在')) {
                        return '目标数据库不存在，请先创建数据库或选择正确的数据库名称';
                    }

                    // 字段类型错误
                    if (errorMessage.includes('字段类型') || errorMessage.includes('数据类型') ||
                        errorMessage.includes('type') && errorMessage.includes('convert')) {
                        return '数据类型转换失败，请检查源表和目标表的字段类型是否兼容';
                    }

                    // 数据过长错误
                    if (errorMessage.includes('Data too long') || errorMessage.includes('数据过长') ||
                        errorMessage.includes('字符串长度超出')) {
                        return '数据长度超出字段限制，建议增加目标字段长度或清理过长的数据';
                    }

                    // 主键冲突
                    if (errorMessage.includes('Duplicate entry') || errorMessage.includes('主键冲突') ||
                        errorMessage.includes('PRIMARY KEY')) {
                        return '主键冲突，目标表中已存在相同主键的数据，请清理冲突数据或使用不同的主键策略';
                    }

                    // 外键约束
                    if (errorMessage.includes('foreign key') || errorMessage.includes('外键约束')) {
                        return '外键约束冲突，请确保相关联的表数据完整，或暂时禁用外键检查';
                    }

                    // 磁盘空间不足
                    if (errorMessage.includes('disk') && errorMessage.includes('space') ||
                        errorMessage.includes('磁盘空间')) {
                        return '磁盘空间不足，请清理磁盘空间后重试，或联系管理员扩容';
                    }

                    // 网络错误
                    if (errorMessage.includes('Network') || errorMessage.includes('网络') ||
                        errorMessage.includes('Request failed')) {
                        return '网络连接异常，请检查网络连接状态后重试';
                    }

                    // 如果没有匹配到特定错误，返回原始错误信息但加上友好提示
                    return `迁移过程中遇到问题：${errorMessage}。如需帮助请联系技术支持`;
                };

                const getFieldDifferenceSummary = (record) => {
                    if (!record.fieldMappingRecords || !originalFields.value.length) return '无法比较';
                    
                    // 检查历史记录是否为双主键设计
                    const hasSystemIdField = record.fieldMappingRecords.some(f => 
                        f.sourceFieldName === '__SYSTEM_GENERATED__' && 
                        f.targetFieldName === 'id' && 
                        f.targetFieldIsPrimary
                    );
                    const hasBusinessIdField = record.fieldMappingRecords.some(f => 
                        f.targetFieldName === 'business_id'
                    );
                    const isDualPrimaryKey = hasSystemIdField && hasBusinessIdField;
                    
                    const currentFields = originalFields.value.map(f => `${f.name}:${f.type}`);
                    
                    // 过滤历史记录中的字段，如果是双主键设计则忽略系统生成的id字段
                    const effectiveHistoryFields = isDualPrimaryKey ? 
                        record.fieldMappingRecords.filter(f => 
                            !(f.sourceFieldName === '__SYSTEM_GENERATED__' && f.targetFieldName === 'id')
                        ) : 
                        record.fieldMappingRecords;
                        
                    const historyFields = effectiveHistoryFields.map(f => `${f.sourceFieldName}:${f.sourceFieldType}`);
                    
                    const missingInHistory = currentFields.filter(f => !historyFields.includes(f));
                    const extraInHistory = historyFields.filter(f => !currentFields.includes(f));
                    
                    const differences = [];
                    if (missingInHistory.length) differences.push(`缺少${missingInHistory.length}个字段`);
                    if (extraInHistory.length) differences.push(`多出${extraInHistory.length}个字段`);
                    
                    return differences.length ? differences.join(', ') : '结构匹配';
                };

                // 调整字段映射显示，确保正确的字段对齐
                const getAdjustedFieldMappings = (fieldMappings) => {
                    if (!fieldMappings) return [];
                    
                    console.log('🔍 开始调整字段映射显示，当前表字段：', originalFields.value.map(f => f.name));
                    console.log('📊 历史迁移字段映射：', fieldMappings.map(f => `${f.sourceFieldName} -> ${f.targetFieldName}`));
                    
                    const adjustedMappings = [];
                    const currentFieldNames = originalFields.value.map(f => f.name);
                    
                    // 1. 首先添加历史目标表中的系统ID字段（雪花算法生成），当前表中没有对应字段
                    const systemIdField = fieldMappings.find(f => 
                        f.sourceFieldName === '__SYSTEM_GENERATED__' && 
                        f.targetFieldName === 'id' && 
                        f.targetFieldIsPrimary
                    );
                    if (systemIdField) {
                        adjustedMappings.push({
                            id: 'system_id_placeholder',
                            sourceFieldName: '—', // 当前表中没有对应字段，显示空白
                            sourceFieldType: '—',
                            targetFieldName: systemIdField.targetFieldName,
                            targetFieldType: systemIdField.targetFieldType,
                            targetFieldComment: '系统生成的主键(雪花算法)',
                            isSystemField: true
                        });
                        console.log('✅ 添加系统ID字段行：空白 -> id (BIGINT)');
                    }
                    
                    // 2. 处理当前表的字段与历史字段的对应关系
                    for (const currentField of originalFields.value) {
                        // 查找对应的历史字段映射
                        let matchedHistoryField = null;
                        
                        if (currentField.name === 'id') {
                            // 当前表的id字段应该对应历史表的business_id字段
                            matchedHistoryField = fieldMappings.find(f => 
                                f.targetFieldName === 'business_id' && 
                                f.sourceFieldName === 'id'
                            );
                            
                            if (matchedHistoryField) {
                                adjustedMappings.push({
                                    id: matchedHistoryField.id || `adjusted_${currentField.name}`,
                                    sourceFieldName: currentField.name,
                                    sourceFieldType: currentField.type,
                                    targetFieldName: 'business_id',
                                    targetFieldType: matchedHistoryField.targetFieldType || 'VARCHAR(64)',
                                    targetFieldComment: matchedHistoryField.targetFieldComment || '业务主键ID'
                                });
                                console.log(`✅ 主键字段对齐：${currentField.name} -> business_id`);
                            }
                        } else {
                            // 其他字段按源字段名进行匹配
                            matchedHistoryField = fieldMappings.find(f => 
                                f.sourceFieldName === currentField.name && 
                                f.targetFieldName !== 'business_id' && 
                                f.targetFieldName !== 'id'
                            );
                            
                            if (matchedHistoryField) {
                                adjustedMappings.push({
                                    id: matchedHistoryField.id,
                                    sourceFieldName: currentField.name,
                                    sourceFieldType: currentField.type,
                                    targetFieldName: matchedHistoryField.targetFieldName,
                                    targetFieldType: matchedHistoryField.targetFieldType,
                                    targetFieldComment: matchedHistoryField.targetFieldComment
                                });
                                console.log(`✅ 字段匹配：${currentField.name} -> ${matchedHistoryField.targetFieldName}`);
                            } else {
                                // 当前字段在历史记录中没有找到对应字段
                                adjustedMappings.push({
                                    id: `unmapped_${currentField.name}`,
                                    sourceFieldName: currentField.name,
                                    sourceFieldType: currentField.type,
                                    targetFieldName: '未映射',
                                    targetFieldType: '-',
                                    targetFieldComment: '在历史记录中未找到对应字段'
                                });
                                console.log(`⚠️ 未匹配字段：${currentField.name}`);
                            }
                        }
                    }
                    
                    console.log('🎯 最终调整后的字段映射：', adjustedMappings.map(f => `${f.sourceFieldName} -> ${f.targetFieldName}`));
                    return adjustedMappings;
                };

                const optimizeWithAI = async () => {
                    loadingOptimization.value = true;
                    try {
                        console.log('开始AI优化，原始字段：', originalFields.value);
                        
                        // 使用增强字段映射分析接口进行整体优化 - 直接格式
                        const response = await axios.post('/api/enhanced-field-mapping/analyze', {
                            tableName: selectedTable.value,
                            columns: originalFields.value.map(field => ({
                                name: field.name,
                                dataType: field.type,
                                comment: field.comment,
                                isNullable: field.nullable,
                                defaultValue: null,
                                isPrimaryKey: false
                            })),
                            primaryKeys: [],
                            businessContext: '数据迁移优化'
                        });
                        
                        console.log('AI分析响应：', response.data);

                        if (response.data.success) {
                            // 设置优化后的表名
                            optimizedTableName.value = response.data.optimizedTableName;
                            
                            // 设置优化后的字段
                            optimizedFields.value = response.data.fieldMappings.map(mapping => ({
                                name: mapping.targetField,
                                type: mapping.targetType || mapping.sourceType,
                                nullable: mapping.nullable,
                                comment: mapping.comment,
                                original: mapping.sourceField,
                                confidence: mapping.confidence,
                                optimizationNote: mapping.optimizationNote
                            }));
                        } else {
                            throw new Error(response.data.message || 'AI优化失败');
                        }
                    } catch (error) {
                        console.error('AI optimization error:', error);
                        
                        // 降级处理：使用简单的命名转换
                        try {
                            // 优化表名 - 使用测试接口作为降级
                            const tableNameResponse = await axios.post('/api/enhanced-field-mapping/test-table-name', {
                                tableName: selectedTable.value,
                                businessContext: '数据迁移'
                            });
                            
                            optimizedTableName.value = tableNameResponse.data.success ? 
                                tableNameResponse.data.optimizedTableName : selectedTable.value;
                            
                            // 优化字段名 - 使用AI字段命名接口
                            const fieldPromises = originalFields.value.map(async (field) => {
                                try {
                                    const response = await axios.post('/api/ai/field-naming/optimize-field', {
                                        originalFieldName: field.name,
                                        fieldType: field.type,
                                        comment: field.comment,
                                        businessContext: '数据迁移'
                                    });
                                    
                                    return {
                                        name: response.data.success ? response.data.optimizedName : field.name,
                                        type: field.type,
                                        nullable: field.nullable,
                                        comment: field.comment,
                                        original: field.name
                                    };
                                } catch (error) {
                                    // 如果AI接口不可用，使用简单的snake_case转换
                                    const simpleName = field.name.toLowerCase()
                                        .replace(/[^a-zA-Z0-9]+/g, '_')
                                        .replace(/^_+|_+$/g, '');
                                    
                                    return {
                                        name: simpleName || field.name,
                                        type: field.type,
                                        nullable: field.nullable,
                                        comment: field.comment,
                                        original: field.name
                                    };
                                }
                            });

                            optimizedFields.value = await Promise.all(fieldPromises);
                        } catch (fallbackError) {
                            console.error('Fallback optimization also failed:', fallbackError);
                            // 最后的降级处理：直接使用原字段
                            optimizedTableName.value = selectedTable.value;
                            optimizedFields.value = originalFields.value.map(field => ({
                                ...field,
                                original: field.name
                            }));
                        }
                    } finally {
                        loadingOptimization.value = false;
                    }
                };

                const reoptimizeFields = () => {
                    optimizeWithAI();
                };

                // 新增函数：优化单个字段
                const optimizeSingleField = async (index) => {
                    const field = fieldsForOptimization.value[index];
                    
                    // 验证字段描述是否存在
                    if (!field.comment || !field.comment.trim()) {
                        ElMessage.warning('请先生成字段描述，再进行字段名优化');
                        return;
                    }
                    
                    field.optimizing = true;
                    
                    try {
                        const response = await axios.post('/api/ai/field-naming/optimize-field', {
                            originalFieldName: field.name,
                            fieldType: field.type,
                            comment: field.comment,
                            businessContext: `根据字段描述"${field.comment}"优化字段名`
                        });
                        
                        if (response.data.success) {
                            field.optimizedName = response.data.optimizedName;
                            console.log(`字段优化成功: ${field.name} -> ${field.optimizedName} (基于描述: ${field.comment})`);
                        } else {
                            console.error('字段优化失败:', response.data.message);
                            ElMessage.error('字段优化失败: ' + response.data.message);
                        }
                    } catch (error) {
                        console.error('单个字段优化失败:', error);
                        ElMessage.error('字段优化失败，请重试');
                        // 降级处理：简单转换
                        const simpleName = field.name.toLowerCase()
                            .replace(/[^a-zA-Z0-9]+/g, '_')
                            .replace(/^_+|_+$/g, '');
                        field.optimizedName = simpleName || field.name;
                    } finally {
                        field.optimizing = false;
                    }
                };

                // 新增函数：生成单个字段描述
                const generateSingleComment = async (index) => {
                    const field = fieldsForOptimization.value[index];
                    field.commentGenerating = true;
                    
                    try {
                        const response = await axios.post('/api/ai/field-naming/generate-comment', {
                            fieldName: field.optimizedName || field.name,
                            fieldType: field.type,
                            originalComment: field.comment,
                            businessContext: selectedTable.value + '表的字段描述'
                        });
                        
                        if (response.data.success) {
                            field.comment = response.data.comment;
                        } else {
                            console.error('生成字段描述失败:', response.data.message);
                        }
                    } catch (error) {
                        console.error('生成单个字段描述失败:', error);
                        // 降级处理：简单描述
                        field.comment = `${field.name}字段`;
                    } finally {
                        field.commentGenerating = false;
                    }
                };

                // 新增函数：AI优化表名
                const optimizeTableNameOnly = async () => {
                    // 验证用户是否输入了中文描述
                    if (!tableDescription.value || tableDescription.value.trim() === '') {
                        ElMessage.warning('请先输入表的中文描述，用于生成对应的英文表名');
                        return;
                    }
                    
                    loadingTableNameOptimization.value = true;
                    
                    try {
                        // 使用用户输入的中文描述作为业务上下文
                        const businessContext = tableDescription.value.trim();
                        
                        const response = await axios.post('/api/enhanced-field-mapping/test-table-name', {
                            tableName: selectedTable.value,
                            businessContext: businessContext
                        });
                        
                        if (response.data.success) {
                            optimizedTableName.value = response.data.optimizedTableName;
                        } else {
                            console.error('表名优化失败:', response.data.message);
                        }
                    } catch (error) {
                        console.error('表名优化失败:', error);
                        // 降级处理：简单转换
                        const simpleName = selectedTable.value.toLowerCase()
                            .replace(/[^a-zA-Z0-9]+/g, '_')
                            .replace(/^_+|_+$/g, '');
                        optimizedTableName.value = simpleName || selectedTable.value;
                    } finally {
                        loadingTableNameOptimization.value = false;
                    }
                };

                // 新增函数：一键AI优化所有字段
                const optimizeAllFields = async () => {
                    loadingBatchOptimization.value = true;
                    
                    try {
                        console.log('开始一键AI优化：先生成描述，再优化字段名');
                        
                        // 第一步：先生成所有字段描述
                        ElMessage.info('正在生成字段描述...');
                        const commentPromises = fieldsForOptimization.value.map(async (field) => {
                            // 为所有字段生成描述，包括已有描述的字段（重新生成更准确的描述）
                            field.commentGenerating = true;
                            try {
                                const response = await axios.post('/api/ai/field-naming/generate-comment', {
                                    fieldName: field.name,
                                    fieldType: field.type,
                                    originalComment: field.comment || '',
                                    businessContext: `${selectedTable.value}表中的${field.name}字段用途分析`
                                });
                                
                                if (response.data.success) {
                                    field.comment = response.data.comment;
                                    console.log(`字段描述生成成功: ${field.name} -> ${field.comment}`);
                                } else {
                                    console.error(`字段 ${field.name} 描述生成失败:`, response.data.message);
                                    field.comment = field.comment || `${field.name}字段`;
                                }
                            } catch (error) {
                                console.error(`字段 ${field.name} 描述生成失败:`, error);
                                field.comment = field.comment || `${field.name}字段`;
                            } finally {
                                field.commentGenerating = false;
                            }
                        });
                        
                        await Promise.all(commentPromises);
                        ElMessage.success('字段描述生成完成');
                        
                        // 第二步：根据描述优化字段名
                        ElMessage.info('正在根据描述优化字段名...');
                        const fieldPromises = fieldsForOptimization.value.map(async (field, index) => {
                            field.optimizing = true;
                            try {
                                const response = await axios.post('/api/ai/field-naming/optimize-field', {
                                    originalFieldName: field.name,
                                    fieldType: field.type,
                                    comment: field.comment,
                                    businessContext: `根据字段描述"${field.comment}"为${field.name}字段生成符合数据库命名规范的英文字段名`
                                });
                                
                                if (response.data.success) {
                                    field.optimizedName = response.data.optimizedName;
                                    console.log(`字段名优化成功: ${field.name} -> ${field.optimizedName} (基于描述: ${field.comment})`);
                                } else {
                                    console.error(`字段 ${field.name} 优化失败:`, response.data.message);
                                    // 降级处理
                                    const simpleName = field.name.toLowerCase()
                                        .replace(/[^a-zA-Z0-9]+/g, '_')
                                        .replace(/^_+|_+$/g, '');
                                    field.optimizedName = simpleName || field.name;
                                }
                            } catch (error) {
                                console.error(`字段 ${field.name} 优化失败:`, error);
                                // 降级处理
                                const simpleName = field.name.toLowerCase()
                                    .replace(/[^a-zA-Z0-9]+/g, '_')
                                    .replace(/^_+|_+$/g, '');
                                field.optimizedName = simpleName || field.name;
                            } finally {
                                field.optimizing = false;
                            }
                        });
                        
                        await Promise.all(fieldPromises);
                        ElMessage.success('字段名优化完成');
                        
                        // 检查并处理重复字段名
                        const checkAndFixDuplicateFieldNames = () => {
                            const usedNames = new Set();
                            const duplicateCount = new Map();
                            let hasChanges = false;
                            
                            fieldsForOptimization.value.forEach((field, index) => {
                                let targetName = field.optimizedName || field.name;
                                
                                if (usedNames.has(targetName)) {
                                    // 发现重复，生成新名称
                                    const baseId = duplicateCount.get(targetName) || 1;
                                    duplicateCount.set(targetName, baseId + 1);
                                    const newName = `${targetName}_${baseId + 1}`;
                                    
                                    console.warn(`检测到重复字段名，自动重命名: ${targetName} -> ${newName} (原字段: ${field.name})`);
                                    field.optimizedName = newName;
                                    usedNames.add(newName);
                                    hasChanges = true;
                                } else {
                                    usedNames.add(targetName);
                                }
                            });
                            
                            if (hasChanges) {
                                ElMessage.warning('检测到重复字段名，已自动重命名避免冲突');
                            }
                            
                            return hasChanges;
                        };
                        
                        checkAndFixDuplicateFieldNames();
                        
                        // 第三步：最后优化表名
                        if (tableDescription.value && tableDescription.value.trim()) {
                            ElMessage.info('正在优化表名...');
                            await optimizeTableNameOnly();
                            ElMessage.success('表名优化完成');
                        } else {
                            ElMessage.warning('表名未优化：请先输入表的中文描述');
                        }
                        
                        ElMessage.success('🎉 一键AI优化完成！所有字段都已生成描述并优化字段名');
                        
                    } catch (error) {
                        console.error('批量优化失败:', error);
                        ElMessage.error('批量优化失败，请重试');
                    } finally {
                        loadingBatchOptimization.value = false;
                    }
                };

                const executeMigration = async () => {
                    console.log('🚀 executeMigration 开始执行');

                    migrating.value = true;
                    migrationProgress.value = 0;
                    migrationStatus.value = '初始化中';

                    console.log('📊 迁移状态已设置:', {
                        migrating: migrating.value,
                        progress: migrationProgress.value,
                        status: migrationStatus.value
                    });

                    try {
                        // 进度更新：10%
                        migrationProgress.value = 10;
                        migrationStatus.value = '同步字段信息';
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // 先将fieldsForOptimization的数据同步到optimizedFields，以便迁移服务使用
                        optimizedFields.value = fieldsForOptimization.value.map(field => ({
                            name: field.optimizedName,
                            type: field.type,
                            nullable: field.nullable,
                            comment: field.comment,
                            original: field.name
                        }));

                        // 进度更新：25%
                        migrationProgress.value = 25;
                        migrationStatus.value = '构建字段映射';
                        await new Promise(resolve => setTimeout(resolve, 600));

                        // 构建字段映射数据
                        const fieldMappings = fieldsForOptimization.value.map(field => ({
                            sourceField: field.name,
                            targetField: field.optimizedName || field.name,
                            sourceType: field.type,
                            comment: field.comment,
                            fieldType: field.optimizedName ? 'AI_OPTIMIZED' : 'MANUAL',
                            isEnglishName: true,
                            isSnakeCase: true,
                            confidence: 0.9
                        }));

                        // 进度更新：45%
                        migrationProgress.value = 45;
                        migrationStatus.value = '准备迁移请求';
                        await new Promise(resolve => setTimeout(resolve, 400));

                        // 构建请求数据
                        const requestData = {
                            sourceDatabase: selectedSourceDb.value,
                            targetDatabase: selectedTargetDb.value,
                            tableName: selectedTable.value,
                            targetTableName: optimizedTableName.value,
                            copyData: copyData.value,
                            dropIfExists: dropIfExists.value,
                            fieldMappings: fieldMappings,
                            // 添加业务上下文描述，用于生成表注释
                            businessContext: tableDescription.value && tableDescription.value.trim()
                                ? tableDescription.value.trim()
                                : null
                        };

                        // 如果是跨数据库迁移，添加相关参数
                        if (selectedTargetDbType.value === 'MYSQL') {
                            requestData.crossDatabaseMigration = true;
                            requestData.sourceDbType = 'POSTGRESQL';
                            requestData.targetDbType = 'MYSQL';
                            requestData.useDualPrimaryKey = true;
                            requestData.newPrimaryKeyFieldName = 'id';
                            requestData.businessKeyFieldName = 'business_id';
                        }

                        // 进度更新：65%
                        migrationProgress.value = 65;
                        migrationStatus.value = '正在迁移数据';

                        const response = await axios.post('/api/copy/table', requestData);

                        // 进度更新：100%
                        migrationProgress.value = 100;
                        migrationStatus.value = '迁移完成';
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        migrationResult.value = response.data;
                    } catch (error) {
                        migrationProgress.value = 0;
                        migrationStatus.value = '迁移失败';

                        // 提取原始错误信息
                        let rawErrorMessage = '迁移失败';
                        if (error.response && error.response.data) {
                            // 服务器返回的详细错误信息
                            rawErrorMessage = error.response.data.message || rawErrorMessage;
                        } else if (error.message) {
                            // 网络或其他错误
                            rawErrorMessage = error.message;
                        }

                        // 转换为用户友好的错误信息
                        const userFriendlyMessage = translateErrorMessage(rawErrorMessage);

                        migrationResult.value = {
                            success: false,
                            message: userFriendlyMessage
                        };

                        console.error('Migration error details:', error);
                        console.log('Raw error message:', rawErrorMessage);
                        console.log('User-friendly message:', userFriendlyMessage);
                    } finally {
                        migrating.value = false;
                        // 延迟重置进度状态，让用户能看到最终状态
                        setTimeout(() => {
                            migrationProgress.value = 0;
                            migrationStatus.value = '';
                        }, 2000);
                    }
                };

                const nextStep = async () => {
                    console.log('🔄 nextStep 被调用，当前步骤:', currentStep.value);
                    console.log('🔍 canProceed 状态:', canProceed.value);

                    if (!canProceed.value) {
                        console.log('❌ canProceed 为 false，无法继续');
                        return;
                    }

                    // 当从第1步进入第2步时，自动加载表列表
                    if (currentStep.value === 1) {
                        currentStep.value++;
                        await loadTables();
                        return;
                    }
                    
                    // 当从第3步进入第4步时，自动查询历史记录
                    if (currentStep.value === 3) {
                        currentStep.value++;
                        await loadMigrationHistory();
                        return;
                    }
                    
                    // 当从第4步进入下一步时，根据用户选择决定跳转到第5步还是第6步
                    if (currentStep.value === 4) {
                        if (hasMatchingHistory.value && useHistoryRecord.value) {
                            // 使用历史记录，跳过AI优化，直接到确认页面
                            await applyHistoryRecord();
                            currentStep.value = 6; // 跳过第5步（AI优化）
                        } else {
                            // 继续AI优化流程
                            currentStep.value = 5;
                        }
                        return;
                    }
                    
                    // 第6步执行迁移
                    if (currentStep.value === 6) {
                        console.log('🚀 开始执行迁移...');
                        try {
                            await executeMigration();
                            console.log('✅ 迁移执行完成');
                        } catch (error) {
                            console.error('❌ 迁移执行失败:', error);
                            return; // 迁移失败时不要继续到下一步
                        }
                    }

                    currentStep.value++;
                };

                const previousStep = () => {
                    if (currentStep.value > 1) {
                        currentStep.value--;
                    }
                };

                const goToStep = (step) => {
                    if (step <= currentStep.value || step === 1) {
                        currentStep.value = step;
                    }
                };

                const startNewMigration = () => {
                    // 重置所有数据
                    currentStep.value = 1;
                    selectedSourceDb.value = '';
                    selectedTargetDb.value = '';
                    selectedTable.value = '';
                    optimizedTableName.value = '';
                    tableDescription.value = '';
                    tables.value = [];
                    originalFields.value = [];
                    optimizedFields.value = [];
                    fieldsForOptimization.value = [];
                    migrationResult.value = { success: false, message: '', data: null };
                    
                    // 重置历史记录相关数据
                    historyRecords.value = [];
                    hasMatchingHistory.value = false;
                    matchedRecord.value = null;
                    useHistoryRecord.value = false;
                    loadingHistory.value = false;
                };

                // 日志相关方法
                const initLogWebSocket = () => {
                    console.log('🔧 初始化日志WebSocket...');

                    // 添加消息处理器
                    const messageHandler = (message) => {
                        console.log('📨 收到日志消息:', message);
                        logMessages.value.push(message);

                        // 限制日志数量，避免内存溢出
                        if (logMessages.value.length > 500) {
                            logMessages.value.splice(0, 100);
                        }

                        // 自动滚动到底部
                        if (autoScroll.value) {
                            Vue.nextTick(() => {
                                if (logContent.value) {
                                    logContent.value.scrollTop = logContent.value.scrollHeight;
                                }
                            });
                        }
                    };

                    // 添加状态处理器
                    const statusHandler = (connected) => {
                        console.log('🔗 WebSocket状态变化:', connected);
                        logConnected.value = connected;
                    };

                    // 注册处理器
                    globalLogWebSocket.addMessageHandler(messageHandler);
                    globalLogWebSocket.addStatusHandler(statusHandler);

                    // 设置初始状态
                    const currentlyConnected = globalLogWebSocket.isConnected();
                    console.log('📊 当前WebSocket连接状态:', currentlyConnected);
                    logConnected.value = currentlyConnected;

                    // 如果还没连接，则连接
                    if (!currentlyConnected) {
                        console.log('🚀 开始连接WebSocket...');
                        globalLogWebSocket.connect();
                    } else {
                        console.log('✅ WebSocket已连接，跳过重复连接');
                    }

                    // 返回清理函数
                    return () => {
                        globalLogWebSocket.removeMessageHandler(messageHandler);
                        globalLogWebSocket.removeStatusHandler(statusHandler);
                    };
                };

                const clearLogs = () => {
                    logMessages.value = [];
                };

                const toggleAutoScroll = () => {
                    autoScroll.value = !autoScroll.value;
                };

                const toggleLogPanel = () => {
                    logPanelExpanded.value = !logPanelExpanded.value;
                };

                const getLogClass = (log) => {
                    if (log.includes('❌') || log.includes('ERROR')) return 'error';
                    if (log.includes('⚠️') || log.includes('WARN')) return 'warn';
                    if (log.includes('ℹ️') || log.includes('INFO')) return 'info';
                    if (log.includes('🔧') || log.includes('DEBUG')) return 'debug';
                    return '';
                };

                // 生命周期
                onMounted(() => {
                    // 先加载数据库配置，然后加载数据库列表
                    if (loadDatabaseConfig()) {
                        loadDatabases();
                    } else {
                        // 配置未找到，跳转到配置页面
                        window.location.href = '/database-config.html';
                    }

                    // 初始化日志WebSocket（返回清理函数）
                    const cleanupWebSocket = initLogWebSocket();

                    // 监听localStorage变化，实时同步配置更新
                    const handleStorageChange = (event) => {
                        if (event.key === 'databaseConfig' && event.newValue) {
                            console.log('检测到数据库配置变更，重新加载...');
                            try {
                                const newConfig = JSON.parse(event.newValue);
                                databaseConfig.value = newConfig;
                                
                                // 重新加载数据库列表
                                loadDatabases();
                                
                                // 清空当前选择状态，让用户重新选择
                                selectedSourceDb.value = '';
                                selectedTargetDb.value = '';
                                selectedTable.value = '';
                                tables.value = [];
                                originalFields.value = [];
                                optimizedFields.value = [];
                                fieldsForOptimization.value = [];
                                
                                // 重置历史记录相关数据
                                historyRecords.value = [];
                                hasMatchingHistory.value = false;
                                matchedRecord.value = null;
                                useHistoryRecord.value = false;
                                loadingHistory.value = false;
                                
                                // 重置到第一步
                                currentStep.value = 1;
                                
                                ElMessage.success('数据库配置已更新，请重新进行迁移操作');
                            } catch (error) {
                                console.error('解析新配置失败:', error);
                                ElMessage.error('配置更新失败，请刷新页面');
                            }
                        }
                    };

                    // 添加事件监听器
                    window.addEventListener('storage', handleStorageChange);
                    
                    // 组件卸载时清理事件监听器
                    const cleanup = () => {
                        window.removeEventListener('storage', handleStorageChange);
                        // 清理WebSocket处理器（但不关闭连接，保持全局连接）
                        if (cleanupWebSocket) {
                            cleanupWebSocket();
                        }
                    };

                    // Vue 3没有beforeDestroy，需要手动管理清理
                    // 使用pagehide事件替代beforeunload，避免阻止浏览器导航
                    window.addEventListener('pagehide', cleanup);
                });

                return {
                    currentStep,
                    steps,
                    loadingDatabases,
                    loadingTables,
                    loadingOptimization,
                    loadingTableNameOptimization,
                    loadingBatchOptimization,
                    migrating,
                    migrationProgress,
                    migrationStatus,
                    migrationStats,
                    errorMessage,
                    databases,
                    tables,
                    originalFields,
                    optimizedFields,
                    fieldsForOptimization,
                    selectedSourceDb,
                    selectedTargetDb,
                    selectedTargetDbType,
                    selectedTable,
                    optimizedTableName,
                    tableDescription,
                    copyData,
                    dropIfExists,
                    migrationResult,
                    // 历史记录相关
                    loadingHistory,
                    historyRecords,
                    hasHistoryRecords,
                    hasMatchingHistory,
                    matchedRecord,
                    useHistoryRecord,
                    availableSourceDatabases,
                    availableTargetDatabases,
                    optimizedFieldsCount,
                    canProceed,
                    selectSourceDatabase,
                    selectTargetDatabase,
                    selectTable,
                    optimizeSingleField,
                    generateSingleComment,
                    optimizeTableNameOnly,
                    optimizeAllFields,
                    reoptimizeFields,
                    // 历史记录相关方法
                    loadMigrationHistory,
                    findMatchingRecord,
                    applyHistoryRecord,
                    formatDate,
                    getFieldDifferenceSummary,
                    getAdjustedFieldMappings,
                    nextStep,
                    previousStep,
                    goToStep,
                    startNewMigration,
                    // 日志相关
                    logMessages,
                    logConnected,
                    logPanelExpanded,
                    autoScroll,
                    logContent,
                    clearLogs,
                    toggleAutoScroll,
                    toggleLogPanel,
                    getLogClass
                };
            }
        }).mount('#app');

        // 页面卸载时关闭全局WebSocket连接
        // 使用pagehide事件替代beforeunload，避免阻止浏览器导航
        window.addEventListener('pagehide', () => {
            globalLogWebSocket.disconnect();
        });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializePage().then(() => {
                // WebSocket连接将在Vue应用的onMounted中初始化，这里不需要重复连接
                console.log('📄 页面初始化完成，WebSocket将在Vue应用中初始化');
            });
        });
    </script>
</body>
</html>