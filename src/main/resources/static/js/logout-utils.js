/**
 * 退出登录工具函数
 */

/**
 * 显示退出确认对话框
 * @param {Function} onConfirm 确认回调
 * @param {Function} onCancel 取消回调
 */
function showLogoutConfirmDialog(onConfirm, onCancel) {
    // 创建对话框HTML
    const dialogHTML = `
        <div id="logout-dialog-overlay" class="logout-dialog-overlay">
            <div class="logout-dialog">
                <div class="logout-dialog-header">
                    <h3>🚪 退出登录</h3>
                </div>
                <div class="logout-dialog-body">
                    <p>确定要退出登录吗？</p>
                    <p class="logout-dialog-note">退出后需要重新登录才能使用系统功能</p>
                </div>
                <div class="logout-dialog-footer">
                    <button id="logout-cancel-btn" class="logout-btn-secondary">
                        取消
                    </button>
                    <button id="logout-confirm-btn" class="logout-btn-primary">
                        确定退出
                    </button>
                </div>
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .logout-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .logout-dialog {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            width: 400px;
            max-width: 90vw;
            animation: slideIn 0.3s ease;
        }

        .logout-dialog-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .logout-dialog-header h3 {
            margin: 0;
            font-size: 18px;
            color: #2c3e50;
        }

        .logout-dialog-body {
            padding: 20px 24px;
        }

        .logout-dialog-body p {
            margin: 0 0 12px 0;
            color: #34495e;
            line-height: 1.5;
        }

        .logout-dialog-note {
            font-size: 14px;
            color: #7f8c8d !important;
        }

        .logout-dialog-footer {
            padding: 16px 24px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .logout-btn-primary, .logout-btn-secondary {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn-primary {
            background: #e74c3c;
            color: white;
        }

        .logout-btn-primary:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .logout-btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .logout-btn-secondary:hover {
            background: #d5dbdb;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    `;

    // 添加样式到页面
    document.head.appendChild(style);

    // 添加对话框到页面
    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    // 获取元素
    const overlay = document.getElementById('logout-dialog-overlay');
    const confirmBtn = document.getElementById('logout-confirm-btn');
    const cancelBtn = document.getElementById('logout-cancel-btn');

    // 绑定事件
    confirmBtn.addEventListener('click', () => {
        removeDialog();
        if (onConfirm) onConfirm();
    });

    cancelBtn.addEventListener('click', () => {
        removeDialog();
        if (onCancel) onCancel();
    });

    // 点击遮罩层关闭
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            removeDialog();
            if (onCancel) onCancel();
        }
    });

    // ESC键关闭
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            removeDialog();
            if (onCancel) onCancel();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    // 移除对话框
    function removeDialog() {
        const dialog = document.getElementById('logout-dialog-overlay');
        if (dialog) {
            dialog.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                dialog.remove();
                style.remove();
            }, 300);
        }
        document.removeEventListener('keydown', handleEscape);
    }
}

/**
 * 执行退出登录
 * @param {Object} authManager 认证管理器实例
 */
async function performLogout(authManager) {
    try {
        // 显示加载状态
        showLogoutLoading();

        // 执行退出
        await authManager.logout();

        // 显示成功消息
        showLogoutSuccess();

        // 延迟跳转
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);

    } catch (error) {
        console.error('❌ 退出登录失败:', error);
        hideLogoutLoading();
        showLogoutError('退出登录失败，请稍后重试');
    }
}

/**
 * 显示退出加载状态
 */
function showLogoutLoading() {
    const loadingHTML = `
        <div id="logout-loading" class="logout-loading-overlay">
            <div class="logout-loading-content">
                <div class="logout-loading-spinner"></div>
                <p>正在退出登录...</p>
            </div>
        </div>
    `;

    const style = document.createElement('style');
    style.id = 'logout-loading-style';
    style.textContent = `
        .logout-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
        }

        .logout-loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .logout-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;

    document.head.appendChild(style);
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
}

/**
 * 隐藏退出加载状态
 */
function hideLogoutLoading() {
    const loading = document.getElementById('logout-loading');
    const style = document.getElementById('logout-loading-style');
    if (loading) loading.remove();
    if (style) style.remove();
}

/**
 * 显示退出成功消息
 */
function showLogoutSuccess() {
    hideLogoutLoading();
    
    const successHTML = `
        <div id="logout-success" class="logout-message-overlay">
            <div class="logout-message-content logout-success-content">
                <div class="logout-success-icon">✅</div>
                <p>退出登录成功</p>
                <p class="logout-message-note">正在跳转到首页...</p>
            </div>
        </div>
    `;

    const style = document.createElement('style');
    style.id = 'logout-success-style';
    style.textContent = `
        .logout-message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
        }

        .logout-message-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .logout-success-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .logout-message-note {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 8px;
        }
    `;

    document.head.appendChild(style);
    document.body.insertAdjacentHTML('beforeend', successHTML);
}

/**
 * 显示退出错误消息
 */
function showLogoutError(message) {
    const errorHTML = `
        <div id="logout-error" class="logout-message-overlay">
            <div class="logout-message-content logout-error-content">
                <div class="logout-error-icon">❌</div>
                <p>${message}</p>
                <button onclick="document.getElementById('logout-error').remove(); document.getElementById('logout-error-style').remove();" class="logout-error-btn">
                    确定
                </button>
            </div>
        </div>
    `;

    const style = document.createElement('style');
    style.id = 'logout-error-style';
    style.textContent = `
        .logout-error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .logout-error-btn {
            margin-top: 16px;
            padding: 8px 20px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .logout-error-btn:hover {
            background: #c0392b;
        }
    `;

    document.head.appendChild(style);
    document.body.insertAdjacentHTML('beforeend', errorHTML);
}

// 导出到全局作用域
window.logoutUtils = {
    showLogoutConfirmDialog,
    performLogout
};
