/**
 * 智能CDN加载器 - 支持多级降级
 * 优先使用国内CDN，逐级降级到本地资源
 */
class CDNLoader {
    constructor() {
        // CDN健康状态缓存
        this.cdnHealthCache = new Map();

        // 优化CDN配置 - jsDelivr优先，减轻服务器带宽压力（2025年最新有效地址）
        this.cdnConfigs = {
            vue: [
                'https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.min.js',                   // jsDelivr (第一优先级)
                'libs/vue.global.js',                                                            // 本地资源 (第二优先级)
                'https://unpkg.com/vue@3.5.13/dist/vue.global.min.js'                         // unpkg (备用)
            ],
            axios: [
                'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js',                       // jsDelivr (第一优先级)
                'libs/axios.min.js',                                                            // 本地资源 (第二优先级)
                'https://unpkg.com/axios@1.7.9/dist/axios.min.js'                            // unpkg (备用)
            ],
            elementPlus: [
                'https://cdn.jsdelivr.net/npm/element-plus/dist/index.full.min.js',           // jsDelivr (第一优先级)
                'libs/element-plus.js',                                                         // 本地资源 (第二优先级)
                'https://unpkg.com/element-plus@2.9.1/dist/index.full.min.js'                // unpkg (备用)
            ],
            elementPlusCss: [
                'https://cdn.jsdelivr.net/npm/element-plus/dist/index.min.css',               // jsDelivr (第一优先级)
                'libs/element-plus.css',                                                        // 本地资源 (第二优先级)
                'https://unpkg.com/element-plus@2.9.1/dist/index.min.css'                    // unpkg (备用)
            ],
            elementPlusIconsCss: [
                'https://cdn.jsdelivr.net/npm/@element-plus/icons-vue/dist/index.css',       // jsDelivr (第一优先级)
                'https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.css'            // unpkg (备用)
            ],
            elementPlusIconsJs: [
                'libs/element-plus-icons.js',                                                 // 本地资源 (第一优先级)
                'https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js',   // unpkg IIFE版本 (第二优先级)
                'https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.3.1/dist/index.iife.min.js' // jsDelivr IIFE版本 (备用)
            ]
        };
        
        this.loadedResources = new Set();
        this.loadingPromises = new Map();
    }

    /**
     * 快速检测CDN可用性
     */
    async quickCDNCheck(url, timeout = 800) {
        // 简化版本：直接返回true，让实际加载来验证CDN可用性
        // 这样避免了favicon.ico的404/403错误
        return true;
    }

    /**
     * 加载JavaScript资源
     */
    async loadScript(resourceName, timeout = 1500) {
        if (this.loadedResources.has(resourceName)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(resourceName)) {
            return this.loadingPromises.get(resourceName);
        }

        const urls = this.cdnConfigs[resourceName];
        if (!urls) {
            throw new Error(`未找到资源配置: ${resourceName}`);
        }

        const loadPromise = this._loadScriptWithFallback(urls, timeout);
        this.loadingPromises.set(resourceName, loadPromise);

        try {
            await loadPromise;
            this.loadedResources.add(resourceName);
            console.log(`✅ ${resourceName} 加载成功`);
        } catch (error) {
            this.loadingPromises.delete(resourceName);
            throw error;
        }

        return loadPromise;
    }

    /**
     * 加载CSS资源
     */
    async loadCSS(resourceName, timeout = 1500) {
        if (this.loadedResources.has(resourceName)) {
            return Promise.resolve();
        }

        const urls = this.cdnConfigs[resourceName];
        if (!urls) {
            throw new Error(`未找到资源配置: ${resourceName}`);
        }

        try {
            await this._loadCSSWithFallback(urls, timeout);
            this.loadedResources.add(resourceName);
            console.log(`✅ ${resourceName} CSS加载成功`);
        } catch (error) {
            console.error(`❌ ${resourceName} CSS加载失败:`, error);
            throw error;
        }
    }

    /**
     * 带降级的脚本加载（优化版）
     */
    _loadScriptWithFallback(urls, timeout) {
        return new Promise(async (resolve, reject) => {
            // 快速预检测前3个CDN的健康状态
            const healthChecks = await Promise.allSettled(
                urls.slice(0, 3).map(url => this.quickCDNCheck(url))
            );

            // 根据健康检查结果重新排序
            const sortedUrls = [...urls];
            healthChecks.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value === false) {
                    // 将不健康的CDN移到后面
                    const unhealthyUrl = sortedUrls.splice(index, 1)[0];
                    sortedUrls.push(unhealthyUrl);
                }
            });

            let currentIndex = 0;

            const tryLoad = () => {
                if (currentIndex >= sortedUrls.length) {
                    reject(new Error('所有CDN源都加载失败'));
                    return;
                }

                const url = sortedUrls[currentIndex];
                const script = document.createElement('script');
                const timer = setTimeout(() => {
                    script.remove();
                    console.warn(`⚠️ CDN超时 (${timeout}ms): ${url}`);
                    currentIndex++;
                    tryLoad();
                }, timeout);

                script.onload = () => {
                    clearTimeout(timer);
                    console.log(`✅ 成功加载: ${url}`);
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timer);
                    script.remove();
                    console.warn(`❌ CDN失败: ${url}`);
                    currentIndex++;
                    tryLoad();
                };

                script.src = url;
                document.head.appendChild(script);
            };

            tryLoad();
        });
    }

    /**
     * 带降级的CSS加载
     */
    _loadCSSWithFallback(urls, timeout) {
        return new Promise((resolve, reject) => {
            let currentIndex = 0;

            const tryLoad = () => {
                if (currentIndex >= urls.length) {
                    reject(new Error('所有CDN源都加载失败'));
                    return;
                }

                const url = urls[currentIndex];
                const link = document.createElement('link');
                const timer = setTimeout(() => {
                    link.remove();
                    console.warn(`⚠️ CSS CDN超时: ${url}`);
                    currentIndex++;
                    tryLoad();
                }, timeout);

                link.onload = () => {
                    clearTimeout(timer);
                    console.log(`✅ 成功加载CSS: ${url}`);
                    resolve();
                };

                link.onerror = () => {
                    clearTimeout(timer);
                    link.remove();
                    console.warn(`❌ CSS CDN失败: ${url}`);
                    currentIndex++;
                    tryLoad();
                };

                link.rel = 'stylesheet';
                link.href = url;
                document.head.appendChild(link);
            };

            tryLoad();
        });
    }

    /**
     * 批量加载资源
     */
    async loadResources(resources) {
        const loadPromises = resources.map(resource => {
            if (resource.type === 'css') {
                return this.loadCSS(resource.name);
            } else {
                return this.loadScript(resource.name);
            }
        });

        try {
            await Promise.all(loadPromises);
            console.log('🎉 所有资源加载完成');
        } catch (error) {
            console.error('❌ 资源加载失败:', error);
            throw error;
        }
    }

    /**
     * 显示加载进度
     */
    showLoadingProgress(message = '正在加载页面资源...') {
        const overlay = document.createElement('div');
        overlay.id = 'cdn-loading-overlay';
        overlay.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        display: flex; align-items: center; justify-content: center; 
                        z-index: 9999; color: white; font-size: 18px;">
                <div style="text-align: center;">
                    <div style="margin-bottom: 20px;">🚀 ${message}</div>
                    <div style="font-size: 14px; opacity: 0.8;">使用国内CDN加速，请稍候...</div>
                    <div style="margin-top: 20px;">
                        <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; overflow: hidden;">
                            <div id="progress-bar" style="width: 0%; height: 100%; background: #fff; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
        return overlay;
    }

    /**
     * 隐藏加载进度
     */
    hideLoadingProgress() {
        const overlay = document.getElementById('cdn-loading-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => overlay.remove(), 300);
        }
    }

    /**
     * 更新进度条
     */
    updateProgress(percent) {
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            progressBar.style.width = percent + '%';
        }
    }
}

// 创建全局实例
window.cdnLoader = new CDNLoader();

// 导出到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CDNLoader;
}
