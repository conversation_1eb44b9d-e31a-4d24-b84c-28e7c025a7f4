/**
 * 快速CDN加载器 - 简化版本，专注速度
 * 使用最可靠的国内CDN，快速降级
 */
class FastCDNLoader {
    constructor() {
        // 快速加载配置 - jsDelivr优先，减轻服务器带宽压力
        this.cdnConfigs = {
            vue: [
                'https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.min.js',      // jsDelivr (第一优先级)
                'libs/vue.global.js'                                              // 本地资源 (第二优先级)
            ],
            axios: [
                'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js',          // jsDelivr (第一优先级)
                'libs/axios.min.js'                                              // 本地资源 (第二优先级)
            ],
            elementPlus: [
                'https://cdn.jsdelivr.net/npm/element-plus/dist/index.full.min.js',       // jsDelivr (第一优先级)
                'libs/element-plus.js'                                                     // 本地资源 (第二优先级)
            ],
            elementPlusCss: [
                'https://cdn.jsdelivr.net/npm/element-plus/dist/index.min.css',           // jsDelivr (第一优先级)
                'libs/element-plus.css'                                                    // 本地资源 (第二优先级)
            ]
        };
        
        this.loadedResources = new Set();
    }

    /**
     * 快速加载脚本
     */
    async loadScript(resourceName, timeout = 500) {
        if (this.loadedResources.has(resourceName)) {
            return Promise.resolve();
        }

        const urls = this.cdnConfigs[resourceName];
        if (!urls) {
            throw new Error(`未找到资源配置: ${resourceName}`);
        }

        try {
            await this._fastLoadScript(urls, timeout);
            this.loadedResources.add(resourceName);
            console.log(`✅ ${resourceName} 加载成功`);
        } catch (error) {
            console.error(`❌ ${resourceName} 加载失败:`, error);
            throw error;
        }
    }

    /**
     * 快速加载CSS
     */
    async loadCSS(resourceName, timeout = 500) {
        if (this.loadedResources.has(resourceName)) {
            return Promise.resolve();
        }

        const urls = this.cdnConfigs[resourceName];
        if (!urls) {
            throw new Error(`未找到资源配置: ${resourceName}`);
        }

        try {
            await this._fastLoadCSS(urls, timeout);
            this.loadedResources.add(resourceName);
            console.log(`✅ ${resourceName} CSS加载成功`);
        } catch (error) {
            console.error(`❌ ${resourceName} CSS加载失败:`, error);
            throw error;
        }
    }

    /**
     * 快速脚本加载实现
     */
    _fastLoadScript(urls, timeout) {
        return new Promise((resolve, reject) => {
            let currentIndex = 0;
            let attempts = 0;
            const maxAttempts = urls.length;

            const tryLoad = () => {
                if (attempts >= maxAttempts) {
                    console.error('❌ 所有CDN源都加载失败');
                    reject(new Error('所有CDN源都加载失败'));
                    return;
                }

                const url = urls[currentIndex];
                const script = document.createElement('script');
                let isResolved = false;

                const timer = setTimeout(() => {
                    if (!isResolved) {
                        isResolved = true;
                        script.remove();
                        console.warn(`⚠️ 超时 (${timeout}ms): ${url}`);
                        nextAttempt();
                    }
                }, timeout);

                script.onload = () => {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timer);
                        console.log(`✅ 成功: ${url}`);
                        resolve();
                    }
                };

                script.onerror = () => {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timer);
                        script.remove();
                        console.warn(`❌ 失败: ${url}`);
                        nextAttempt();
                    }
                };

                script.src = url;
                document.head.appendChild(script);
            };

            const nextAttempt = () => {
                attempts++;
                currentIndex++;
                if (currentIndex < urls.length && attempts < maxAttempts) {
                    setTimeout(tryLoad, 10); // 10ms快速重试
                } else {
                    console.error('❌ 所有CDN源都加载失败');
                    reject(new Error('所有CDN源都加载失败'));
                }
            };

            tryLoad();
        });
    }

    /**
     * 快速CSS加载实现
     */
    _fastLoadCSS(urls, timeout) {
        return new Promise((resolve, reject) => {
            let currentIndex = 0;
            let attempts = 0;
            const maxAttempts = urls.length;

            const tryLoad = () => {
                if (attempts >= maxAttempts) {
                    console.error('❌ 所有CSS CDN源都加载失败');
                    reject(new Error('所有CSS CDN源都加载失败'));
                    return;
                }

                const url = urls[currentIndex];
                const link = document.createElement('link');
                let isResolved = false;

                const timer = setTimeout(() => {
                    if (!isResolved) {
                        isResolved = true;
                        link.remove();
                        console.warn(`⚠️ CSS超时 (${timeout}ms): ${url}`);
                        nextAttempt();
                    }
                }, timeout);

                link.onload = () => {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timer);
                        console.log(`✅ CSS成功: ${url}`);
                        resolve();
                    }
                };

                link.onerror = () => {
                    if (!isResolved) {
                        isResolved = true;
                        clearTimeout(timer);
                        link.remove();
                        console.warn(`❌ CSS失败: ${url}`);
                        nextAttempt();
                    }
                };

                link.rel = 'stylesheet';
                link.href = url;
                document.head.appendChild(link);
            };

            const nextAttempt = () => {
                attempts++;
                currentIndex++;
                if (currentIndex < urls.length && attempts < maxAttempts) {
                    setTimeout(tryLoad, 10); // 10ms快速重试
                } else {
                    console.error('❌ 所有CSS CDN源都加载失败');
                    reject(new Error('所有CSS CDN源都加载失败'));
                }
            };

            tryLoad();
        });
    }

    /**
     * 显示简单加载提示
     */
    showLoadingProgress(message = '正在加载...') {
        const overlay = document.createElement('div');
        overlay.id = 'fast-loading-overlay';
        overlay.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        display: flex; align-items: center; justify-content: center; 
                        z-index: 9999; color: white; font-size: 18px; font-family: Arial, sans-serif;">
                <div style="text-align: center;">
                    <div style="margin-bottom: 20px;">🚀 ${message}</div>
                    <div style="font-size: 14px; opacity: 0.8;">使用国内CDN加速，请稍候...</div>
                    <div style="margin-top: 20px;">
                        <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; overflow: hidden; margin: 0 auto;">
                            <div style="width: 100%; height: 100%; background: #fff; animation: loading 2s infinite;"></div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                @keyframes loading {
                    0% { transform: translateX(-100%); }
                    50% { transform: translateX(0%); }
                    100% { transform: translateX(100%); }
                }
            </style>
        `;
        document.body.appendChild(overlay);
        return overlay;
    }

    /**
     * 隐藏加载提示
     */
    hideLoadingProgress() {
        const overlay = document.getElementById('fast-loading-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => overlay.remove(), 300);
        }
    }

    /**
     * 批量加载资源
     */
    async loadResources(resources) {
        for (const resource of resources) {
            if (resource.type === 'css') {
                await this.loadCSS(resource.name);
            } else {
                await this.loadScript(resource.name);
            }
        }
        console.log('🎉 所有资源加载完成');
    }
}

// 创建全局实例
window.fastCdnLoader = new FastCDNLoader();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FastCDNLoader;
}
