/**
 * Sa-Token版本的用户认证和状态管理工具
 */
class SaTokenAuthManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.loginCallbacks = [];
        this.logoutCallbacks = [];
        this.token = null;
        this.tokenKey = 'DB_COPY_TOKEN';
    }

    /**
     * 检查用户登录状态
     */
    async checkLoginStatus() {
        try {
            // 从localStorage获取token
            this.token = localStorage.getItem(this.tokenKey);
            
            if (!this.token) {
                return false;
            }

            // 设置axios默认header
            axios.defaults.headers.common['satoken'] = this.token;

            const response = await axios.get('/api/satoken/current-user');
            if (response.data.success && response.data.user) {
                this.setCurrentUser(response.data.user);
                return true;
            } else {
                this.clearCurrentUser();
                return false;
            }
        } catch (error) {
            console.error('检查登录状态失败:', error);
            this.clearCurrentUser();
            return false;
        }
    }

    /**
     * 用户登录
     */
    async login(username, password) {
        try {
            const response = await axios.post('/api/satoken/login', {
                username: username,
                password: password
            });

            if (response.data.success) {
                // 保存token
                this.token = response.data.token;
                localStorage.setItem(this.tokenKey, this.token);
                
                // 设置axios默认header
                axios.defaults.headers.common['satoken'] = this.token;
                
                // 设置用户信息
                this.setCurrentUser(response.data.user);
                
                // 通知登录回调
                this.notifyLoginCallbacks(response.data.user);
                
                return {
                    success: true,
                    message: response.data.message,
                    user: response.data.user,
                    token: this.token
                };
            } else {
                return {
                    success: false,
                    message: response.data.message
                };
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            return {
                success: false,
                message: error.response?.data?.message || '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            if (this.token) {
                await axios.post('/api/satoken/logout');
            }
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            this.clearCurrentUser();
            this.notifyLogoutCallbacks();
        }
    }

    /**
     * 用户注册
     */
    async register(userData) {
        try {
            const response = await axios.post('/api/satoken/register', userData);
            return {
                success: response.data.success,
                message: response.data.message,
                user: response.data.user
            };
        } catch (error) {
            console.error('注册请求失败:', error);
            return {
                success: false,
                message: error.response?.data?.message || '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 获取Token信息
     */
    async getTokenInfo() {
        try {
            const response = await axios.get('/api/satoken/token-info');
            return response.data;
        } catch (error) {
            console.error('获取Token信息失败:', error);
            return { success: false, message: '获取Token信息失败' };
        }
    }

    /**
     * 刷新用户信息
     */
    async refreshUserInfo() {
        try {
            const response = await axios.get('/api/satoken/current-user');
            if (response.data.success && response.data.user) {
                this.setCurrentUser(response.data.user);
                return true;
            }
            return false;
        } catch (error) {
            console.error('刷新用户信息失败:', error);
            return false;
        }
    }

    /**
     * 设置当前用户
     */
    setCurrentUser(user) {
        this.currentUser = user;
        this.isLoggedIn = true;
    }

    /**
     * 清除当前用户
     */
    clearCurrentUser() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.token = null;
        
        // 清除localStorage
        localStorage.removeItem(this.tokenKey);
        
        // 清除axios默认header
        delete axios.defaults.headers.common['satoken'];
    }

    /**
     * 获取当前用户
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查是否已登录
     */
    isUserLoggedIn() {
        return this.isLoggedIn && this.currentUser != null;
    }

    /**
     * 获取当前Token
     */
    getToken() {
        return this.token;
    }

    /**
     * 添加登录回调
     */
    onLogin(callback) {
        this.loginCallbacks.push(callback);
    }

    /**
     * 添加登出回调
     */
    onLogout(callback) {
        this.logoutCallbacks.push(callback);
    }

    /**
     * 移除登录回调
     */
    removeLoginCallback(callback) {
        const index = this.loginCallbacks.indexOf(callback);
        if (index > -1) {
            this.loginCallbacks.splice(index, 1);
        }
    }

    /**
     * 移除登出回调
     */
    removeLogoutCallback(callback) {
        const index = this.logoutCallbacks.indexOf(callback);
        if (index > -1) {
            this.logoutCallbacks.splice(index, 1);
        }
    }

    /**
     * 通知登录回调
     */
    notifyLoginCallbacks(user) {
        this.loginCallbacks.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('登录回调执行失败:', error);
            }
        });
    }

    /**
     * 通知登出回调
     */
    notifyLogoutCallbacks() {
        this.logoutCallbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('登出回调执行失败:', error);
            }
        });
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin(returnUrl) {
        const loginUrl = '/login.html';
        if (returnUrl) {
            window.location.href = `${loginUrl}?returnUrl=${encodeURIComponent(returnUrl)}`;
        } else {
            window.location.href = loginUrl;
        }
    }

    /**
     * 处理未授权响应
     */
    handleUnauthorized() {
        console.log('🔒 处理未授权响应，当前页面:', window.location.pathname);
        this.clearCurrentUser();
        this.notifyLogoutCallbacks();

        // 只有在非主页和非登录页面时才重定向
        // 主页应该显示欢迎页面而不是跳转到登录页
        const currentPath = window.location.pathname;
        const isHomePage = currentPath === '/' || currentPath === '/index.html';
        const isLoginPage = currentPath.includes('/login.html');

        if (!isHomePage && !isLoginPage) {
            console.log('🔄 重定向到登录页面，因为当前不在主页/登录页');
            this.redirectToLogin(window.location.pathname + window.location.search);
        } else {
            console.log('🏠 当前在主页/欢迎页/登录页，不进行重定向');
        }
    }

    /**
     * 初始化认证管理器
     */
    async init() {
        // 设置axios拦截器
        this.setupAxiosInterceptors();
        
        // 检查登录状态
        await this.checkLoginStatus();
    }

    /**
     * 设置axios拦截器
     */
    setupAxiosInterceptors() {
        // 请求拦截器 - 自动添加token
        axios.interceptors.request.use(
            (config) => {
                const token = localStorage.getItem(this.tokenKey);
                if (token) {
                    config.headers['satoken'] = token;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // 响应拦截器 - 处理认证相关错误
        axios.interceptors.response.use(
            (response) => {
                return response;
            },
            (error) => {
                const requestUrl = error.config?.url || '';
                const isApiRequest = requestUrl.startsWith('/api/');

                // 处理401未授权
                if (error.response && error.response.status === 401) {
                    if (isApiRequest) {
                        const currentPath = window.location.pathname;
                        const isHomePage = currentPath === '/' || currentPath === '/index.html';

                        console.log('🔒 API请求未授权(401)，当前页面:', currentPath);
                        // 如果是主页的登录状态检查API失败，不要跳转，让主页显示欢迎界面
                        if (isHomePage && requestUrl.includes('/current-user')) {
                            console.log('🏠 主页登录状态检查失败，显示欢迎界面而不跳转');
                        } else {
                            console.log('🔄 非主页API请求未授权，执行重定向');
                            this.handleUnauthorized();
                        }
                    } else {
                        console.log('🔍 非API请求401响应，不自动重定向');
                    }
                }
                // 处理认证失败的错误响应（检查错误代码）
                else if (error.response && error.response.data && error.response.data.code === 'NOT_LOGIN') {
                    if (isApiRequest) {
                        const currentPath = window.location.pathname;
                        const isHomePage = currentPath === '/' || currentPath === '/index.html';

                        console.log('🔒 API请求认证失败(NOT_LOGIN)，当前页面:', currentPath);
                        // 如果是主页的登录状态检查API失败，不要跳转
                        if (isHomePage && requestUrl.includes('/current-user')) {
                            console.log('🏠 主页登录状态检查失败，显示欢迎界面而不跳转');
                        } else {
                            console.log('🔄 非主页API请求认证失败，执行重定向');
                            this.handleUnauthorized();
                        }
                    } else {
                        console.log('🔍 非API请求认证失败，不自动重定向');
                    }
                }

                return Promise.reject(error);
            }
        );
    }
}

// 创建全局实例
const saTokenAuthManager = new SaTokenAuthManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    saTokenAuthManager.init();
});

// 导出到全局作用域
window.saTokenAuthManager = saTokenAuthManager;
