<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <!-- 数据库图标 -->
  <defs>
    <linearGradient id="dbGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 数据库圆柱体顶部 -->
  <ellipse cx="16" cy="8" rx="12" ry="4" fill="url(#dbGradient)" stroke="#2C5F8A" stroke-width="1"/>
  
  <!-- 数据库圆柱体主体 -->
  <rect x="4" y="8" width="24" height="16" fill="url(#dbGradient)" stroke="#2C5F8A" stroke-width="1"/>
  
  <!-- 数据库圆柱体底部 -->
  <ellipse cx="16" cy="24" rx="12" ry="4" fill="url(#dbGradient)" stroke="#2C5F8A" stroke-width="1"/>
  
  <!-- 数据库分层线 -->
  <ellipse cx="16" cy="14" rx="12" ry="3" fill="none" stroke="#2C5F8A" stroke-width="1" opacity="0.7"/>
  <ellipse cx="16" cy="19" rx="12" ry="3" fill="none" stroke="#2C5F8A" stroke-width="1" opacity="0.7"/>
  
  <!-- 数据传输箭头 -->
  <path d="M20 12 L24 12 L22 10 M24 12 L22 14" stroke="#FFD700" stroke-width="2" fill="none" stroke-linecap="round"/>
</svg>
