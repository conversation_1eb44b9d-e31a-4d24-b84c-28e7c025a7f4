<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 数据库迁移系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">

    <!-- CSS将通过CDN加载器动态加载 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            max-width: 90vw;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .login-subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }

        .login-form {
            margin-top: 30px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: 500;
        }

        .input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
            padding: 10px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
            padding: 10px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .demo-info {
            margin-top: 30px;
            padding: 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .demo-info h4 {
            color: #2980b9;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .demo-info p {
            color: #34495e;
            font-size: 13px;
            margin: 4px 0;
        }

        .back-to-home {
            text-align: center;
            margin-top: 20px;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .back-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
            }
            
            .login-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>


    <div id="app" style="display: none;">
        <div class="login-container">
            <div class="login-header">
                <h1 class="login-title">🔐 用户登录</h1>
                <p class="login-subtitle">数据库迁移系统</p>
            </div>

            <form class="login-form" @submit.prevent="handleLogin">
                <div class="form-item">
                    <label for="username">用户名</label>
                    <div class="input-wrapper">
                        <input 
                            id="username"
                            type="text" 
                            class="form-input"
                            v-model="loginForm.username"
                            placeholder="请输入用户名"
                            :disabled="loading"
                            required
                        >
                    </div>
                </div>

                <div class="form-item">
                    <label for="password">密码</label>
                    <div class="input-wrapper">
                        <input 
                            id="password"
                            type="password" 
                            class="form-input"
                            v-model="loginForm.password"
                            placeholder="请输入密码"
                            :disabled="loading"
                            required
                        >
                    </div>
                </div>

                <button 
                    type="submit" 
                    class="login-button"
                    :disabled="loading || !loginForm.username || !loginForm.password"
                >
                    <span v-if="loading" class="loading-spinner"></span>
                    {{ loading ? '登录中...' : '登录' }}
                </button>

                <div v-if="errorMessage" class="error-message">
                    {{ errorMessage }}
                </div>

                <div v-if="successMessage" class="success-message">
                    {{ successMessage }}
                </div>
            </form>



            <div class="back-to-home">
                <a href="/" class="back-link">
                    🏠 返回首页
                </a>
            </div>

            <div class="footer">
                <p>&copy; 2025 数据库迁移系统 - 安全可靠的数据迁移解决方案</p>
            </div>
        </div>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script src="js/satoken-auth.js"></script>
    <script>
        // 智能加载页面资源（参考注册页面逻辑）
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载登录页面...');

                cdnLoader.updateProgress(20);
                await cdnLoader.loadCSS('elementPlusCss');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(90);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
                document.body.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
                        <div>
                            <h3>⚠️ 资源加载失败</h3>
                            <p>请检查网络连接或刷新页面重试</p>
                            <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #fff; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 刷新页面
                            </button>
                        </div>
                    </div>
                `;
            }
        }



        function initVueApp() {
            console.log('🔧 initVueApp函数开始执行...');

            // 检查Vue是否可用
            if (typeof Vue === 'undefined') {
                console.error('❌ Vue未加载，无法初始化应用');
                document.body.innerHTML = '<div style="text-align: center; padding: 50px; color: red;">Vue框架加载失败，请刷新页面重试</div>';
                return;
            }

            console.log('✅ Vue已加载，版本:', Vue.version || 'unknown');

            const { createApp, ref, onMounted } = Vue;

            createApp({
                setup() {
                    const loginForm = ref({
                        username: '',
                        password: ''
                    });

                    const loading = ref(false);
                    const errorMessage = ref('');
                    const successMessage = ref('');

                // 处理登录
                const handleLogin = async () => {
                    if (!loginForm.value.username || !loginForm.value.password) {
                        errorMessage.value = '请输入用户名和密码';
                        return;
                    }

                    loading.value = true;
                    errorMessage.value = '';
                    successMessage.value = '';

                    try {
                        const response = await axios.post('/api/satoken/login', {
                            username: loginForm.value.username,
                            password: loginForm.value.password
                        });

                        if (response.data.success) {
                            // 保存Token到localStorage
                            if (response.data.token) {
                                localStorage.setItem('DB_COPY_TOKEN', response.data.token);
                            }

                            successMessage.value = '登录成功，正在跳转...';

                            // 延迟跳转，让用户看到成功消息
                            setTimeout(() => {
                                // 跳转到首页或之前访问的页面
                                const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/';
                                window.location.href = redirectUrl;
                            }, 1000);
                        } else {
                            errorMessage.value = response.data.message || '登录失败';
                        }
                    } catch (error) {
                        console.error('登录错误:', error);
                        if (error.response && error.response.data && error.response.data.message) {
                            errorMessage.value = error.response.data.message;
                        } else {
                            errorMessage.value = '网络错误，请稍后重试';
                        }
                    } finally {
                        loading.value = false;
                    }
                };

                // 检查是否已登录
                const checkLoginStatus = async () => {
                    try {
                        const response = await axios.get('/api/satoken/current-user');
                        if (response.data.success && response.data.user) {
                            // 已登录，直接跳转
                            const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/';
                            window.location.href = redirectUrl;
                        }
                    } catch (error) {
                        // 未登录，继续显示登录页面
                        console.log('用户未登录');
                    }
                };

                // 页面加载时检查登录状态
                onMounted(() => {
                    checkLoginStatus();
                });

                    return {
                        loginForm,
                        loading,
                        errorMessage,
                        successMessage,
                        handleLogin
                    };
                }
            }).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
