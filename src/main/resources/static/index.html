<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库迁移系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="shortcut icon" href="favicon.ico">

    <link rel="stylesheet" href="libs/element-plus.css">
    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script src="js/satoken-auth.js"></script>
    <script src="js/logout-utils.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 欢迎页面样式 */
        .welcome-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .welcome-content {
            max-width: 1200px;
            width: 100%;
            text-align: center;
            color: white;
        }

        .welcome-header {
            margin-bottom: 60px;
        }

        .welcome-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .welcome-subtitle {
            font-size: 20px;
            opacity: 0.9;
            font-weight: 300;
        }

        .welcome-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .feature-card p {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .welcome-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 60px;
            flex-wrap: wrap;
        }

        .btn-primary, .btn-secondary {
            padding: 16px 32px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 160px;
            justify-content: center;
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: #667eea;
            transform: translateY(-3px);
        }

        .welcome-footer {
            opacity: 0.8;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .welcome-title {
                font-size: 36px;
            }

            .welcome-subtitle {
                font-size: 18px;
            }

            .welcome-features {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .feature-card {
                padding: 30px 20px;
            }

            .welcome-actions {
                flex-direction: column;
                align-items: center;
            }
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧菜单区域 */
        .sidebar {
            width: 250px;
            background: white;
            color: #2c3e50;
            box-shadow: 2px 0 12px rgba(0, 0, 0, 0.08);
            position: relative;
            z-index: 1000;
            border-right: 1px solid #e9ecef;
        }

        .sidebar-header {
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-header h1 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 3px;
            color: white;
        }

        .sidebar-header p {
            font-size: 13px;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.9);
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-top: 15px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            margin-right: 10px;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            color: white;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-top: 2px;
        }

        .user-actions {
            margin-left: 8px;
        }

        .user-menu-trigger {
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .user-menu-trigger:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* 退出登录区域样式 */
        .logout-section {
            margin-top: auto;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logout-btn {
            width: 100%;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #5a6c7d;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-align: left;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            color: #2c3e50;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .logout-btn .icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #5a6c7d;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            margin: 2px 8px;
            border-radius: 8px;
        }

        .menu-item:hover {
            background: #f8f9fa;
            border-left-color: #667eea;
            color: #2c3e50;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .menu-item.active {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-left-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }

        .menu-item .icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .menu-item:hover .icon,
        .menu-item.active .icon {
            opacity: 1;
        }



        .menu-section {
            margin-top: 30px;
        }

        .menu-section-title {
            padding: 8px 28px;
            font-size: 12px;
            font-weight: 600;
            color: #8892b0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 右侧主内容区域 */
        .main-content {
            flex: 1;
            background: #f5f7fa;
            overflow-y: auto;
        }

        .content-header {
            background: white;
            padding: 8px 15px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .content-title h2 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }

        .content-title p {
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 1px;
        }

        .content-body {
            padding: 10px;
        }



        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .content-body {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <!-- 欢迎页面 (未登录状态) -->
        <div v-if="!isLoggedIn" class="welcome-container">
            <div class="welcome-content">
                <div class="welcome-header">
                    <h1 class="welcome-title">🚀 数据库迁移系统</h1>
                    <p class="welcome-subtitle">智能化数据库表迁移和字段优化平台</p>
                </div>

                <div class="welcome-features">
                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <h3>智能迁移</h3>
                        <p>支持MySQL、PostgreSQL等多种数据库的智能迁移</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🤖</div>
                        <h3>AI优化</h3>
                        <p>基于AI的字段名称和表结构智能优化</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>可视化管理</h3>
                        <p>直观的迁移过程监控和历史记录管理</p>
                    </div>
                </div>

                <div class="welcome-actions">
                    <button class="btn-primary" @click="goToLogin">
                        <i class="icon">🔐</i>
                        立即登录
                    </button>
                    <button class="btn-secondary" @click="goToRegister">
                        <i class="icon">👤</i>
                        注册账号
                    </button>
                </div>

                <div class="welcome-footer">
                    <p>&copy; 2025 数据库迁移系统 - 安全可靠的数据迁移解决方案</p>
                </div>
            </div>
        </div>

        <!-- 主应用页面 (已登录状态) -->
        <div v-else class="app-container">
            <!-- 左侧菜单区域 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <h1>🚀 数据库迁移系统</h1>
                    <!--<p>智能化数据库表迁移和字段优化平台</p>-->

                    <!-- 用户信息区域 -->
                    <div class="user-info" v-if="currentUser">
                        <div class="user-avatar">
                            <span>{{ getUserInitial() }}</span>
                        </div>
                        <div class="user-details">
                            <div class="user-name">{{ currentUser.realName || currentUser.username }}</div>
                            <div class="user-role">{{ getUserRole() }}</div>
                        </div>
                        <div class="user-actions">
                            <el-dropdown @command="handleUserAction">
                                <span class="user-menu-trigger">
                                    <span style="font-size: 16px;">⋯</span>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                                        <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                </div>
                
                <div class="sidebar-menu">
                    <div class="menu-section-title">主要功能</div>
                    <!-- 主要功能 -->
                    <a href="#database-config" @click="setActiveMenu('database-config')"
                       :class="['menu-item', { active: activeMenu === 'database-config' }]">
                        <span class="icon">🔧</span>
                        <span>数据库配置</span>
                    </a>
                    
                    <a href="#migration" @click="setActiveMenu('migration')" 
                       :class="['menu-item', { active: activeMenu === 'migration' }]">
                        <span class="icon">🔄</span>
                        <span>数据迁移</span>
                    </a>
                    
                    <div class="menu-section">
                        <div class="menu-section-title">管理工具</div>
                        
                        <a href="#config-management" @click="setActiveMenu('config-management')" 
                           :class="['menu-item', { active: activeMenu === 'config-management' }]">
                            <span class="icon">🗄️</span>
                            <span>配置管理</span>
                        </a>
                        
                        <!--<a href="#field-mapping" @click="setActiveMenu('field-mapping')" -->
                        <!--   :class="['menu-item', { active: activeMenu === 'field-mapping' }]">-->
                        <!--    <span class="icon">📊</span>-->
                        <!--    <span>字段映射</span>-->
                        <!--</a>-->
                        
                        <a href="#migration-history" @click="setActiveMenu('migration-history')"
                           :class="['menu-item', { active: activeMenu === 'migration-history' }]">
                            <span class="icon">📋</span>
                            <span>迁移记录</span>
                        </a>

                        <!--<a href="#ai-optimize" @click="setActiveMenu('ai-optimize')" -->
                        <!--   :class="['menu-item', { active: activeMenu === 'ai-optimize' }]">-->
                        <!--    <span class="icon">🤖</span>-->
                        <!--    <span>AI优化</span>-->
                        <!--</a>-->
                    </div>

                    <div class="menu-section">
                        <div class="menu-section-title">系统工具</div>

                        <a href="#operation-logs" @click="setActiveMenu('operation-logs')"
                           :class="['menu-item', { active: activeMenu === 'operation-logs' }]">
                            <span class="icon">📊</span>
                            <span>操作日志</span>
                        </a>

                        <!--<a href="#logs" @click="setActiveMenu('logs')"-->
                        <!--   :class="['menu-item', { active: activeMenu === 'logs' }]">-->
                        <!--    <span class="icon">📋</span>-->
                        <!--    <span>系统日志</span>-->
                        <!--</a>-->
                        
                        <!--<a href="#settings" @click="setActiveMenu('settings')"-->
                        <!--   :class="['menu-item', { active: activeMenu === 'settings' }]">-->
                        <!--    <span class="icon">⚙️</span>-->
                        <!--    <span>系统设置</span>-->
                        <!--</a>-->
                    </div>

                    <!-- 退出登录区域 -->
                    <div class="logout-section">
                        <button class="logout-btn" @click="handleLogout" title="退出登录">
                            <span class="icon">🚪</span>
                            <span>退出登录</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右侧主内容区域 -->
            <div class="main-content">
                <div class="content-header">
                    <div class="content-title">
                        <h2 v-if="activeMenu === 'database-config'">🔧 数据库配置</h2>
                        <h2 v-else-if="activeMenu === 'migration'">🔄 数据迁移</h2>
                        <h2 v-else-if="activeMenu === 'config-management'">🗄️ 配置管理</h2>
                        <h2 v-else-if="activeMenu === 'field-mapping'">📊 字段映射</h2>
                        <h2 v-else-if="activeMenu === 'migration-history'">📋 迁移记录</h2>
                        <h2 v-else-if="activeMenu === 'operation-logs'">📊 操作日志</h2>
                        <h2 v-else-if="activeMenu === 'ai-optimize'">🤖 AI优化</h2>
                        <h2 v-else-if="activeMenu === 'logs'">📋 系统日志</h2>
                        <h2 v-else-if="activeMenu === 'settings'">⚙️ 系统设置</h2>
                        
                        <p v-if="activeMenu === 'database-config'">请配置源数据库和目标数据库连接信息</p>
                        <p v-else-if="activeMenu === 'migration'">执行数据库表迁移操作</p>
                        <p v-else-if="activeMenu === 'config-management'">集中管理源数据库和目标数据库配置信息</p>
                        <p v-else-if="activeMenu === 'field-mapping'">管理字段映射关系</p>
                        <p v-else-if="activeMenu === 'migration-history'">查看迁移历史和目标表详细信息</p>
                        <p v-else-if="activeMenu === 'operation-logs'">查看系统操作日志和审计记录</p>
                        <p v-else-if="activeMenu === 'ai-optimize'">使用AI优化数据库结构</p>
                        <p v-else-if="activeMenu === 'logs'">查看系统运行日志</p>
                        <p v-else-if="activeMenu === 'settings'">配置系统参数</p>
                    </div>
                </div>
                
                <div class="content-body">
                    <!-- 数据库配置页面 -->
                    <div v-if="activeMenu === 'database-config'">
                        <iframe src="database-config.html" 
                                style="width: 100%; height: calc(100vh - 80px); border: none; border-radius: 6px;">
                        </iframe>
                    </div>

                    <!-- 数据迁移页面 -->
                    <div v-else-if="activeMenu === 'migration'">
                        <iframe src="migration.html" 
                                style="width: 100%; height: calc(100vh - 80px); border: none; border-radius: 6px;">
                        </iframe>
                    </div>

                    <!-- 配置管理页面 -->
                    <div v-else-if="activeMenu === 'config-management'">
                        <iframe src="database-config-management.html" 
                                style="width: 100%; height: calc(100vh - 80px); border: none; border-radius: 6px;">
                        </iframe>
                    </div>

                    <!-- 迁移记录页面 -->
                    <div v-else-if="activeMenu === 'migration-history'">
                        <iframe src="migration-history.html"
                                style="width: 100%; height: calc(100vh - 80px); border: none; border-radius: 6px;">
                        </iframe>
                    </div>

                    <!-- 操作日志页面 -->
                    <div v-else-if="activeMenu === 'operation-logs'">
                        <iframe src="operation-logs-simple.html"
                                style="width: 100%; height: calc(100vh - 80px); border: none; border-radius: 6px;">
                        </iframe>
                    </div>

                    <!-- 其他功能页面 -->
                    <div v-else style="text-align: center; padding: 50px; background: white; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.1);">
                        <h3 style="color: #2c3e50; margin-bottom: 20px;">{{ getMenuTitle() }}</h3>
                        <p style="color: #7f8c8d; margin-bottom: 30px;">此功能正在开发中，敬请期待...</p>
                        <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                            <button style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 25px; border-radius: 25px; font-size: 1em; cursor: pointer; transition: transform 0.2s;" 
                                    @click="setActiveMenu('database-config')"
                                    @mouseover="$event.target.style.transform='translateY(-2px)'"
                                    @mouseout="$event.target.style.transform='translateY(0)'">
                                🔧 数据库配置
                            </button>
                            <button style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 25px; border-radius: 25px; font-size: 1em; cursor: pointer; transition: transform 0.2s;" 
                                    @click="setActiveMenu('migration')"
                                    @mouseover="$event.target.style.transform='translateY(-2px)'"
                                    @mouseout="$event.target.style.transform='translateY(0)'">
                                🔄 数据迁移
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript库 -->
    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                // 显示加载进度
                const overlay = cdnLoader.showLoadingProgress('正在加载主页面...');

                // 加载所需资源
                cdnLoader.updateProgress(20);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(40);
                await cdnLoader.loadScript('elementPlus');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                // 初始化Vue应用
                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
                // 降级到基础页面
                document.body.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
                        <div>
                            <h3>⚠️ 资源加载失败</h3>
                            <p>请检查网络连接或刷新页面重试</p>
                            <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #fff; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 刷新页面
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function initVueApp() {
            const { createApp } = Vue;

            const app = createApp({
            data() {
                return {
                    activeMenu: 'database-config',
                    currentUser: null,
                    isLoggedIn: false
                }
            },

            methods: {
                // 设置活动菜单
                setActiveMenu(menu) {
                    this.activeMenu = menu;
                },

                // 获取菜单标题
                getMenuTitle() {
                    const titles = {
                        'config-management': '🗄️ 配置管理功能',
                        'field-mapping': '📊 字段映射功能',
                        'ai-optimize': '🤖 AI优化功能',
                        'logs': '📋 系统日志功能',
                        'settings': '⚙️ 系统设置功能'
                    };
                    return titles[this.activeMenu] || '功能开发中';
                },

                // 检查登录状态
                async checkLoginStatus() {
                    console.log('🔍 主页检查登录状态开始...');
                    try {
                        const isLoggedIn = await saTokenAuthManager.checkLoginStatus();
                        if (isLoggedIn) {
                            console.log('✅ 用户已登录，显示主应用界面');
                            this.currentUser = saTokenAuthManager.getCurrentUser();
                            this.isLoggedIn = true;
                        } else {
                            console.log('❌ 用户未登录，显示欢迎页面');
                            // 显示欢迎页面，不自动跳转
                            this.isLoggedIn = false;
                        }
                    } catch (error) {
                        console.error('❌ 登录状态检查失败:', error);
                        console.log('🏠 检查失败时显示欢迎页面，不跳转到登录页');
                        // 即使检查失败也显示欢迎页面，不跳转
                        this.isLoggedIn = false;
                    }
                },

                // 导航到登录页面
                goToLogin() {
                    window.location.href = '/login.html';
                },

                // 导航到注册页面
                goToRegister() {
                    // 跳转到注册页面
                    window.location.href = '/register.html';
                },

                // 获取用户名首字母
                getUserInitial() {
                    if (!this.currentUser) return '';
                    const name = this.currentUser.realName || this.currentUser.username;
                    return name.charAt(0).toUpperCase();
                },

                // 获取用户角色
                getUserRole() {
                    if (!this.currentUser) return '';
                    return this.currentUser.role === 'ADMIN' ? '管理员' : '普通用户';
                },

                // 处理用户操作
                async handleUserAction(command) {
                    if (command === 'logout') {
                        await this.handleLogout();
                    } else if (command === 'changePassword') {
                        this.showChangePasswordDialog();
                    }
                },

                // 处理登出
                async handleLogout() {
                    // 使用优雅的确认对话框
                    window.logoutUtils.showLogoutConfirmDialog(
                        // 确认回调
                        () => {
                            window.logoutUtils.performLogout(saTokenAuthManager);
                        },
                        // 取消回调
                        () => {
                            console.log('用户取消退出登录');
                        }
                    );
                },

                // 显示修改密码对话框
                showChangePasswordDialog() {
                    // 这里可以实现修改密码的对话框
                    this.$message.info('修改密码功能开发中...');
                }
            },

            async mounted() {
                // 检查登录状态
                await this.checkLoginStatus();

                // 监听登录状态变化
                saTokenAuthManager.onLogin((user) => {
                    this.currentUser = user;
                    this.isLoggedIn = true;
                });

                saTokenAuthManager.onLogout(() => {
                    this.currentUser = null;
                    this.isLoggedIn = false;
                });

                // 监听来自iframe的消息
                window.addEventListener('message', (event) => {
                    if (!event.data) return;

                    // 处理导航请求
                    if (event.data.type === 'navigateToMigration') {
                        console.log('收到导航到迁移页面的请求');
                        this.setActiveMenu('migration');

                        // 给用户一个视觉反馈
                        setTimeout(() => {
                            console.log('已切换到数据迁移页面');
                        }, 100);
                    }

                    // 可以扩展支持其他导航类型
                    else if (event.data.type === 'navigate') {
                        const targetMenu = event.data.menu;
                        if (targetMenu) {
                            console.log(`收到导航请求: ${targetMenu}`);
                            this.setActiveMenu(targetMenu);
                        }
                    }
                });
            }
        });

        // 注册 Element Plus
        if (typeof ElementPlus !== 'undefined') {
            app.use(ElementPlus);
            console.log('✅ Element Plus 已注册');
        } else {
            console.warn('⚠️ Element Plus 未加载，组件可能无法正常工作');
        }

        // 挂载应用
        app.mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>