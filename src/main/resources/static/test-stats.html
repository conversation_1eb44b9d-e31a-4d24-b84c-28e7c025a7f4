<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .debug {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 操作日志统计测试</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalLogs">-</div>
                <div class="stat-label">总日志数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayLogs">-</div>
                <div class="stat-label">今日日志</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="errorLogs">-</div>
                <div class="stat-label">错误日志</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">-</div>
                <div class="stat-label">活跃用户</div>
            </div>
        </div>

        <button class="btn" onclick="loadStatistics()">加载统计数据</button>
        <button class="btn" onclick="testAPI()">测试API</button>
        <button class="btn" onclick="clearDebug()">清除调试信息</button>

        <div class="debug" id="debugInfo">点击"加载统计数据"按钮开始测试...</div>
    </div>

    <script>
        const TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoidXVoTTdxYkpQSElrdHJDQmVmWWZibWo2U0J3dFE3UUoifQ.5e8hBsk5i_h_Ay_fYQJD19TP1Swsw1llr9M9LMfvjOQ';

        function log(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
        }

        function clearDebug() {
            document.getElementById('debugInfo').textContent = '';
        }

        async function testAPI() {
            log('开始测试API连接...');
            
            try {
                const response = await fetch('/api/operation-logs/statistics', {
                    headers: {
                        'DB_COPY_TOKEN': TOKEN
                    }
                });

                log(`API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
                } else {
                    const errorText = await response.text();
                    log(`API错误响应: ${errorText}`);
                }
            } catch (error) {
                log(`API请求失败: ${error.message}`);
            }
        }

        async function loadStatistics() {
            log('开始加载统计数据...');
            
            try {
                const response = await fetch('/api/operation-logs/statistics', {
                    headers: {
                        'DB_COPY_TOKEN': TOKEN
                    }
                });

                log(`统计API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`统计API响应: ${JSON.stringify(result, null, 2)}`);
                    
                    if (result.success) {
                        updateStatistics(result.data);
                        log('统计数据更新成功');
                    } else {
                        log(`统计API返回错误: ${result.message}`);
                    }
                } else {
                    const errorText = await response.text();
                    log(`统计API HTTP错误: ${errorText}`);
                }
            } catch (error) {
                log(`统计API请求异常: ${error.message}`);
            }
        }

        function updateStatistics(data) {
            log('开始更新统计显示...');
            
            // 计算今日日志数量
            const today = new Date().toISOString().split('T')[0];
            log(`今日日期: ${today}`);
            
            let todayLogs = 0;
            if (data.dailyOperationStats && Array.isArray(data.dailyOperationStats)) {
                log(`每日统计数据: ${JSON.stringify(data.dailyOperationStats)}`);
                const todayStat = data.dailyOperationStats.find(stat => {
                    const statDate = stat.date;
                    log(`比较日期: ${statDate} vs ${today}`);
                    return statDate && statDate.toString().startsWith(today);
                });
                todayLogs = todayStat ? (parseInt(todayStat.count) || 0) : 0;
                log(`今日日志数量: ${todayLogs}`);
            }
            
            // 计算错误日志数量
            const errorLogs = data.errorStats?.error_count || 0;
            log(`错误日志数量: ${errorLogs}`);
            
            // 计算活跃用户数量
            const activeUsers = data.userOperationStats?.length || 0;
            log(`活跃用户数量: ${activeUsers}`);
            
            // 计算总日志数量
            let totalLogs = 0;
            if (data.dailyOperationStats && Array.isArray(data.dailyOperationStats)) {
                totalLogs = data.dailyOperationStats.reduce((sum, stat) => 
                    sum + (parseInt(stat.count) || 0), 0
                );
            }
            log(`总日志数量: ${totalLogs}`);

            // 更新页面显示
            document.getElementById('totalLogs').textContent = totalLogs.toLocaleString();
            document.getElementById('todayLogs').textContent = todayLogs.toLocaleString();
            document.getElementById('errorLogs').textContent = errorLogs.toLocaleString();
            document.getElementById('activeUsers').textContent = activeUsers.toLocaleString();
            
            log('统计显示更新完成');
        }

        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试...');
            loadStatistics();
        });
    </script>
</body>
</html>
