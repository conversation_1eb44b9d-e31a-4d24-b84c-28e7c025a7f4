<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 数据库迁移系统</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 450px;
            max-width: 90vw;
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .register-subtitle {
            color: #7f8c8d;
            font-size: 14px;
        }

        .register-form {
            margin-top: 30px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-item label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .register-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .register-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
            padding: 10px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
            padding: 10px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .back-to-home {
            text-align: center;
            margin-top: 20px;
        }

        .back-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 0 8px;
        }

        .back-link:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 12px;
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 30px 20px;
            }
            
            .register-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div id="app" style="display: none;">
        <div class="register-container">
            <div class="register-header">
                <h1 class="register-title">👤 用户注册</h1>
                <p class="register-subtitle">数据库迁移系统</p>
            </div>

            <form class="register-form" @submit.prevent="handleRegister">
                <div class="form-item">
                    <label for="username">用户名 *</label>
                    <input 
                        id="username"
                        type="text" 
                        class="form-input"
                        v-model="registerForm.username"
                        placeholder="请输入用户名"
                        :disabled="loading"
                        required
                    >
                </div>

                <div class="form-item">
                    <label for="realName">真实姓名 *</label>
                    <input 
                        id="realName"
                        type="text" 
                        class="form-input"
                        v-model="registerForm.realName"
                        placeholder="请输入真实姓名"
                        :disabled="loading"
                        required
                    >
                </div>

                <div class="form-item">
                    <label for="invitationCode">邀请码 *</label>
                    <input
                        id="invitationCode"
                        type="text"
                        class="form-input"
                        v-model="registerForm.invitationCode"
                        placeholder="请输入邀请码"
                        :disabled="loading"
                        required
                    >
                </div>

                <div class="form-item">
                    <label for="password">密码 *</label>
                    <input 
                        id="password"
                        type="password" 
                        class="form-input"
                        v-model="registerForm.password"
                        placeholder="请输入密码"
                        :disabled="loading"
                        required
                    >
                </div>

                <div class="form-item">
                    <label for="confirmPassword">确认密码 *</label>
                    <input 
                        id="confirmPassword"
                        type="password" 
                        class="form-input"
                        v-model="registerForm.confirmPassword"
                        placeholder="请再次输入密码"
                        :disabled="loading"
                        required
                    >
                </div>

                <button 
                    type="submit" 
                    class="register-button"
                    :disabled="loading || !isFormValid"
                >
                    <span v-if="loading" class="loading-spinner"></span>
                    {{ loading ? '注册中...' : '立即注册' }}
                </button>

                <div v-if="errorMessage" class="error-message">
                    {{ errorMessage }}
                </div>

                <div v-if="successMessage" class="success-message">
                    {{ successMessage }}
                </div>
            </form>

            <div class="back-to-home">
                <a href="/" class="back-link">🏠 返回首页</a>
                <a href="/login.html" class="back-link">🔐 立即登录</a>
            </div>

            <div class="footer">
                <p>&copy; 2025 数据库迁移系统 - 安全可靠的数据迁移解决方案</p>
            </div>
        </div>
    </div>

    <!-- 智能CDN加载器 -->
    <script src="js/cdn-loader.js"></script>
    <script>
        // 智能加载页面资源
        async function initializePage() {
            try {
                const overlay = cdnLoader.showLoadingProgress('正在加载注册页面...');

                cdnLoader.updateProgress(30);
                await cdnLoader.loadScript('vue');

                cdnLoader.updateProgress(70);
                await cdnLoader.loadScript('axios');

                cdnLoader.updateProgress(100);

                setTimeout(() => {
                    initVueApp();
                    cdnLoader.hideLoadingProgress();
                    document.getElementById('app').style.display = 'block';
                }, 300);

            } catch (error) {
                console.error('❌ 页面资源加载失败:', error);
                document.body.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
                        <div>
                            <h3>⚠️ 资源加载失败</h3>
                            <p>请检查网络连接或刷新页面重试</p>
                            <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #fff; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 刷新页面
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        function initVueApp() {
            const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const registerForm = ref({
                    username: '',
                    realName: '',
                    invitationCode: '',
                    password: '',
                    confirmPassword: ''
                });
                
                const loading = ref(false);
                const errorMessage = ref('');
                const successMessage = ref('');

                // 表单验证
                const isFormValid = computed(() => {
                    return registerForm.value.username && 
                           registerForm.value.realName && 
                           registerForm.value.password && 
                           registerForm.value.confirmPassword &&
                           registerForm.value.password === registerForm.value.confirmPassword;
                });

                // 处理注册
                const handleRegister = async () => {
                    if (!isFormValid.value) {
                        errorMessage.value = '请填写所有必填项，并确保两次密码输入一致';
                        return;
                    }

                    if (registerForm.value.password.length < 6) {
                        errorMessage.value = '密码长度至少6位';
                        return;
                    }

                    loading.value = true;
                    errorMessage.value = '';
                    successMessage.value = '';

                    try {
                        const response = await axios.post('/api/satoken/register', {
                            username: registerForm.value.username,
                            realName: registerForm.value.realName,
                            invitationCode: registerForm.value.invitationCode,
                            password: registerForm.value.password
                        });

                        if (response.data.success) {
                            successMessage.value = '注册成功！正在跳转到登录页面...';
                            
                            // 延迟跳转到登录页面
                            setTimeout(() => {
                                window.location.href = '/login.html';
                            }, 2000);
                        } else {
                            errorMessage.value = response.data.message || '注册失败';
                        }
                    } catch (error) {
                        console.error('注册错误:', error);
                        if (error.response && error.response.data && error.response.data.message) {
                            errorMessage.value = error.response.data.message;
                        } else {
                            errorMessage.value = '网络错误，请稍后重试';
                        }
                    } finally {
                        loading.value = false;
                    }
                };

                return {
                    registerForm,
                    loading,
                    errorMessage,
                    successMessage,
                    isFormValid,
                    handleRegister
                };
            }
        }).mount('#app');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
