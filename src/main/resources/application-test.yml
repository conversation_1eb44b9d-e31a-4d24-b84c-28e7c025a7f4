# 测试环境配置
spring:
  application:
    name: DB_CopyData-Test
  
  # 数据源配置 - 测试环境 (阿里云RDS)
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************
    username: healthcar_test
    password: l$!d#!C07LVj%b
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 25000
      idle-timeout: 450000
      max-lifetime: 1500000
      leak-detection-threshold: 60000

# 服务器配置
server:
  port: 9999
  tomcat:
    max-threads: 100
    min-spare-threads: 5
    connection-timeout: 20000

# 应用自定义配置
app:
  # 配置管理数据库连接配置 (阿里云RDS)
  config:
    database:
      host: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com
      port: 3306
      username: healthcar_test
      password: l$!d#!C07LVj%b

  # 表复制配置 - 测试环境
  copy:
    batch-size: 1500          # 测试环境使用中等批次
    max-connections: 15       # 测试环境使用适中的连接数
    timeout: 45               # 测试环境超时时间

  # AI配置
  ai:
    field-naming:
      enabled: true
      model: qwen-plus
      timeout: 60s

  # 大数据迁移配置 - 测试环境优化
  migration:
    big-data:
      large-data-threshold: 50000       # 测试环境降低阈值
      small-batch-size: 1500            # 测试环境批次
      medium-batch-size: 3000
      large-batch-size: 5000
      xlarge-batch-size: 7000
      min-threads: 1
      max-threads: 6                    # 测试环境限制线程数
      connection-pool-size: 8
      query-timeout-seconds: 240        # 4分钟
      batch-timeout-seconds: 480        # 8分钟
      progress-report-interval: 8000
      memory-threshold-mb: 512          # 测试环境内存限制
      enable-memory-monitoring: true
      enable-performance-stats: true
      max-retry-attempts: 3
      retry-interval-ms: 3000

# 数据库优化配置 - 测试环境
database:
  optimization:
    mysql:
      innodb-lock-wait-timeout: 240     # 测试环境稍短
      bulk-insert-buffer-size: 4194304  # 4MB
      max-allowed-packet: 33554432      # 32MB
    postgresql:
      statement-timeout: 240000         # 4分钟
      lock-timeout: 240000
      work-mem: 2048                    # 2MB

# 日志配置 - 测试环境
logging:
  level:
    com.liuyang: DEBUG                  # 测试环境开启DEBUG
    org.springframework.ai: DEBUG
    org.springframework.jdbc: INFO
    org.springframework.web: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/test-application.log
    max-size: 20MB
    max-history: 30

# 管理端点配置 - 测试环境开放更多端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,configprops,beans
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: false                      # 测试环境关闭安全限制
