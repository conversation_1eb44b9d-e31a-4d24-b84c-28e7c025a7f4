# 生产环境配置
spring:
  application:
    name: DB_CopyData-Prod

  # 数据源配置 - 生产环境 (阿里云RDS)
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************
    username: healthcar_test
    password: l$!d#!C07LVj%b
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 1200000
      leak-detection-threshold: 60000
      validation-timeout: 5000
      initialization-fail-timeout: 1

# 服务器配置 - 生产环境
server:
  port: 9999
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    connection-timeout: 20000

# 应用自定义配置 - 生产环境
app:
  # 配置管理数据库连接配置 (阿里云RDS)
  config:
    database:
      host: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com
      port: 3306
      username: healthcar_test
      password: l$!d#!C07LVj%b

  # 表复制配置 - 生产环境
  copy:
    batch-size: 2000          # 生产环境使用较大的批次
    max-connections: 20       # 生产环境使用更多的连接
    timeout: 60               # 生产环境使用更长的超时时间

  # 数据迁移配置 - 生产环境
  migration:
    big-data:
      large-data-threshold: 100000
      small-batch-size: 3000
      medium-batch-size: 8000
      large-batch-size: 12000
      xlarge-batch-size: 15000
      max-threads: 16
      query-timeout-seconds: 600
      batch-timeout-seconds: 1200
    high-performance:
      # 分片配置
      shard-size: 200000
      max-parallel-threads: 32
      min-parallel-threads: 4
      # 批次配置
      max-batch-size: 100000
      min-batch-size: 10000
      optimal-batch-size: 50000
      # 连接池配置
      connection-pool-size: 50
      connection-timeout: 120000
      socket-timeout: 600000
      idle-timeout: 600000
      max-lifetime: 3600000
      # 监控配置
      progress-report-interval: 30
      enable-detailed-logging: false
      enable-performance-metrics: true
      # 优化配置
      enable-batch-rewrite: true
      enable-prepared-statement-cache: true
      enable-connection-optimization: true
      enable-database-specific-optimization: true
      # 内存配置
      fetch-size: 50000
      result-set-cache-size: 5000
      # 错误处理配置
      max-retry-attempts: 5
      retry-delay-ms: 3000
      fail-fast-on-error: false
      # 性能阈值配置
      large-dataset-threshold: 5000000
      huge-dataset-threshold: 50000000
      min-acceptable-speed: 5000.0

# 日志配置 - 生产环境（1Panel优化）
logging:
  level:
    com.liuyang: ${LOG_LEVEL:INFO}
    org.springframework.jdbc: INFO
    org.springframework.web: INFO
    org.springframework.websocket: DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  # 1Panel环境下同时输出到控制台和文件
  file:
    name: /app/logs/application.log
    max-size: 50MB
    max-history: 30
  logback:
    rollingpolicy:
      max-file-size: 50MB
      total-size-cap: 500MB

# 管理端点配置 - 生产环境（限制端点访问）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  security:
    enabled: true
# Sa-Token配置 - 开发环境
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: DB_COPY_TOKEN
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 86400
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # JWT秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz1234567890
