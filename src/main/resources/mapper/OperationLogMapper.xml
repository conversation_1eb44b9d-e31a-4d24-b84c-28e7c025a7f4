<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.liuyang.mapper.OperationLogMapper">

    <!-- 分页查询操作日志 -->
    <select id="selectByPage" resultType="com.liuyang.entity.OperationLog">
        SELECT * FROM operation_logs
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="operationType != null and operationType != ''">
            AND operation_type = #{operationType}
        </if>
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        ORDER BY created_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计操作日志总数 -->
    <select id="countByCondition" resultType="long">
        SELECT COUNT(*) FROM operation_logs
        WHERE 1=1
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="operationType != null and operationType != ''">
            AND operation_type = #{operationType}
        </if>
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询操作类型统计 -->
    <select id="selectOperationTypeStats" resultType="map">
        SELECT
            operation_type as operationType,
            COUNT(*) as count
        FROM operation_logs
        WHERE created_time >= #{startTime} AND created_time &lt;= #{endTime}
        GROUP BY operation_type
        ORDER BY count DESC
    </select>

    <!-- 查询用户操作统计 -->
    <select id="selectUserOperationStats" resultType="map">
        SELECT
            username,
            COUNT(*) as count
        FROM operation_logs
        WHERE created_time >= #{startTime} AND created_time &lt;= #{endTime}
        GROUP BY username
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 查询每日操作统计 -->
    <select id="selectDailyOperationStats" resultType="map">
        SELECT
            DATE(created_time) as date,
            COUNT(*) as count
        FROM operation_logs
        WHERE created_time >= #{startTime} AND created_time &lt;= #{endTime}
        GROUP BY DATE(created_time)
        ORDER BY date DESC
    </select>

    <!-- 查询错误统计 -->
    <select id="selectErrorStats" resultType="map">
        SELECT
            COUNT(*) as error_count,
            COUNT(DISTINCT user_id) as affected_users
        FROM operation_logs
        WHERE response_status >= 400
        AND created_time >= #{startTime} AND created_time &lt;= #{endTime}
    </select>

</mapper>
