# Spring Boot 应用配置 - 共同配置
spring:
  application:
    name: DB_CopyData
  profiles:
    active: dev
  # AI配置 - 共同配置
  ai:
    dashscope:
      # 阿里云千问API密钥
      api-key: sk-02ef5834f1524ef69340d5c3f5957f40
      chat:
        options:
          model: qwen-plus
          temperature: 0.8

  # Web 配置 - 共同配置
  web:
    resources:
      static-locations: classpath:/static/
      add-mappings: true

# 应用自定义配置 - 共同配置
app:
  # AI配置
  ai:
    field-naming:
      enabled: true
      model: qwen-plus
      timeout: 60s

  # 数据迁移配置 - 共同配置
  migration:
    big-data:
      # 大数据量阈值（超过此数量使用优化策略）
      large-data-threshold: 100000

      # 批次大小配置
      small-batch-size: 2000      # 10万以下
      medium-batch-size: 5000     # 100万以下
      large-batch-size: 8000      # 500万以下
      xlarge-batch-size: 10000    # 500万以上

      # 线程配置
      min-threads: 1
      max-threads: 8

      # 连接池配置
      connection-pool-size: 10

      # 超时配置（秒）
      query-timeout-seconds: 300    # 5分钟
      batch-timeout-seconds: 600    # 10分钟

      # 监控配置
      progress-report-interval: 10000  # 进度报告间隔（毫秒）
      memory-threshold-mb: 1024        # 内存监控阈值（MB）
      enable-memory-monitoring: true   # 启用内存监控
      enable-performance-stats: true   # 启用性能统计

      # 错误处理配置
      max-retry-attempts: 3         # 最大重试次数
      retry-interval-ms: 5000       # 重试间隔（毫秒）

# 数据库优化配置 - 共同配置
database:
  optimization:
    # MySQL优化参数
    mysql:
      innodb-lock-wait-timeout: 300
      bulk-insert-buffer-size: 8388608  # 8MB
      max-allowed-packet: 67108864      # 64MB

    # PostgreSQL优化参数
    postgresql:
      statement-timeout: 300000         # 5分钟
      lock-timeout: 300000              # 5分钟
      work-mem: 4096                    # 4MB

# MyBatis配置 - 共同配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-fetch-size: 100
    default-statement-timeout: 30
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.liuyang.entity
