-- =====================================================
-- 添加AI提示词描述字段到迁移记录表
-- 创建时间：2025-01-16
-- 说明：在table_mapping_records表中增加ai_prompt_description字段
--       用于保存AI优化表名时使用的提示词，作为表描述信息
-- =====================================================

-- 1. 为table_mapping_records表添加AI提示词描述字段
ALTER TABLE `table_mapping_records` 
ADD COLUMN `ai_prompt_description` TEXT COMMENT 'AI优化表名时使用的提示词描述' AFTER `target_table_comment`;

-- 2. 添加索引以提高查询性能（可选）
-- ALTER TABLE `table_mapping_records` 
-- ADD INDEX `idx_ai_prompt_description` (`ai_prompt_description`(100));

-- 3. 验证字段添加是否成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'table_mapping_records' 
    AND COLUMN_NAME = 'ai_prompt_description'
    AND TABLE_SCHEMA = DATABASE();

-- 4. 查看表结构确认
DESCRIBE `table_mapping_records`;

-- =====================================================
-- 使用说明：
-- 1. ai_prompt_description字段用于存储AI优化表名时的完整提示词
-- 2. 该字段内容将作为MySQL表的COMMENT使用
-- 3. 字段类型为TEXT，支持存储较长的提示词内容
-- 4. 该字段允许为NULL，兼容历史数据
-- =====================================================
