-- 更新用户密码
-- 这个脚本用于修复用户密码哈希问题

-- 更新admin用户密码 (admin123)
UPDATE `users` 
SET `password` = '$2a$10$7hNtr8kUc29J1JPil3xn0.3UU9lfjvnHkpx8ox2kJU/mZFIdtIkae',
    `updated_time` = CURRENT_TIMESTAMP
WHERE `username` = 'admin';

-- 更新testuser用户密码 (user123)
UPDATE `users` 
SET `password` = '$2a$10$cxgsw4rDG3Eul1yrhijJ7e12SFBYGGDPNfqiGG1hjSpcb6f2P90yy',
    `updated_time` = CURRENT_TIMESTAMP
WHERE `username` = 'testuser';

-- 验证更新结果
SELECT username, real_name, role, status, created_time, updated_time 
FROM users 
WHERE username IN ('admin', 'testuser');
