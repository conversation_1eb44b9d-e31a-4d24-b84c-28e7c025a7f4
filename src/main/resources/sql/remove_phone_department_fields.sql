-- =====================================================
-- 数据库迁移脚本：删除用户表中的phone和department字段
-- 创建时间：2025-01-12
-- 说明：由于注册功能不再需要手机号和部门信息，删除这两个字段
-- =====================================================

-- 1. 检查当前表结构
-- 执行前请先查看当前表结构，确认phone和department字段存在
-- DESCRIBE users;

-- 2. 备份数据（可选，建议在生产环境执行）
-- CREATE TABLE users_backup_phone_dept_20250112 AS SELECT * FROM users;

-- 3. 检查phone和department字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'users' 
    AND COLUMN_NAME IN ('phone', 'department');

-- 4. 删除phone字段
-- 注意：此操作不可逆，请确保已备份数据
ALTER TABLE `users` DROP COLUMN `phone`;

-- 5. 删除department字段
-- 注意：此操作不可逆，请确保已备份数据
ALTER TABLE `users` DROP COLUMN `department`;

-- 6. 验证字段已删除
-- 执行后检查表结构，确认phone和department字段已被删除
-- DESCRIBE users;

-- 7. 如果需要回滚，可以重新添加字段（仅结构，数据会丢失）
-- ALTER TABLE `users` ADD COLUMN `phone` VARCHAR(20) COMMENT '手机号' AFTER `real_name`;
-- ALTER TABLE `users` ADD COLUMN `department` VARCHAR(100) COMMENT '部门' AFTER `phone`;

-- =====================================================
-- 执行说明：
-- 1. 建议在测试环境先执行验证
-- 2. 生产环境执行前请备份数据
-- 3. 确认应用程序已更新，不再使用phone和department字段
-- 4. 执行后验证应用程序功能正常
-- =====================================================
