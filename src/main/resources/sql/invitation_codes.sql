-- =====================================================
-- 邀请码管理表结构
-- 创建时间：2025-01-12
-- 说明：用于管理用户注册邀请码，支持加密存储和使用统计
-- =====================================================

-- 1. 创建邀请码表
CREATE TABLE IF NOT EXISTS `invitation_codes` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '邀请码ID',
    `code_hash` VARCHAR(255) NOT NULL UNIQUE COMMENT '邀请码哈希值(BCrypt加密)',
    `description` VARCHAR(255) COMMENT '邀请码描述',
    `is_active` TINYINT NOT NULL DEFAULT 1 COMMENT '是否激活: 1-激活, 0-禁用',
    `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    `max_usage` INT NOT NULL DEFAULT -1 COMMENT '最大使用次数(-1表示无限制)',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    INDEX `idx_code_hash` (`code_hash`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请码管理表';

-- 2. 插入默认邀请码 "healthcar" (BCrypt加密后的值)
-- 注意：这里使用的是 "healthcar" 经过 BCrypt 加密后的哈希值
-- BCrypt 每次加密结果不同，所以这里使用一个示例值，实际应用中会通过代码生成
INSERT INTO `invitation_codes` (`code_hash`, `description`, `is_active`, `max_usage`, `created_by`)
VALUES ('$2a$10$placeholder_will_be_replaced_by_code', '默认邀请码 - healthcar', 1, -1, 'system')
ON DUPLICATE KEY UPDATE 
    `description` = '默认邀请码 - healthcar',
    `is_active` = 1,
    `updated_time` = CURRENT_TIMESTAMP,
    `updated_by` = 'system';

-- 3. 创建邀请码使用记录表（可选，用于详细追踪）
CREATE TABLE IF NOT EXISTS `invitation_code_usage` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '使用记录ID',
    `invitation_code_id` BIGINT NOT NULL COMMENT '邀请码ID',
    `user_id` BIGINT COMMENT '使用的用户ID',
    `username` VARCHAR(50) COMMENT '使用的用户名',
    `usage_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    `ip_address` VARCHAR(50) COMMENT '使用时的IP地址',
    INDEX `idx_invitation_code_id` (`invitation_code_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_usage_time` (`usage_time`),
    FOREIGN KEY (`invitation_code_id`) REFERENCES `invitation_codes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请码使用记录表';

-- =====================================================
-- 使用说明：
-- 1. invitation_codes 表存储所有可用的邀请码
-- 2. code_hash 字段存储 BCrypt 加密后的邀请码
-- 3. is_active 控制邀请码是否可用
-- 4. max_usage 控制最大使用次数，-1 表示无限制
-- 5. invitation_code_usage 表记录每次使用详情（可选）
-- =====================================================
