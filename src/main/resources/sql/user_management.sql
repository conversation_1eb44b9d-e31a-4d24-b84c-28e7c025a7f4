-- 用户管理相关表结构

-- 1. 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码(加密后)',
    `real_name` VARCHAR(100) NOT NULL COMMENT '真实姓名',
    `role` VARCHAR(20) NOT NULL DEFAULT 'USER' COMMENT '角色: ADMIN, USER',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) COMMENT '最后登录IP',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    INDEX `idx_username` (`username`),
    INDEX `idx_status` (`status`),
    INDEX `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 插入默认管理员用户 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `real_name`, `role`, `created_by`)
VALUES ('admin', '$2a$10$7hNtr8kUc29J1JPil3xn0.3UU9lfjvnHkpx8ox2kJU/mZFIdtIkae', '系统管理员', 'ADMIN', 'system')
ON DUPLICATE KEY UPDATE `password` = '$2a$10$7hNtr8kUc29J1JPil3xn0.3UU9lfjvnHkpx8ox2kJU/mZFIdtIkae', `updated_time` = CURRENT_TIMESTAMP;

-- 3. 插入测试用户 (密码: user123)
INSERT INTO `users` (`username`, `password`, `real_name`, `role`, `created_by`)
VALUES ('testuser', '$2a$10$cxgsw4rDG3Eul1yrhijJ7e12SFBYGGDPNfqiGG1hjSpcb6f2P90yy', '测试用户', 'USER', 'admin')
ON DUPLICATE KEY UPDATE `password` = '$2a$10$cxgsw4rDG3Eul1yrhijJ7e12SFBYGGDPNfqiGG1hjSpcb6f2P90yy', `updated_time` = CURRENT_TIMESTAMP;

-- 4. 修改迁移记录表，增加操作人字段
ALTER TABLE `migration_records` 
ADD COLUMN `operator_id` BIGINT COMMENT '操作人ID' AFTER `migration_batch_id`,
ADD COLUMN `operator_name` VARCHAR(100) COMMENT '操作人姓名' AFTER `operator_id`,
ADD INDEX `idx_operator_id` (`operator_id`);

-- 5. 修改表映射记录表，增加操作人字段
ALTER TABLE `table_mapping_records` 
ADD COLUMN `operator_id` BIGINT COMMENT '操作人ID' AFTER `migration_batch_id`,
ADD COLUMN `operator_name` VARCHAR(100) COMMENT '操作人姓名' AFTER `operator_id`,
ADD INDEX `idx_operator_id` (`operator_id`);

-- 6. 修改字段映射记录表，增加操作人字段
ALTER TABLE `field_mapping_records` 
ADD COLUMN `operator_id` BIGINT COMMENT '操作人ID' AFTER `table_mapping_record_id`,
ADD COLUMN `operator_name` VARCHAR(100) COMMENT '操作人姓名' AFTER `operator_id`,
ADD INDEX `idx_operator_id` (`operator_id`);

-- 7. 用户会话表已删除，改用JWT + Sa-Token无状态认证

-- 8. 创建操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    `user_id` BIGINT COMMENT '操作用户ID',
    `username` VARCHAR(50) COMMENT '操作用户名',
    `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `operation_desc` VARCHAR(500) COMMENT '操作描述',
    `target_type` VARCHAR(50) COMMENT '目标类型',
    `target_id` VARCHAR(100) COMMENT '目标ID',
    `request_method` VARCHAR(10) COMMENT '请求方法',
    `request_url` VARCHAR(500) COMMENT '请求URL',
    `request_params` TEXT COMMENT '请求参数',
    `response_status` INT COMMENT '响应状态码',
    `error_message` TEXT COMMENT '错误信息',
    `execution_time` BIGINT COMMENT '执行时间(毫秒)',
    `client_ip` VARCHAR(50) COMMENT '客户端IP',
    `user_agent` TEXT COMMENT '用户代理',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
