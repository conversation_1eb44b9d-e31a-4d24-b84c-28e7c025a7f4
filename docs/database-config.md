# 数据库配置说明

## 🗄️ 环境配置

### 开发环境 (dev)
- **数据库**: localhost:3306
- **用户名**: root
- **密码**: liuyang
- **端口**: 9999
- **配置文件**: `application.yml`

### 测试环境 (test)
- **数据库**: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com:3306
- **用户名**: healthcar_test
- **密码**: l$!d#!C07LVj%b
- **端口**: 9999
- **配置文件**: `application-test.yml`

### 生产环境 (prod)
- **数据库**: rm-2zebik7o7a04ddnvowo.mysql.rds.aliyuncs.com:3306
- **用户名**: healthcar_test
- **密码**: l$!d#!C07LVj%b
- **端口**: 8080
- **配置文件**: `application-prod.yml`

## 🚀 启动方式

### 方式一：使用脚本启动
```bash
# 开发环境
./scripts/start-dev.sh

# 测试环境
./scripts/start-test.sh

# 生产环境
./scripts/start-prod.sh
```

### 方式二：Maven命令启动
```bash
# 开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 测试环境
mvn spring-boot:run -Dspring-boot.run.profiles=test

# 生产环境
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

### 方式三：JAR包启动
```bash
# 打包
mvn clean package -DskipTests

# 开发环境
java -jar target/DB_CopyData-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev

# 测试环境
java -jar target/DB_CopyData-0.0.1-SNAPSHOT.jar --spring.profiles.active=test

# 生产环境
java -jar target/DB_CopyData-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

## 🔧 连接参数优化

### 阿里云RDS连接优化
所有阿里云RDS连接都包含以下优化参数：
- `useSSL=true` - 启用SSL加密
- `connectTimeout=120000` - 连接超时2分钟
- `socketTimeout=300000` - Socket超时5分钟
- `autoReconnect=true` - 自动重连
- `failOverReadOnly=false` - 故障转移不只读
- `maxReconnects=3` - 最大重连3次

### 连接池配置

| 环境 | 最大连接数 | 最小空闲 | 连接超时 | 空闲超时 | 最大生命周期 |
|------|------------|----------|----------|----------|--------------|
| 开发 | 10         | 2        | 20s      | 5min     | 20min        |
| 测试 | 15         | 3        | 25s      | 7.5min   | 25min        |
| 生产 | 20         | 5        | 30s      | 10min    | 30min        |

## 📊 大数据迁移配置差异

### 测试环境优化
- 大数据阈值: 50,000 (降低)
- 最大线程数: 6 (限制)
- 内存阈值: 512MB (限制)
- 超时时间: 4分钟 (缩短)

### 生产环境优化
- 大数据阈值: 100,000 (标准)
- 最大线程数: 8 (完整)
- 内存阈值: 1024MB (标准)
- 超时时间: 5分钟 (标准)

## 🔐 安全配置

### 密码安全
- 生产和测试环境使用复杂密码: `l$!d#!C07LVj%b`
- 包含特殊字符，需要在某些环境中转义

### 网络安全
- 阿里云RDS启用SSL加密
- 生产环境限制管理端点访问
- 测试环境开放更多调试端点

## 📝 日志配置

### 开发环境
- 日志级别: DEBUG
- 输出: 控制台
- AI日志: 启用

### 测试环境
- 日志级别: DEBUG
- 输出: 文件 + 控制台
- 文件: `/var/log/db-copy-data/test-application.log`
- 保留: 30天, 20MB/文件

### 生产环境
- 日志级别: INFO/WARN
- 输出: 文件
- 文件: `/var/log/db-copy-data/application.log`
- 保留: 90天, 50MB/文件, 总计1GB

## 🔍 故障排查

### 连接问题
1. 检查网络连通性
2. 验证用户名密码
3. 确认数据库服务状态
4. 检查防火墙设置

### 性能问题
1. 监控连接池使用情况
2. 检查慢查询日志
3. 调整批次大小和线程数
4. 监控内存使用

### 配置验证
```bash
# 检查当前环境
curl http://localhost:端口/actuator/env | grep spring.profiles.active

# 检查数据源配置
curl http://localhost:端口/actuator/configprops | grep datasource
```
